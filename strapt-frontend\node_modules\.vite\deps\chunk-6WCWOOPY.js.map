{"version": 3, "sources": ["../../abitype/src/version.ts", "../../abitype/src/errors.ts", "../../abitype/src/human-readable/errors/abiItem.ts", "../../abitype/src/human-readable/errors/abiParameter.ts", "../../abitype/src/human-readable/errors/signature.ts", "../../abitype/src/human-readable/errors/struct.ts", "../../abitype/src/human-readable/errors/splitParameters.ts", "../../abitype/src/regex.ts", "../../abitype/src/human-readable/runtime/signatures.ts", "../../abitype/src/human-readable/runtime/cache.ts", "../../abitype/src/human-readable/runtime/utils.ts", "../../abitype/src/human-readable/runtime/structs.ts", "../../abitype/src/human-readable/parseAbi.ts", "../../abitype/src/human-readable/parseAbiItem.ts", "../../abitype/src/human-readable/parseAbiParameter.ts", "../../abitype/src/human-readable/parseAbiParameters.ts", "../../abitype/src/human-readable/formatAbiParameter.ts", "../../abitype/src/human-readable/formatAbiParameters.ts", "../../abitype/src/human-readable/formatAbiItem.ts"], "sourcesContent": ["export const version = '1.0.8'\n", "import type { <PERSON><PERSON><PERSON>, <PERSON> } from './types.js'\nimport { version } from './version.js'\n\ntype BaseErrorArgs = Pretty<\n  {\n    docsPath?: string | undefined\n    metaMessages?: string[] | undefined\n  } & OneOf<{ details?: string | undefined } | { cause?: BaseError | Error }>\n>\n\nexport class BaseError extends Error {\n  details: string\n  docsPath?: string | undefined\n  metaMessages?: string[] | undefined\n  shortMessage: string\n\n  override name = 'AbiTypeError'\n\n  constructor(shortMessage: string, args: BaseErrorArgs = {}) {\n    const details =\n      args.cause instanceof BaseError\n        ? args.cause.details\n        : args.cause?.message\n          ? args.cause.message\n          : args.details!\n    const docsPath =\n      args.cause instanceof BaseError\n        ? args.cause.docsPath || args.docsPath\n        : args.docsPath\n    const message = [\n      shortMessage || 'An error occurred.',\n      '',\n      ...(args.metaMessages ? [...args.metaMessages, ''] : []),\n      ...(docsPath ? [`Docs: https://abitype.dev${docsPath}`] : []),\n      ...(details ? [`Details: ${details}`] : []),\n      `Version: abitype@${version}`,\n    ].join('\\n')\n\n    super(message)\n\n    if (args.cause) this.cause = args.cause\n    this.details = details\n    this.docsPath = docsPath\n    this.metaMessages = args.metaMessages\n    this.shortMessage = shortMessage\n  }\n}\n", "import { BaseError } from '../../errors.js'\n\nexport class InvalidAbiItemError extends BaseError {\n  override name = 'InvalidAbiItemError'\n\n  constructor({ signature }: { signature: string | object }) {\n    super('Failed to parse ABI item.', {\n      details: `parseAbiItem(${JSON.stringify(signature, null, 2)})`,\n      docsPath: '/api/human#parseabiitem-1',\n    })\n  }\n}\n\nexport class UnknownTypeError extends BaseError {\n  override name = 'UnknownTypeError'\n\n  constructor({ type }: { type: string }) {\n    super('Unknown type.', {\n      metaMessages: [\n        `Type \"${type}\" is not a valid ABI type. Perhaps you forgot to include a struct signature?`,\n      ],\n    })\n  }\n}\n\nexport class UnknownSolidityTypeError extends BaseError {\n  override name = 'UnknownSolidityTypeError'\n\n  constructor({ type }: { type: string }) {\n    super('Unknown type.', {\n      metaMessages: [`Type \"${type}\" is not a valid ABI type.`],\n    })\n  }\n}\n", "import type { AbiItemType, AbiParameter } from '../../abi.js'\nimport { BaseError } from '../../errors.js'\nimport type { Modifier } from '../types/signatures.js'\n\nexport class InvalidAbiParameterError extends BaseError {\n  override name = 'InvalidAbiParameterError'\n\n  constructor({ param }: { param: string | object }) {\n    super('Failed to parse ABI parameter.', {\n      details: `parseAbiParameter(${JSON.stringify(param, null, 2)})`,\n      docsPath: '/api/human#parseabiparameter-1',\n    })\n  }\n}\n\nexport class InvalidAbiParametersError extends BaseError {\n  override name = 'InvalidAbiParametersError'\n\n  constructor({ params }: { params: string | object }) {\n    super('Failed to parse ABI parameters.', {\n      details: `parseAbiParameters(${JSON.stringify(params, null, 2)})`,\n      docsPath: '/api/human#parseabiparameters-1',\n    })\n  }\n}\n\nexport class InvalidParameterError extends BaseError {\n  override name = 'InvalidParameterError'\n\n  constructor({ param }: { param: string }) {\n    super('Invalid ABI parameter.', {\n      details: param,\n    })\n  }\n}\n\nexport class SolidityProtectedKeywordError extends BaseError {\n  override name = 'SolidityProtectedKeywordError'\n\n  constructor({ param, name }: { param: string; name: string }) {\n    super('Invalid ABI parameter.', {\n      details: param,\n      metaMessages: [\n        `\"${name}\" is a protected Solidity keyword. More info: https://docs.soliditylang.org/en/latest/cheatsheet.html`,\n      ],\n    })\n  }\n}\n\nexport class InvalidModifierError extends BaseError {\n  override name = 'InvalidModifierError'\n\n  constructor({\n    param,\n    type,\n    modifier,\n  }: {\n    param: string\n    type?: AbiItemType | 'struct' | undefined\n    modifier: Modifier\n  }) {\n    super('Invalid ABI parameter.', {\n      details: param,\n      metaMessages: [\n        `Modifier \"${modifier}\" not allowed${\n          type ? ` in \"${type}\" type` : ''\n        }.`,\n      ],\n    })\n  }\n}\n\nexport class InvalidFunctionModifierError extends BaseError {\n  override name = 'InvalidFunctionModifierError'\n\n  constructor({\n    param,\n    type,\n    modifier,\n  }: {\n    param: string\n    type?: AbiItemType | 'struct' | undefined\n    modifier: Modifier\n  }) {\n    super('Invalid ABI parameter.', {\n      details: param,\n      metaMessages: [\n        `Modifier \"${modifier}\" not allowed${\n          type ? ` in \"${type}\" type` : ''\n        }.`,\n        `Data location can only be specified for array, struct, or mapping types, but \"${modifier}\" was given.`,\n      ],\n    })\n  }\n}\n\nexport class InvalidAbiTypeParameterError extends BaseError {\n  override name = 'InvalidAbiTypeParameterError'\n\n  constructor({\n    abiParameter,\n  }: {\n    abiParameter: AbiParameter & { indexed?: boolean | undefined }\n  }) {\n    super('Invalid ABI parameter.', {\n      details: JSON.stringify(abiParameter, null, 2),\n      metaMessages: ['ABI parameter type is invalid.'],\n    })\n  }\n}\n", "import type { AbiItemType } from '../../abi.js'\nimport { BaseError } from '../../errors.js'\n\nexport class InvalidSignatureError extends BaseError {\n  override name = 'InvalidSignatureError'\n\n  constructor({\n    signature,\n    type,\n  }: {\n    signature: string\n    type: AbiItemType | 'struct'\n  }) {\n    super(`Invalid ${type} signature.`, {\n      details: signature,\n    })\n  }\n}\n\nexport class UnknownSignatureError extends BaseError {\n  override name = 'UnknownSignatureError'\n\n  constructor({ signature }: { signature: string }) {\n    super('Unknown signature.', {\n      details: signature,\n    })\n  }\n}\n\nexport class InvalidStructSignatureError extends BaseError {\n  override name = 'InvalidStructSignatureError'\n\n  constructor({ signature }: { signature: string }) {\n    super('Invalid struct signature.', {\n      details: signature,\n      metaMessages: ['No properties exist.'],\n    })\n  }\n}\n", "import { BaseError } from '../../errors.js'\n\nexport class CircularReferenceError extends BaseError {\n  override name = 'CircularReferenceError'\n\n  constructor({ type }: { type: string }) {\n    super('Circular reference detected.', {\n      metaMessages: [`Struct \"${type}\" is a circular reference.`],\n    })\n  }\n}\n", "import { BaseError } from '../../errors.js'\n\nexport class InvalidParenthesisError extends BaseError {\n  override name = 'InvalidParenthesisError'\n\n  constructor({ current, depth }: { current: string; depth: number }) {\n    super('Unbalanced parentheses.', {\n      metaMessages: [\n        `\"${current.trim()}\" has too many ${\n          depth > 0 ? 'opening' : 'closing'\n        } parentheses.`,\n      ],\n      details: `Depth \"${depth}\"`,\n    })\n  }\n}\n", "// TODO: This looks cool. Need to check the performance of `new RegExp` versus defined inline though.\n// https://twitter.com/GabrielVergnaud/status/1622906834343366657\nexport function execTyped<type>(regex: RegExp, string: string) {\n  const match = regex.exec(string)\n  return match?.groups as type | undefined\n}\n\n// `bytes<M>`: binary type of `M` bytes, `0 < M <= 32`\n// https://regexr.com/6va55\nexport const bytesRegex = /^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/\n\n// `(u)int<M>`: (un)signed integer type of `M` bits, `0 < M <= 256`, `M % 8 == 0`\n// https://regexr.com/6v8hp\nexport const integerRegex =\n  /^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/\n\nexport const isTupleRegex = /^\\(.+?\\).*?$/\n", "import type { AbiStateMutability } from '../../abi.js'\nimport { execTyped } from '../../regex.js'\nimport type {\n  EventModifier,\n  FunctionModifier,\n  Modifier,\n} from '../types/signatures.js'\n\n// https://regexr.com/7gmok\nconst errorSignatureRegex =\n  /^error (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/\nexport function isErrorSignature(signature: string) {\n  return errorSignatureRegex.test(signature)\n}\nexport function execErrorSignature(signature: string) {\n  return execTyped<{ name: string; parameters: string }>(\n    errorSignatureRegex,\n    signature,\n  )\n}\n\n// https://regexr.com/7gmoq\nconst eventSignatureRegex =\n  /^event (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)$/\nexport function isEventSignature(signature: string) {\n  return eventSignatureRegex.test(signature)\n}\nexport function execEventSignature(signature: string) {\n  return execTyped<{ name: string; parameters: string }>(\n    eventSignatureRegex,\n    signature,\n  )\n}\n\n// https://regexr.com/7gmot\nconst functionSignatureRegex =\n  /^function (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*)\\((?<parameters>.*?)\\)(?: (?<scope>external|public{1}))?(?: (?<stateMutability>pure|view|nonpayable|payable{1}))?(?: returns\\s?\\((?<returns>.*?)\\))?$/\nexport function isFunctionSignature(signature: string) {\n  return functionSignatureRegex.test(signature)\n}\nexport function execFunctionSignature(signature: string) {\n  return execTyped<{\n    name: string\n    parameters: string\n    stateMutability?: AbiStateMutability\n    returns?: string\n  }>(functionSignatureRegex, signature)\n}\n\n// https://regexr.com/7gmp3\nconst structSignatureRegex =\n  /^struct (?<name>[a-zA-Z$_][a-zA-Z0-9$_]*) \\{(?<properties>.*?)\\}$/\nexport function isStructSignature(signature: string) {\n  return structSignatureRegex.test(signature)\n}\nexport function execStructSignature(signature: string) {\n  return execTyped<{ name: string; properties: string }>(\n    structSignatureRegex,\n    signature,\n  )\n}\n\n// https://regexr.com/78u01\nconst constructorSignatureRegex =\n  /^constructor\\((?<parameters>.*?)\\)(?:\\s(?<stateMutability>payable{1}))?$/\nexport function isConstructorSignature(signature: string) {\n  return constructorSignatureRegex.test(signature)\n}\nexport function execConstructorSignature(signature: string) {\n  return execTyped<{\n    parameters: string\n    stateMutability?: Extract<AbiStateMutability, 'payable'>\n  }>(constructorSignatureRegex, signature)\n}\n\n// https://regexr.com/7srtn\nconst fallbackSignatureRegex =\n  /^fallback\\(\\) external(?:\\s(?<stateMutability>payable{1}))?$/\nexport function isFallbackSignature(signature: string) {\n  return fallbackSignatureRegex.test(signature)\n}\nexport function execFallbackSignature(signature: string) {\n  return execTyped<{\n    parameters: string\n    stateMutability?: Extract<AbiStateMutability, 'payable'>\n  }>(fallbackSignatureRegex, signature)\n}\n\n// https://regexr.com/78u1k\nconst receiveSignatureRegex = /^receive\\(\\) external payable$/\nexport function isReceiveSignature(signature: string) {\n  return receiveSignatureRegex.test(signature)\n}\n\nexport const modifiers = new Set<Modifier>([\n  'memory',\n  'indexed',\n  'storage',\n  'calldata',\n])\nexport const eventModifiers = new Set<EventModifier>(['indexed'])\nexport const functionModifiers = new Set<FunctionModifier>([\n  'calldata',\n  'memory',\n  'storage',\n])\n", "import type { AbiItemType, AbiParameter } from '../../abi.js'\nimport type { StructLookup } from '../types/structs.js'\n\n/**\n * Gets {@link parameterCache} cache key namespaced by {@link type}. This prevents parameters from being accessible to types that don't allow them (e.g. `string indexed foo` not allowed outside of `type: 'event'`).\n * @param param ABI parameter string\n * @param type ABI parameter type\n * @returns Cache key for {@link parameterCache}\n */\nexport function getParameterCacheKey(\n  param: string,\n  type?: AbiItemType | 'struct',\n  structs?: StructLookup,\n) {\n  let structKey = ''\n  if (structs)\n    for (const struct of Object.entries(structs)) {\n      if (!struct) continue\n      let propertyKey = ''\n      for (const property of struct[1]) {\n        propertyKey += `[${property.type}${property.name ? `:${property.name}` : ''}]`\n      }\n      structKey += `(${struct[0]}{${propertyKey}})`\n    }\n  if (type) return `${type}:${param}${structKey}`\n  return param\n}\n\n/**\n * Basic cache seeded with common ABI parameter strings.\n *\n * **Note: When seeding more parameters, make sure you benchmark performance. The current number is the ideal balance between performance and having an already existing cache.**\n */\nexport const parameterCache = new Map<\n  string,\n  AbiParameter & { indexed?: boolean }\n>([\n  // Unnamed\n  ['address', { type: 'address' }],\n  ['bool', { type: 'bool' }],\n  ['bytes', { type: 'bytes' }],\n  ['bytes32', { type: 'bytes32' }],\n  ['int', { type: 'int256' }],\n  ['int256', { type: 'int256' }],\n  ['string', { type: 'string' }],\n  ['uint', { type: 'uint256' }],\n  ['uint8', { type: 'uint8' }],\n  ['uint16', { type: 'uint16' }],\n  ['uint24', { type: 'uint24' }],\n  ['uint32', { type: 'uint32' }],\n  ['uint64', { type: 'uint64' }],\n  ['uint96', { type: 'uint96' }],\n  ['uint112', { type: 'uint112' }],\n  ['uint160', { type: 'uint160' }],\n  ['uint192', { type: 'uint192' }],\n  ['uint256', { type: 'uint256' }],\n\n  // Named\n  ['address owner', { type: 'address', name: 'owner' }],\n  ['address to', { type: 'address', name: 'to' }],\n  ['bool approved', { type: 'bool', name: 'approved' }],\n  ['bytes _data', { type: 'bytes', name: '_data' }],\n  ['bytes data', { type: 'bytes', name: 'data' }],\n  ['bytes signature', { type: 'bytes', name: 'signature' }],\n  ['bytes32 hash', { type: 'bytes32', name: 'hash' }],\n  ['bytes32 r', { type: 'bytes32', name: 'r' }],\n  ['bytes32 root', { type: 'bytes32', name: 'root' }],\n  ['bytes32 s', { type: 'bytes32', name: 's' }],\n  ['string name', { type: 'string', name: 'name' }],\n  ['string symbol', { type: 'string', name: 'symbol' }],\n  ['string tokenURI', { type: 'string', name: 'tokenURI' }],\n  ['uint tokenId', { type: 'uint256', name: 'tokenId' }],\n  ['uint8 v', { type: 'uint8', name: 'v' }],\n  ['uint256 balance', { type: 'uint256', name: 'balance' }],\n  ['uint256 tokenId', { type: 'uint256', name: 'tokenId' }],\n  ['uint256 value', { type: 'uint256', name: 'value' }],\n\n  // Indexed\n  [\n    'event:address indexed from',\n    { type: 'address', name: 'from', indexed: true },\n  ],\n  ['event:address indexed to', { type: 'address', name: 'to', indexed: true }],\n  [\n    'event:uint indexed tokenId',\n    { type: 'uint256', name: 'tokenId', indexed: true },\n  ],\n  [\n    'event:uint256 indexed tokenId',\n    { type: 'uint256', name: 'tokenId', indexed: true },\n  ],\n])\n", "import type {\n  AbiItemType,\n  AbiType,\n  SolidityArray,\n  SolidityBytes,\n  SolidityString,\n  SolidityTuple,\n} from '../../abi.js'\nimport {\n  bytesRegex,\n  execTyped,\n  integerRegex,\n  isTupleRegex,\n} from '../../regex.js'\nimport { UnknownSolidityTypeError } from '../errors/abiItem.js'\nimport {\n  InvalidFunctionModifierError,\n  InvalidModifierError,\n  InvalidParameterError,\n  SolidityProtectedKeywordError,\n} from '../errors/abiParameter.js'\nimport {\n  InvalidSignatureError,\n  UnknownSignatureError,\n} from '../errors/signature.js'\nimport { InvalidParenthesisError } from '../errors/splitParameters.js'\nimport type { FunctionModifier, Modifier } from '../types/signatures.js'\nimport type { StructLookup } from '../types/structs.js'\nimport { getParameterCacheKey, parameterCache } from './cache.js'\nimport {\n  eventModifiers,\n  execConstructorSignature,\n  execErrorSignature,\n  execEventSignature,\n  execFallbackSignature,\n  execFunctionSignature,\n  functionModifiers,\n  isConstructorSignature,\n  isErrorSignature,\n  isEventSignature,\n  isFallbackSignature,\n  isFunctionSignature,\n  isReceiveSignature,\n} from './signatures.js'\n\nexport function parseSignature(signature: string, structs: StructLookup = {}) {\n  if (isFunctionSignature(signature))\n    return parseFunctionSignature(signature, structs)\n\n  if (isEventSignature(signature))\n    return parseEventSignature(signature, structs)\n\n  if (isErrorSignature(signature))\n    return parseErrorSignature(signature, structs)\n\n  if (isConstructorSignature(signature))\n    return parseConstructorSignature(signature, structs)\n\n  if (isFallbackSignature(signature)) return parseFallbackSignature(signature)\n\n  if (isReceiveSignature(signature))\n    return {\n      type: 'receive',\n      stateMutability: 'payable',\n    }\n\n  throw new UnknownSignatureError({ signature })\n}\n\nexport function parseFunctionSignature(\n  signature: string,\n  structs: StructLookup = {},\n) {\n  const match = execFunctionSignature(signature)\n  if (!match) throw new InvalidSignatureError({ signature, type: 'function' })\n\n  const inputParams = splitParameters(match.parameters)\n  const inputs = []\n  const inputLength = inputParams.length\n  for (let i = 0; i < inputLength; i++) {\n    inputs.push(\n      parseAbiParameter(inputParams[i]!, {\n        modifiers: functionModifiers,\n        structs,\n        type: 'function',\n      }),\n    )\n  }\n\n  const outputs = []\n  if (match.returns) {\n    const outputParams = splitParameters(match.returns)\n    const outputLength = outputParams.length\n    for (let i = 0; i < outputLength; i++) {\n      outputs.push(\n        parseAbiParameter(outputParams[i]!, {\n          modifiers: functionModifiers,\n          structs,\n          type: 'function',\n        }),\n      )\n    }\n  }\n\n  return {\n    name: match.name,\n    type: 'function',\n    stateMutability: match.stateMutability ?? 'nonpayable',\n    inputs,\n    outputs,\n  }\n}\n\nexport function parseEventSignature(\n  signature: string,\n  structs: StructLookup = {},\n) {\n  const match = execEventSignature(signature)\n  if (!match) throw new InvalidSignatureError({ signature, type: 'event' })\n\n  const params = splitParameters(match.parameters)\n  const abiParameters = []\n  const length = params.length\n  for (let i = 0; i < length; i++)\n    abiParameters.push(\n      parseAbiParameter(params[i]!, {\n        modifiers: eventModifiers,\n        structs,\n        type: 'event',\n      }),\n    )\n  return { name: match.name, type: 'event', inputs: abiParameters }\n}\n\nexport function parseErrorSignature(\n  signature: string,\n  structs: StructLookup = {},\n) {\n  const match = execErrorSignature(signature)\n  if (!match) throw new InvalidSignatureError({ signature, type: 'error' })\n\n  const params = splitParameters(match.parameters)\n  const abiParameters = []\n  const length = params.length\n  for (let i = 0; i < length; i++)\n    abiParameters.push(\n      parseAbiParameter(params[i]!, { structs, type: 'error' }),\n    )\n  return { name: match.name, type: 'error', inputs: abiParameters }\n}\n\nexport function parseConstructorSignature(\n  signature: string,\n  structs: StructLookup = {},\n) {\n  const match = execConstructorSignature(signature)\n  if (!match)\n    throw new InvalidSignatureError({ signature, type: 'constructor' })\n\n  const params = splitParameters(match.parameters)\n  const abiParameters = []\n  const length = params.length\n  for (let i = 0; i < length; i++)\n    abiParameters.push(\n      parseAbiParameter(params[i]!, { structs, type: 'constructor' }),\n    )\n  return {\n    type: 'constructor',\n    stateMutability: match.stateMutability ?? 'nonpayable',\n    inputs: abiParameters,\n  }\n}\n\nexport function parseFallbackSignature(signature: string) {\n  const match = execFallbackSignature(signature)\n  if (!match) throw new InvalidSignatureError({ signature, type: 'fallback' })\n\n  return {\n    type: 'fallback',\n    stateMutability: match.stateMutability ?? 'nonpayable',\n  }\n}\n\nconst abiParameterWithoutTupleRegex =\n  /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/\nconst abiParameterWithTupleRegex =\n  /^\\((?<type>.+?)\\)(?<array>(?:\\[\\d*?\\])+?)?(?:\\s(?<modifier>calldata|indexed|memory|storage{1}))?(?:\\s(?<name>[a-zA-Z$_][a-zA-Z0-9$_]*))?$/\nconst dynamicIntegerRegex = /^u?int$/\n\ntype ParseOptions = {\n  modifiers?: Set<Modifier>\n  structs?: StructLookup\n  type?: AbiItemType | 'struct'\n}\n\nexport function parseAbiParameter(param: string, options?: ParseOptions) {\n  // optional namespace cache by `type`\n  const parameterCacheKey = getParameterCacheKey(\n    param,\n    options?.type,\n    options?.structs,\n  )\n  if (parameterCache.has(parameterCacheKey))\n    return parameterCache.get(parameterCacheKey)!\n\n  const isTuple = isTupleRegex.test(param)\n  const match = execTyped<{\n    array?: string\n    modifier?: Modifier\n    name?: string\n    type: string\n  }>(\n    isTuple ? abiParameterWithTupleRegex : abiParameterWithoutTupleRegex,\n    param,\n  )\n  if (!match) throw new InvalidParameterError({ param })\n\n  if (match.name && isSolidityKeyword(match.name))\n    throw new SolidityProtectedKeywordError({ param, name: match.name })\n\n  const name = match.name ? { name: match.name } : {}\n  const indexed = match.modifier === 'indexed' ? { indexed: true } : {}\n  const structs = options?.structs ?? {}\n  let type: string\n  let components = {}\n  if (isTuple) {\n    type = 'tuple'\n    const params = splitParameters(match.type)\n    const components_ = []\n    const length = params.length\n    for (let i = 0; i < length; i++) {\n      // remove `modifiers` from `options` to prevent from being added to tuple components\n      components_.push(parseAbiParameter(params[i]!, { structs }))\n    }\n    components = { components: components_ }\n  } else if (match.type in structs) {\n    type = 'tuple'\n    components = { components: structs[match.type] }\n  } else if (dynamicIntegerRegex.test(match.type)) {\n    type = `${match.type}256`\n  } else {\n    type = match.type\n    if (!(options?.type === 'struct') && !isSolidityType(type))\n      throw new UnknownSolidityTypeError({ type })\n  }\n\n  if (match.modifier) {\n    // Check if modifier exists, but is not allowed (e.g. `indexed` in `functionModifiers`)\n    if (!options?.modifiers?.has?.(match.modifier))\n      throw new InvalidModifierError({\n        param,\n        type: options?.type,\n        modifier: match.modifier,\n      })\n\n    // Check if resolved `type` is valid if there is a function modifier\n    if (\n      functionModifiers.has(match.modifier as FunctionModifier) &&\n      !isValidDataLocation(type, !!match.array)\n    )\n      throw new InvalidFunctionModifierError({\n        param,\n        type: options?.type,\n        modifier: match.modifier,\n      })\n  }\n\n  const abiParameter = {\n    type: `${type}${match.array ?? ''}`,\n    ...name,\n    ...indexed,\n    ...components,\n  }\n  parameterCache.set(parameterCacheKey, abiParameter)\n  return abiParameter\n}\n\n// s/o latika for this\nexport function splitParameters(\n  params: string,\n  result: string[] = [],\n  current = '',\n  depth = 0,\n): readonly string[] {\n  const length = params.trim().length\n  // biome-ignore lint/correctness/noUnreachable: recursive\n  for (let i = 0; i < length; i++) {\n    const char = params[i]\n    const tail = params.slice(i + 1)\n    switch (char) {\n      case ',':\n        return depth === 0\n          ? splitParameters(tail, [...result, current.trim()])\n          : splitParameters(tail, result, `${current}${char}`, depth)\n      case '(':\n        return splitParameters(tail, result, `${current}${char}`, depth + 1)\n      case ')':\n        return splitParameters(tail, result, `${current}${char}`, depth - 1)\n      default:\n        return splitParameters(tail, result, `${current}${char}`, depth)\n    }\n  }\n\n  if (current === '') return result\n  if (depth !== 0) throw new InvalidParenthesisError({ current, depth })\n\n  result.push(current.trim())\n  return result\n}\n\nexport function isSolidityType(\n  type: string,\n): type is Exclude<AbiType, SolidityTuple | SolidityArray> {\n  return (\n    type === 'address' ||\n    type === 'bool' ||\n    type === 'function' ||\n    type === 'string' ||\n    bytesRegex.test(type) ||\n    integerRegex.test(type)\n  )\n}\n\nconst protectedKeywordsRegex =\n  /^(?:after|alias|anonymous|apply|auto|byte|calldata|case|catch|constant|copyof|default|defined|error|event|external|false|final|function|immutable|implements|in|indexed|inline|internal|let|mapping|match|memory|mutable|null|of|override|partial|private|promise|public|pure|reference|relocatable|return|returns|sizeof|static|storage|struct|super|supports|switch|this|true|try|typedef|typeof|var|view|virtual)$/\n\n/** @internal */\nexport function isSolidityKeyword(name: string) {\n  return (\n    name === 'address' ||\n    name === 'bool' ||\n    name === 'function' ||\n    name === 'string' ||\n    name === 'tuple' ||\n    bytesRegex.test(name) ||\n    integerRegex.test(name) ||\n    protectedKeywordsRegex.test(name)\n  )\n}\n\n/** @internal */\nexport function isValidDataLocation(\n  type: string,\n  isArray: boolean,\n): type is Exclude<\n  AbiType,\n  SolidityString | Extract<SolidityBytes, 'bytes'> | SolidityArray\n> {\n  return isArray || type === 'bytes' || type === 'string' || type === 'tuple'\n}\n", "import type { AbiParameter } from '../../abi.js'\nimport { execTyped, isTupleRegex } from '../../regex.js'\nimport { UnknownTypeError } from '../errors/abiItem.js'\nimport { InvalidAbiTypeParameterError } from '../errors/abiParameter.js'\nimport {\n  InvalidSignatureError,\n  InvalidStructSignatureError,\n} from '../errors/signature.js'\nimport { CircularReferenceError } from '../errors/struct.js'\nimport type { StructLookup } from '../types/structs.js'\nimport { execStructSignature, isStructSignature } from './signatures.js'\nimport { isSolidityType, parseAbiParameter } from './utils.js'\n\nexport function parseStructs(signatures: readonly string[]) {\n  // Create \"shallow\" version of each struct (and filter out non-structs or invalid structs)\n  const shallowStructs: StructLookup = {}\n  const signaturesLength = signatures.length\n  for (let i = 0; i < signaturesLength; i++) {\n    const signature = signatures[i]!\n    if (!isStructSignature(signature)) continue\n\n    const match = execStructSignature(signature)\n    if (!match) throw new InvalidSignatureError({ signature, type: 'struct' })\n\n    const properties = match.properties.split(';')\n\n    const components: AbiParameter[] = []\n    const propertiesLength = properties.length\n    for (let k = 0; k < propertiesLength; k++) {\n      const property = properties[k]!\n      const trimmed = property.trim()\n      if (!trimmed) continue\n      const abiParameter = parseAbiParameter(trimmed, {\n        type: 'struct',\n      })\n      components.push(abiParameter)\n    }\n\n    if (!components.length) throw new InvalidStructSignatureError({ signature })\n    shallowStructs[match.name] = components\n  }\n\n  // Resolve nested structs inside each parameter\n  const resolvedStructs: StructLookup = {}\n  const entries = Object.entries(shallowStructs)\n  const entriesLength = entries.length\n  for (let i = 0; i < entriesLength; i++) {\n    const [name, parameters] = entries[i]!\n    resolvedStructs[name] = resolveStructs(parameters, shallowStructs)\n  }\n\n  return resolvedStructs\n}\n\nconst typeWithoutTupleRegex =\n  /^(?<type>[a-zA-Z$_][a-zA-Z0-9$_]*)(?<array>(?:\\[\\d*?\\])+?)?$/\n\nfunction resolveStructs(\n  abiParameters: readonly (AbiParameter & { indexed?: true })[],\n  structs: StructLookup,\n  ancestors = new Set<string>(),\n) {\n  const components: AbiParameter[] = []\n  const length = abiParameters.length\n  for (let i = 0; i < length; i++) {\n    const abiParameter = abiParameters[i]!\n    const isTuple = isTupleRegex.test(abiParameter.type)\n    if (isTuple) components.push(abiParameter)\n    else {\n      const match = execTyped<{ array?: string; type: string }>(\n        typeWithoutTupleRegex,\n        abiParameter.type,\n      )\n      if (!match?.type) throw new InvalidAbiTypeParameterError({ abiParameter })\n\n      const { array, type } = match\n      if (type in structs) {\n        if (ancestors.has(type)) throw new CircularReferenceError({ type })\n\n        components.push({\n          ...abiParameter,\n          type: `tuple${array ?? ''}`,\n          components: resolveStructs(\n            structs[type] ?? [],\n            structs,\n            new Set([...ancestors, type]),\n          ),\n        })\n      } else {\n        if (isSolidityType(type)) components.push(abiParameter)\n        else throw new UnknownTypeError({ type })\n      }\n    }\n  }\n\n  return components\n}\n", "import type { <PERSON><PERSON> } from '../abi.js'\nimport type { <PERSON>rro<PERSON>, Filter } from '../types.js'\nimport { isStructSignature } from './runtime/signatures.js'\nimport { parseStructs } from './runtime/structs.js'\nimport { parseSignature } from './runtime/utils.js'\nimport type { Signatures } from './types/signatures.js'\nimport type { ParseStructs } from './types/structs.js'\nimport type { ParseSignature } from './types/utils.js'\n\n/**\n * Parses human-readable ABI into JSON {@link Abi}\n *\n * @param signatures - Human-readable ABI\n * @returns Parsed {@link Abi}\n *\n * @example\n * type Result = ParseAbi<\n *   // ^? type Result = readonly [{ name: \"balanceOf\"; type: \"function\"; stateMutability:...\n *   [\n *     'function balanceOf(address owner) view returns (uint256)',\n *     'event Transfer(address indexed from, address indexed to, uint256 amount)',\n *   ]\n * >\n */\nexport type Parse<PERSON>bi<signatures extends readonly string[]> =\n  string[] extends signatures\n    ? Abi // If `T` was not able to be inferred (e.g. just `string[]`), return `Abi`\n    : signatures extends readonly string[]\n      ? signatures extends Signatures<signatures> // Validate signatures\n        ? ParseStructs<signatures> extends infer sructs\n          ? {\n              [key in keyof signatures]: signatures[key] extends string\n                ? ParseSignature<signatures[key], sructs>\n                : never\n            } extends infer mapped extends readonly unknown[]\n            ? Filter<mapped, never> extends infer result\n              ? result extends readonly []\n                ? never\n                : result\n              : never\n            : never\n          : never\n        : never\n      : never\n\n/**\n * Parses human-readable ABI into JSON {@link Abi}\n *\n * @param signatures - Human-Readable ABI\n * @returns Parsed {@link Abi}\n *\n * @example\n * const abi = parseAbi([\n *   //  ^? const abi: readonly [{ name: \"balanceOf\"; type: \"function\"; stateMutability:...\n *   'function balanceOf(address owner) view returns (uint256)',\n *   'event Transfer(address indexed from, address indexed to, uint256 amount)',\n * ])\n */\nexport function parseAbi<const signatures extends readonly string[]>(\n  signatures: signatures['length'] extends 0\n    ? Error<'At least one signature required'>\n    : Signatures<signatures> extends signatures\n      ? signatures\n      : Signatures<signatures>,\n): ParseAbi<signatures> {\n  const structs = parseStructs(signatures as readonly string[])\n  const abi = []\n  const length = signatures.length as number\n  for (let i = 0; i < length; i++) {\n    const signature = (signatures as readonly string[])[i]!\n    if (isStructSignature(signature)) continue\n    abi.push(parseSignature(signature, structs))\n  }\n  return abi as unknown as ParseAbi<signatures>\n}\n", "import type { <PERSON><PERSON> } from '../abi.js'\nimport type { Narrow } from '../narrow.js'\nimport type { Error, Filter } from '../types.js'\nimport { InvalidAbiItemError } from './errors/abiItem.js'\nimport { isStructSignature } from './runtime/signatures.js'\nimport { parseStructs } from './runtime/structs.js'\nimport { parseSignature } from './runtime/utils.js'\nimport type { Signature, Signatures } from './types/signatures.js'\nimport type { ParseStructs } from './types/structs.js'\nimport type { ParseSignature } from './types/utils.js'\n\n/**\n * Parses human-readable ABI item (e.g. error, event, function) into {@link Abi} item\n *\n * @param signature - Human-readable ABI item\n * @returns Parsed {@link Abi} item\n *\n * @example\n * type Result = ParseAbiItem<'function balanceOf(address owner) view returns (uint256)'>\n * //   ^? type Result = { name: \"balanceOf\"; type: \"function\"; stateMutability: \"view\";...\n *\n * @example\n * type Result = ParseAbiItem<\n *   // ^? type Result = { name: \"foo\"; type: \"function\"; stateMutability: \"view\"; inputs:...\n *   ['function foo(Baz bar) view returns (string)', 'struct Baz { string name; }']\n * >\n */\nexport type ParseAbiItem<\n  signature extends string | readonly string[] | readonly unknown[],\n> =\n  | (signature extends string\n      ? string extends signature\n        ? Abi[number]\n        : signature extends Signature<signature> // Validate signature\n          ? ParseSignature<signature>\n          : never\n      : never)\n  | (signature extends readonly string[]\n      ? string[] extends signature\n        ? Abi[number] // Return generic Abi item since type was no inferrable\n        : signature extends Signatures<signature> // Validate signature\n          ? ParseStructs<signature> extends infer structs\n            ? {\n                [key in keyof signature]: ParseSignature<\n                  signature[key] extends string ? signature[key] : never,\n                  structs\n                >\n              } extends infer mapped extends readonly unknown[]\n              ? // Filter out `never` since those are structs\n                Filter<mapped, never>[0] extends infer result\n                ? result extends undefined // convert `undefined` to `never` (e.g. `ParseAbiItem<['struct Foo { string name; }']>`)\n                  ? never\n                  : result\n                : never\n              : never\n            : never\n          : never\n      : never)\n\n/**\n * Parses human-readable ABI item (e.g. error, event, function) into {@link Abi} item\n *\n * @param signature - Human-readable ABI item\n * @returns Parsed {@link Abi} item\n *\n * @example\n * const abiItem = parseAbiItem('function balanceOf(address owner) view returns (uint256)')\n * //    ^? const abiItem: { name: \"balanceOf\"; type: \"function\"; stateMutability: \"view\";...\n *\n * @example\n * const abiItem = parseAbiItem([\n *   //  ^? const abiItem: { name: \"foo\"; type: \"function\"; stateMutability: \"view\"; inputs:...\n *   'function foo(Baz bar) view returns (string)',\n *   'struct Baz { string name; }',\n * ])\n */\nexport function parseAbiItem<\n  signature extends string | readonly string[] | readonly unknown[],\n>(\n  signature: Narrow<signature> &\n    (\n      | (signature extends string\n          ? string extends signature\n            ? unknown\n            : Signature<signature>\n          : never)\n      | (signature extends readonly string[]\n          ? signature extends readonly [] // empty array\n            ? Error<'At least one signature required.'>\n            : string[] extends signature\n              ? unknown\n              : Signatures<signature>\n          : never)\n    ),\n): ParseAbiItem<signature> {\n  let abiItem: ParseAbiItem<signature> | undefined\n  if (typeof signature === 'string')\n    abiItem = parseSignature(signature) as ParseAbiItem<signature>\n  else {\n    const structs = parseStructs(signature as readonly string[])\n    const length = signature.length as number\n    for (let i = 0; i < length; i++) {\n      const signature_ = (signature as readonly string[])[i]!\n      if (isStructSignature(signature_)) continue\n      abiItem = parseSignature(signature_, structs) as ParseAbiItem<signature>\n      break\n    }\n  }\n\n  if (!abiItem) throw new InvalidAbiItemError({ signature })\n  return abiItem as ParseAbiItem<signature>\n}\n", "import type { <PERSON>bi<PERSON>arameter } from '../abi.js'\nimport type { Narrow } from '../narrow.js'\nimport type { Error, Filter } from '../types.js'\nimport { InvalidAbiParameterError } from './errors/abiParameter.js'\nimport { isStructSignature, modifiers } from './runtime/signatures.js'\nimport { parseStructs } from './runtime/structs.js'\nimport { parseAbiParameter as parseAbiParameter_ } from './runtime/utils.js'\nimport type { IsStructSignature, Modifier } from './types/signatures.js'\nimport type { ParseStructs } from './types/structs.js'\nimport type { ParseAbiParameter as ParseAbiParameter_ } from './types/utils.js'\n\n/**\n * Parses human-readable ABI parameter into {@link AbiParameter}\n *\n * @param param - Human-readable ABI parameter\n * @returns Parsed {@link AbiParameter}\n *\n * @example\n * type Result = ParseAbiParameter<'address from'>\n * //   ^? type Result = { type: \"address\"; name: \"from\"; }\n *\n * @example\n * type Result = ParseAbiParameter<\n *   // ^? type Result = { type: \"tuple\"; components: [{ type: \"string\"; name:...\n *   ['Baz bar', 'struct Baz { string name; }']\n * >\n */\nexport type ParseAbiParameter<\n  param extends string | readonly string[] | readonly unknown[],\n> =\n  | (param extends string\n      ? param extends ''\n        ? never\n        : string extends param\n          ? AbiParameter\n          : ParseAbiParameter_<param, { modifier: Modifier }>\n      : never)\n  | (param extends readonly string[]\n      ? string[] extends param\n        ? AbiParameter // Return generic AbiParameter item since type was no inferrable\n        : ParseStructs<param> extends infer structs\n          ? {\n              [key in keyof param]: param[key] extends string\n                ? IsStructSignature<param[key]> extends true\n                  ? never\n                  : ParseAbiParameter_<\n                      param[key],\n                      { modifier: Modifier; structs: structs }\n                    >\n                : never\n            } extends infer mapped extends readonly unknown[]\n            ? Filter<mapped, never>[0] extends infer result\n              ? result extends undefined\n                ? never\n                : result\n              : never\n            : never\n          : never\n      : never)\n\n/**\n * Parses human-readable ABI parameter into {@link AbiParameter}\n *\n * @param param - Human-readable ABI parameter\n * @returns Parsed {@link AbiParameter}\n *\n * @example\n * const abiParameter = parseAbiParameter('address from')\n * //    ^? const abiParameter: { type: \"address\"; name: \"from\"; }\n *\n * @example\n * const abiParameter = parseAbiParameter([\n *   //  ^? const abiParameter: { type: \"tuple\"; components: [{ type: \"string\"; name:...\n *   'Baz bar',\n *   'struct Baz { string name; }',\n * ])\n */\nexport function parseAbiParameter<\n  param extends string | readonly string[] | readonly unknown[],\n>(\n  param: Narrow<param> &\n    (\n      | (param extends string\n          ? param extends ''\n            ? Error<'Empty string is not allowed.'>\n            : unknown\n          : never)\n      | (param extends readonly string[]\n          ? param extends readonly [] // empty array\n            ? Error<'At least one parameter required.'>\n            : string[] extends param\n              ? unknown\n              : unknown // TODO: Validate param string\n          : never)\n    ),\n): ParseAbiParameter<param> {\n  let abiParameter: AbiParameter | undefined\n  if (typeof param === 'string')\n    abiParameter = parseAbiParameter_(param, {\n      modifiers,\n    }) as ParseAbiParameter<param>\n  else {\n    const structs = parseStructs(param as readonly string[])\n    const length = param.length as number\n    for (let i = 0; i < length; i++) {\n      const signature = (param as readonly string[])[i]!\n      if (isStructSignature(signature)) continue\n      abiParameter = parseAbiParameter_(signature, { modifiers, structs })\n      break\n    }\n  }\n\n  if (!abiParameter) throw new InvalidAbiParameterError({ param })\n\n  return abiParameter as ParseAbiParameter<param>\n}\n", "import type { AbiParameter } from '../abi.js'\nimport type { Narrow } from '../narrow.js'\nimport type { Error, Filter } from '../types.js'\nimport { InvalidAbiParametersError } from './errors/abiParameter.js'\nimport { isStructSignature, modifiers } from './runtime/signatures.js'\nimport { parseStructs } from './runtime/structs.js'\nimport { splitParameters } from './runtime/utils.js'\nimport { parseAbiParameter as parseAbiParameter_ } from './runtime/utils.js'\nimport type { IsStructSignature, Modifier } from './types/signatures.js'\nimport type { ParseStructs } from './types/structs.js'\nimport type { SplitParameters } from './types/utils.js'\nimport type { ParseAbiParameters as ParseAbiParameters_ } from './types/utils.js'\n\n/**\n * Parses human-readable ABI parameters into {@link AbiParameter}s\n *\n * @param params - Human-readable ABI parameters\n * @returns Parsed {@link AbiParameter}s\n *\n * @example\n * type Result = ParseAbiParameters('address from, address to, uint256 amount')\n * //   ^? type Result: [{ type: \"address\"; name: \"from\"; }, { type: \"address\";...\n *\n * @example\n * type Result = ParseAbiParameters<\n *   // ^? type Result: [{ type: \"tuple\"; components: [{ type: \"string\"; name:...\n *   ['Baz bar', 'struct Baz { string name; }']\n * >\n */\nexport type ParseAbiParameters<\n  params extends string | readonly string[] | readonly unknown[],\n> =\n  | (params extends string\n      ? params extends ''\n        ? never\n        : string extends params\n          ? readonly AbiParameter[]\n          : ParseAbiParameters_<SplitParameters<params>, { modifier: Modifier }>\n      : never)\n  | (params extends readonly string[]\n      ? string[] extends params\n        ? AbiParameter // Return generic AbiParameter item since type was no inferrable\n        : ParseStructs<params> extends infer structs\n          ? {\n              [key in keyof params]: params[key] extends string\n                ? IsStructSignature<params[key]> extends true\n                  ? never\n                  : ParseAbiParameters_<\n                      SplitParameters<params[key]>,\n                      { modifier: Modifier; structs: structs }\n                    >\n                : never\n            } extends infer mapped extends readonly unknown[]\n            ? Filter<mapped, never> extends readonly [...infer content]\n              ? content['length'] extends 0\n                ? never\n                : DeepFlatten<content>\n              : never\n            : never\n          : never\n      : never)\n\n/**\n * Flatten all members of {@link T}\n *\n * @param T - List of items to flatten\n * @param Acc - The accumulator used while recursing\n * @returns The flattened array\n *\n * @example\n * type Result = DeepFlatten<[['a', 'b'], [['c']]]>\n * //   ^? type Result = ['a', 'b', 'c']\n */\ntype DeepFlatten<\n  T extends readonly unknown[],\n  Acc extends readonly unknown[] = readonly [],\n> = T extends readonly [infer head, ...infer tail]\n  ? tail extends undefined\n    ? never\n    : head extends readonly unknown[]\n      ? DeepFlatten<tail, readonly [...Acc, ...DeepFlatten<head>]>\n      : DeepFlatten<tail, readonly [...Acc, head]>\n  : Acc\n\n/**\n * Parses human-readable ABI parameters into {@link AbiParameter}s\n *\n * @param params - Human-readable ABI parameters\n * @returns Parsed {@link AbiParameter}s\n *\n * @example\n * const abiParameters = parseAbiParameters('address from, address to, uint256 amount')\n * //    ^? const abiParameters: [{ type: \"address\"; name: \"from\"; }, { type: \"address\";...\n *\n * @example\n * const abiParameters = parseAbiParameters([\n *   //  ^? const abiParameters: [{ type: \"tuple\"; components: [{ type: \"string\"; name:...\n *   'Baz bar',\n *   'struct Baz { string name; }',\n * ])\n */\nexport function parseAbiParameters<\n  params extends string | readonly string[] | readonly unknown[],\n>(\n  params: Narrow<params> &\n    (\n      | (params extends string\n          ? params extends ''\n            ? Error<'Empty string is not allowed.'>\n            : unknown\n          : never)\n      | (params extends readonly string[]\n          ? params extends readonly [] // empty array\n            ? Error<'At least one parameter required.'>\n            : string[] extends params\n              ? unknown\n              : unknown // TODO: Validate param string\n          : never)\n    ),\n): ParseAbiParameters<params> {\n  const abiParameters: AbiParameter[] = []\n  if (typeof params === 'string') {\n    const parameters = splitParameters(params)\n    const length = parameters.length\n    for (let i = 0; i < length; i++) {\n      abiParameters.push(parseAbiParameter_(parameters[i]!, { modifiers }))\n    }\n  } else {\n    const structs = parseStructs(params as readonly string[])\n    const length = params.length as number\n    for (let i = 0; i < length; i++) {\n      const signature = (params as readonly string[])[i]!\n      if (isStructSignature(signature)) continue\n      const parameters = splitParameters(signature)\n      const length = parameters.length\n      for (let k = 0; k < length; k++) {\n        abiParameters.push(\n          parseAbiParameter_(parameters[k]!, { modifiers, structs }),\n        )\n      }\n    }\n  }\n\n  if (abiParameters.length === 0)\n    throw new InvalidAbiParametersError({ params })\n\n  return abiParameters as ParseAbiParameters<params>\n}\n", "import type { AbiEventParameter, AbiParameter } from '../abi.js'\nimport { execTyped } from '../regex.js'\nimport type { IsNarrowable, Join } from '../types.js'\nimport type { AssertName } from './types/signatures.js'\n\n/**\n * Formats {@link AbiParameter} to human-readable ABI parameter.\n *\n * @param abiParameter - ABI parameter\n * @returns Human-readable ABI parameter\n *\n * @example\n * type Result = FormatAbiParameter<{ type: 'address'; name: 'from'; }>\n * //   ^? type Result = 'address from'\n */\nexport type FormatAbiParameter<\n  abiParameter extends AbiParameter | AbiEventParameter,\n> = abiParameter extends {\n  name?: infer name extends string\n  type: `tuple${infer array}`\n  components: infer components extends readonly AbiParameter[]\n  indexed?: infer indexed extends boolean\n}\n  ? FormatAbiParameter<\n      {\n        type: `(${Join<\n          {\n            [key in keyof components]: FormatAbiParameter<\n              {\n                type: components[key]['type']\n              } & (IsNarrowable<components[key]['name'], string> extends true\n                ? { name: components[key]['name'] }\n                : unknown) &\n                (components[key] extends { components: readonly AbiParameter[] }\n                  ? { components: components[key]['components'] }\n                  : unknown)\n            >\n          },\n          ', '\n        >})${array}`\n      } & (IsNarrowable<name, string> extends true ? { name: name } : unknown) &\n        (IsNarrowable<indexed, boolean> extends true\n          ? { indexed: indexed }\n          : unknown)\n    >\n  : `${abiParameter['type']}${abiParameter extends { indexed: true }\n      ? ' indexed'\n      : ''}${abiParameter['name'] extends infer name extends string\n      ? name extends ''\n        ? ''\n        : ` ${AssertName<name>}`\n      : ''}`\n\n// https://regexr.com/7f7rv\nconst tupleRegex = /^tuple(?<array>(\\[(\\d*)\\])*)$/\n\n/**\n * Formats {@link AbiParameter} to human-readable ABI parameter.\n *\n * @param abiParameter - ABI parameter\n * @returns Human-readable ABI parameter\n *\n * @example\n * const result = formatAbiParameter({ type: 'address', name: 'from' })\n * //    ^? const result: 'address from'\n */\nexport function formatAbiParameter<\n  const abiParameter extends AbiParameter | AbiEventParameter,\n>(abiParameter: abiParameter): FormatAbiParameter<abiParameter> {\n  type Result = FormatAbiParameter<abiParameter>\n\n  let type = abiParameter.type\n  if (tupleRegex.test(abiParameter.type) && 'components' in abiParameter) {\n    type = '('\n    const length = abiParameter.components.length as number\n    for (let i = 0; i < length; i++) {\n      const component = abiParameter.components[i]!\n      type += formatAbiParameter(component)\n      if (i < length - 1) type += ', '\n    }\n    const result = execTyped<{ array?: string }>(tupleRegex, abiParameter.type)\n    type += `)${result?.array ?? ''}`\n    return formatAbiParameter({\n      ...abiParameter,\n      type,\n    }) as Result\n  }\n  // Add `indexed` to type if in `abiParameter`\n  if ('indexed' in abiParameter && abiParameter.indexed)\n    type = `${type} indexed`\n  // Return human-readable ABI parameter\n  if (abiParameter.name) return `${type} ${abiParameter.name}` as Result\n  return type as Result\n}\n", "import type { AbiEventParameter, AbiParameter } from '../abi.js'\nimport type { Join } from '../types.js'\nimport {\n  type FormatAbiParameter,\n  formatAbiParameter,\n} from './formatAbiParameter.js'\n\n/**\n * Formats {@link AbiParameter}s to human-readable ABI parameter.\n *\n * @param abiParameters - ABI parameters\n * @returns Human-readable ABI parameters\n *\n * @example\n * type Result = FormatAbiParameters<[\n *   // ^? type Result = 'address from, uint256 tokenId'\n *   { type: 'address'; name: 'from'; },\n *   { type: 'uint256'; name: 'tokenId'; },\n * ]>\n */\nexport type FormatAbiParameters<\n  abiParameters extends readonly [\n    AbiParameter | AbiEventParameter,\n    ...(readonly (AbiParameter | AbiEventParameter)[]),\n  ],\n> = Join<\n  {\n    [key in keyof abiParameters]: FormatAbiParameter<abiParameters[key]>\n  },\n  ', '\n>\n\n/**\n * Formats {@link AbiParameter}s to human-readable ABI parameters.\n *\n * @param abiParameters - ABI parameters\n * @returns Human-readable ABI parameters\n *\n * @example\n * const result = formatAbiParameters([\n *   //  ^? const result: 'address from, uint256 tokenId'\n *   { type: 'address', name: 'from' },\n *   { type: 'uint256', name: 'tokenId' },\n * ])\n */\nexport function formatAbiParameters<\n  const abiParameters extends readonly [\n    AbiParameter | AbiEventParameter,\n    ...(readonly (AbiParameter | AbiEventParameter)[]),\n  ],\n>(abiParameters: abiParameters): FormatAbiParameters<abiParameters> {\n  let params = ''\n  const length = abiParameters.length\n  for (let i = 0; i < length; i++) {\n    const abiParameter = abiParameters[i]!\n    params += formatAbiParameter(abiParameter)\n    if (i !== length - 1) params += ', '\n  }\n  return params as FormatAbiParameters<abiParameters>\n}\n", "import type {\n  <PERSON><PERSON>,\n  AbiConstructor,\n  AbiError,\n  AbiEvent,\n  AbiEventParameter,\n  AbiFallback,\n  AbiFunction,\n  AbiParameter,\n  AbiReceive,\n  AbiStateMutability,\n} from '../abi.js'\nimport {\n  type FormatAbiParameters as FormatAbiParameters_,\n  formatAbiParameters,\n} from './formatAbiParameters.js'\nimport type { AssertName } from './types/signatures.js'\n\n/**\n * Formats ABI item (e.g. error, event, function) into human-readable ABI item\n *\n * @param abiItem - ABI item\n * @returns Human-readable ABI item\n */\nexport type FormatAbiItem<abiItem extends Abi[number]> =\n  Abi[number] extends abiItem\n    ? string\n    :\n        | (abiItem extends AbiFunction\n            ? AbiFunction extends abiItem\n              ? string\n              : `function ${AssertName<abiItem['name']>}(${FormatAbiParameters<\n                  abiItem['inputs']\n                >})${abiItem['stateMutability'] extends Exclude<\n                  AbiStateMutability,\n                  'nonpayable'\n                >\n                  ? ` ${abiItem['stateMutability']}`\n                  : ''}${abiItem['outputs']['length'] extends 0\n                  ? ''\n                  : ` returns (${FormatAbiParameters<abiItem['outputs']>})`}`\n            : never)\n        | (abiItem extends AbiEvent\n            ? AbiEvent extends abiItem\n              ? string\n              : `event ${AssertName<abiItem['name']>}(${FormatAbiParameters<\n                  abiItem['inputs']\n                >})`\n            : never)\n        | (abiItem extends AbiError\n            ? AbiError extends abiItem\n              ? string\n              : `error ${AssertName<abiItem['name']>}(${FormatAbiParameters<\n                  abiItem['inputs']\n                >})`\n            : never)\n        | (abiItem extends AbiConstructor\n            ? AbiConstructor extends abiItem\n              ? string\n              : `constructor(${FormatAbiParameters<\n                  abiItem['inputs']\n                >})${abiItem['stateMutability'] extends 'payable'\n                  ? ' payable'\n                  : ''}`\n            : never)\n        | (abiItem extends AbiFallback\n            ? AbiFallback extends abiItem\n              ? string\n              : `fallback() external${abiItem['stateMutability'] extends 'payable'\n                  ? ' payable'\n                  : ''}`\n            : never)\n        | (abiItem extends AbiReceive\n            ? AbiReceive extends abiItem\n              ? string\n              : 'receive() external payable'\n            : never)\n\ntype FormatAbiParameters<\n  abiParameters extends readonly (AbiParameter | AbiEventParameter)[],\n> = abiParameters['length'] extends 0\n  ? ''\n  : FormatAbiParameters_<\n      abiParameters extends readonly [\n        AbiParameter | AbiEventParameter,\n        ...(readonly (AbiParameter | AbiEventParameter)[]),\n      ]\n        ? abiParameters\n        : never\n    >\n\n/**\n * Formats ABI item (e.g. error, event, function) into human-readable ABI item\n *\n * @param abiItem - ABI item\n * @returns Human-readable ABI item\n */\nexport function formatAbiItem<const abiItem extends Abi[number]>(\n  abiItem: abiItem,\n): FormatAbiItem<abiItem> {\n  type Result = FormatAbiItem<abiItem>\n  type Params = readonly [\n    AbiParameter | AbiEventParameter,\n    ...(readonly (AbiParameter | AbiEventParameter)[]),\n  ]\n\n  if (abiItem.type === 'function')\n    return `function ${abiItem.name}(${formatAbiParameters(\n      abiItem.inputs as Params,\n    )})${\n      abiItem.stateMutability && abiItem.stateMutability !== 'nonpayable'\n        ? ` ${abiItem.stateMutability}`\n        : ''\n    }${\n      abiItem.outputs?.length\n        ? ` returns (${formatAbiParameters(abiItem.outputs as Params)})`\n        : ''\n    }`\n  if (abiItem.type === 'event')\n    return `event ${abiItem.name}(${formatAbiParameters(\n      abiItem.inputs as Params,\n    )})`\n  if (abiItem.type === 'error')\n    return `error ${abiItem.name}(${formatAbiParameters(\n      abiItem.inputs as Params,\n    )})`\n  if (abiItem.type === 'constructor')\n    return `constructor(${formatAbiParameters(abiItem.inputs as Params)})${\n      abiItem.stateMutability === 'payable' ? ' payable' : ''\n    }`\n  if (abiItem.type === 'fallback')\n    return `fallback() external${\n      abiItem.stateMutability === 'payable' ? ' payable' : ''\n    }` as Result\n  return 'receive() external payable' as Result\n}\n"], "mappings": ";AAAO,IAAM,UAAU;;;ACUjB,IAAO,YAAP,MAAO,mBAAkB,MAAK;EAQlC,YAAY,cAAsB,OAAsB,CAAA,GAAE;AAjB5D;AAkBI,UAAM,UACJ,KAAK,iBAAiB,aAClB,KAAK,MAAM,YACX,UAAK,UAAL,mBAAY,WACV,KAAK,MAAM,UACX,KAAK;AACb,UAAM,WACJ,KAAK,iBAAiB,aAClB,KAAK,MAAM,YAAY,KAAK,WAC5B,KAAK;AACX,UAAM,UAAU;MACd,gBAAgB;MAChB;MACA,GAAI,KAAK,eAAe,CAAC,GAAG,KAAK,cAAc,EAAE,IAAI,CAAA;MACrD,GAAI,WAAW,CAAC,4BAA4B,QAAQ,EAAE,IAAI,CAAA;MAC1D,GAAI,UAAU,CAAC,YAAY,OAAO,EAAE,IAAI,CAAA;MACxC,oBAAoB,OAAO;MAC3B,KAAK,IAAI;AAEX,UAAM,OAAO;AA3Bf,WAAA,eAAA,MAAA,WAAA;;;;;;AACA,WAAA,eAAA,MAAA,YAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AACA,WAAA,eAAA,MAAA,gBAAA;;;;;;AAES,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;AAwBd,QAAI,KAAK;AAAO,WAAK,QAAQ,KAAK;AAClC,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,eAAe,KAAK;AACzB,SAAK,eAAe;EACtB;;;;AC3CI,IAAO,sBAAP,cAAmC,UAAS;EAGhD,YAAY,EAAE,UAAS,GAAkC;AACvD,UAAM,6BAA6B;MACjC,SAAS,gBAAgB,KAAK,UAAU,WAAW,MAAM,CAAC,CAAC;MAC3D,UAAU;KACX;AANM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOhB;;AAGI,IAAO,mBAAP,cAAgC,UAAS;EAG7C,YAAY,EAAE,KAAI,GAAoB;AACpC,UAAM,iBAAiB;MACrB,cAAc;QACZ,SAAS,IAAI;;KAEhB;AAPM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAQhB;;AAGI,IAAO,2BAAP,cAAwC,UAAS;EAGrD,YAAY,EAAE,KAAI,GAAoB;AACpC,UAAM,iBAAiB;MACrB,cAAc,CAAC,SAAS,IAAI,4BAA4B;KACzD;AALM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMhB;;;;AC5BI,IAAO,2BAAP,cAAwC,UAAS;EAGrD,YAAY,EAAE,MAAK,GAA8B;AAC/C,UAAM,kCAAkC;MACtC,SAAS,qBAAqB,KAAK,UAAU,OAAO,MAAM,CAAC,CAAC;MAC5D,UAAU;KACX;AANM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOhB;;AAGI,IAAO,4BAAP,cAAyC,UAAS;EAGtD,YAAY,EAAE,OAAM,GAA+B;AACjD,UAAM,mCAAmC;MACvC,SAAS,sBAAsB,KAAK,UAAU,QAAQ,MAAM,CAAC,CAAC;MAC9D,UAAU;KACX;AANM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOhB;;AAGI,IAAO,wBAAP,cAAqC,UAAS;EAGlD,YAAY,EAAE,MAAK,GAAqB;AACtC,UAAM,0BAA0B;MAC9B,SAAS;KACV;AALM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMhB;;AAGI,IAAO,gCAAP,cAA6C,UAAS;EAG1D,YAAY,EAAE,OAAO,KAAI,GAAmC;AAC1D,UAAM,0BAA0B;MAC9B,SAAS;MACT,cAAc;QACZ,IAAI,IAAI;;KAEX;AARM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAShB;;AAGI,IAAO,uBAAP,cAAoC,UAAS;EAGjD,YAAY,EACV,OACA,MACA,SAAQ,GAKT;AACC,UAAM,0BAA0B;MAC9B,SAAS;MACT,cAAc;QACZ,aAAa,QAAQ,gBACnB,OAAO,QAAQ,IAAI,WAAW,EAChC;;KAEH;AAlBM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAmBhB;;AAGI,IAAO,+BAAP,cAA4C,UAAS;EAGzD,YAAY,EACV,OACA,MACA,SAAQ,GAKT;AACC,UAAM,0BAA0B;MAC9B,SAAS;MACT,cAAc;QACZ,aAAa,QAAQ,gBACnB,OAAO,QAAQ,IAAI,WAAW,EAChC;QACA,iFAAiF,QAAQ;;KAE5F;AAnBM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAoBhB;;AAGI,IAAO,+BAAP,cAA4C,UAAS;EAGzD,YAAY,EACV,aAAY,GAGb;AACC,UAAM,0BAA0B;MAC9B,SAAS,KAAK,UAAU,cAAc,MAAM,CAAC;MAC7C,cAAc,CAAC,gCAAgC;KAChD;AAVM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWhB;;;;ACzGI,IAAO,wBAAP,cAAqC,UAAS;EAGlD,YAAY,EACV,WACA,KAAI,GAIL;AACC,UAAM,WAAW,IAAI,eAAe;MAClC,SAAS;KACV;AAXM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYhB;;AAGI,IAAO,wBAAP,cAAqC,UAAS;EAGlD,YAAY,EAAE,UAAS,GAAyB;AAC9C,UAAM,sBAAsB;MAC1B,SAAS;KACV;AALM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMhB;;AAGI,IAAO,8BAAP,cAA2C,UAAS;EAGxD,YAAY,EAAE,UAAS,GAAyB;AAC9C,UAAM,6BAA6B;MACjC,SAAS;MACT,cAAc,CAAC,sBAAsB;KACtC;AANM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOhB;;;;ACnCI,IAAO,yBAAP,cAAsC,UAAS;EAGnD,YAAY,EAAE,KAAI,GAAoB;AACpC,UAAM,gCAAgC;MACpC,cAAc,CAAC,WAAW,IAAI,4BAA4B;KAC3D;AALM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMhB;;;;ACPI,IAAO,0BAAP,cAAuC,UAAS;EAGpD,YAAY,EAAE,SAAS,MAAK,GAAsC;AAChE,UAAM,2BAA2B;MAC/B,cAAc;QACZ,IAAI,QAAQ,KAAI,CAAE,kBAChB,QAAQ,IAAI,YAAY,SAC1B;;MAEF,SAAS,UAAU,KAAK;KACzB;AAVM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWhB;;;;ACZI,SAAU,UAAgB,OAAe,QAAc;AAC3D,QAAM,QAAQ,MAAM,KAAK,MAAM;AAC/B,SAAO,+BAAO;AAChB;AAIO,IAAM,aAAa;AAInB,IAAM,eACX;AAEK,IAAM,eAAe;;;ACP5B,IAAM,sBACJ;AACI,SAAU,iBAAiB,WAAiB;AAChD,SAAO,oBAAoB,KAAK,SAAS;AAC3C;AACM,SAAU,mBAAmB,WAAiB;AAClD,SAAO,UACL,qBACA,SAAS;AAEb;AAGA,IAAM,sBACJ;AACI,SAAU,iBAAiB,WAAiB;AAChD,SAAO,oBAAoB,KAAK,SAAS;AAC3C;AACM,SAAU,mBAAmB,WAAiB;AAClD,SAAO,UACL,qBACA,SAAS;AAEb;AAGA,IAAM,yBACJ;AACI,SAAU,oBAAoB,WAAiB;AACnD,SAAO,uBAAuB,KAAK,SAAS;AAC9C;AACM,SAAU,sBAAsB,WAAiB;AACrD,SAAO,UAKJ,wBAAwB,SAAS;AACtC;AAGA,IAAM,uBACJ;AACI,SAAU,kBAAkB,WAAiB;AACjD,SAAO,qBAAqB,KAAK,SAAS;AAC5C;AACM,SAAU,oBAAoB,WAAiB;AACnD,SAAO,UACL,sBACA,SAAS;AAEb;AAGA,IAAM,4BACJ;AACI,SAAU,uBAAuB,WAAiB;AACtD,SAAO,0BAA0B,KAAK,SAAS;AACjD;AACM,SAAU,yBAAyB,WAAiB;AACxD,SAAO,UAGJ,2BAA2B,SAAS;AACzC;AAGA,IAAM,yBACJ;AACI,SAAU,oBAAoB,WAAiB;AACnD,SAAO,uBAAuB,KAAK,SAAS;AAC9C;AACM,SAAU,sBAAsB,WAAiB;AACrD,SAAO,UAGJ,wBAAwB,SAAS;AACtC;AAGA,IAAM,wBAAwB;AACxB,SAAU,mBAAmB,WAAiB;AAClD,SAAO,sBAAsB,KAAK,SAAS;AAC7C;AAEO,IAAM,YAAY,oBAAI,IAAc;EACzC;EACA;EACA;EACA;CACD;AACM,IAAM,iBAAiB,oBAAI,IAAmB,CAAC,SAAS,CAAC;AACzD,IAAM,oBAAoB,oBAAI,IAAsB;EACzD;EACA;EACA;CACD;;;AChGK,SAAU,qBACd,OACA,MACA,SAAsB;AAEtB,MAAI,YAAY;AAChB,MAAI;AACF,eAAW,UAAU,OAAO,QAAQ,OAAO,GAAG;AAC5C,UAAI,CAAC;AAAQ;AACb,UAAI,cAAc;AAClB,iBAAW,YAAY,OAAO,CAAC,GAAG;AAChC,uBAAe,IAAI,SAAS,IAAI,GAAG,SAAS,OAAO,IAAI,SAAS,IAAI,KAAK,EAAE;MAC7E;AACA,mBAAa,IAAI,OAAO,CAAC,CAAC,IAAI,WAAW;IAC3C;AACF,MAAI;AAAM,WAAO,GAAG,IAAI,IAAI,KAAK,GAAG,SAAS;AAC7C,SAAO;AACT;AAOO,IAAM,iBAAiB,oBAAI,IAGhC;;EAEA,CAAC,WAAW,EAAE,MAAM,UAAS,CAAE;EAC/B,CAAC,QAAQ,EAAE,MAAM,OAAM,CAAE;EACzB,CAAC,SAAS,EAAE,MAAM,QAAO,CAAE;EAC3B,CAAC,WAAW,EAAE,MAAM,UAAS,CAAE;EAC/B,CAAC,OAAO,EAAE,MAAM,SAAQ,CAAE;EAC1B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,QAAQ,EAAE,MAAM,UAAS,CAAE;EAC5B,CAAC,SAAS,EAAE,MAAM,QAAO,CAAE;EAC3B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,UAAU,EAAE,MAAM,SAAQ,CAAE;EAC7B,CAAC,WAAW,EAAE,MAAM,UAAS,CAAE;EAC/B,CAAC,WAAW,EAAE,MAAM,UAAS,CAAE;EAC/B,CAAC,WAAW,EAAE,MAAM,UAAS,CAAE;EAC/B,CAAC,WAAW,EAAE,MAAM,UAAS,CAAE;;EAG/B,CAAC,iBAAiB,EAAE,MAAM,WAAW,MAAM,QAAO,CAAE;EACpD,CAAC,cAAc,EAAE,MAAM,WAAW,MAAM,KAAI,CAAE;EAC9C,CAAC,iBAAiB,EAAE,MAAM,QAAQ,MAAM,WAAU,CAAE;EACpD,CAAC,eAAe,EAAE,MAAM,SAAS,MAAM,QAAO,CAAE;EAChD,CAAC,cAAc,EAAE,MAAM,SAAS,MAAM,OAAM,CAAE;EAC9C,CAAC,mBAAmB,EAAE,MAAM,SAAS,MAAM,YAAW,CAAE;EACxD,CAAC,gBAAgB,EAAE,MAAM,WAAW,MAAM,OAAM,CAAE;EAClD,CAAC,aAAa,EAAE,MAAM,WAAW,MAAM,IAAG,CAAE;EAC5C,CAAC,gBAAgB,EAAE,MAAM,WAAW,MAAM,OAAM,CAAE;EAClD,CAAC,aAAa,EAAE,MAAM,WAAW,MAAM,IAAG,CAAE;EAC5C,CAAC,eAAe,EAAE,MAAM,UAAU,MAAM,OAAM,CAAE;EAChD,CAAC,iBAAiB,EAAE,MAAM,UAAU,MAAM,SAAQ,CAAE;EACpD,CAAC,mBAAmB,EAAE,MAAM,UAAU,MAAM,WAAU,CAAE;EACxD,CAAC,gBAAgB,EAAE,MAAM,WAAW,MAAM,UAAS,CAAE;EACrD,CAAC,WAAW,EAAE,MAAM,SAAS,MAAM,IAAG,CAAE;EACxC,CAAC,mBAAmB,EAAE,MAAM,WAAW,MAAM,UAAS,CAAE;EACxD,CAAC,mBAAmB,EAAE,MAAM,WAAW,MAAM,UAAS,CAAE;EACxD,CAAC,iBAAiB,EAAE,MAAM,WAAW,MAAM,QAAO,CAAE;;EAGpD;IACE;IACA,EAAE,MAAM,WAAW,MAAM,QAAQ,SAAS,KAAI;;EAEhD,CAAC,4BAA4B,EAAE,MAAM,WAAW,MAAM,MAAM,SAAS,KAAI,CAAE;EAC3E;IACE;IACA,EAAE,MAAM,WAAW,MAAM,WAAW,SAAS,KAAI;;EAEnD;IACE;IACA,EAAE,MAAM,WAAW,MAAM,WAAW,SAAS,KAAI;;CAEpD;;;AC9CK,SAAU,eAAe,WAAmB,UAAwB,CAAA,GAAE;AAC1E,MAAI,oBAAoB,SAAS;AAC/B,WAAO,uBAAuB,WAAW,OAAO;AAElD,MAAI,iBAAiB,SAAS;AAC5B,WAAO,oBAAoB,WAAW,OAAO;AAE/C,MAAI,iBAAiB,SAAS;AAC5B,WAAO,oBAAoB,WAAW,OAAO;AAE/C,MAAI,uBAAuB,SAAS;AAClC,WAAO,0BAA0B,WAAW,OAAO;AAErD,MAAI,oBAAoB,SAAS;AAAG,WAAO,uBAAuB,SAAS;AAE3E,MAAI,mBAAmB,SAAS;AAC9B,WAAO;MACL,MAAM;MACN,iBAAiB;;AAGrB,QAAM,IAAI,sBAAsB,EAAE,UAAS,CAAE;AAC/C;AAEM,SAAU,uBACd,WACA,UAAwB,CAAA,GAAE;AAE1B,QAAM,QAAQ,sBAAsB,SAAS;AAC7C,MAAI,CAAC;AAAO,UAAM,IAAI,sBAAsB,EAAE,WAAW,MAAM,WAAU,CAAE;AAE3E,QAAM,cAAc,gBAAgB,MAAM,UAAU;AACpD,QAAM,SAAS,CAAA;AACf,QAAM,cAAc,YAAY;AAChC,WAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,WAAO,KACL,kBAAkB,YAAY,CAAC,GAAI;MACjC,WAAW;MACX;MACA,MAAM;KACP,CAAC;EAEN;AAEA,QAAM,UAAU,CAAA;AAChB,MAAI,MAAM,SAAS;AACjB,UAAM,eAAe,gBAAgB,MAAM,OAAO;AAClD,UAAM,eAAe,aAAa;AAClC,aAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACrC,cAAQ,KACN,kBAAkB,aAAa,CAAC,GAAI;QAClC,WAAW;QACX;QACA,MAAM;OACP,CAAC;IAEN;EACF;AAEA,SAAO;IACL,MAAM,MAAM;IACZ,MAAM;IACN,iBAAiB,MAAM,mBAAmB;IAC1C;IACA;;AAEJ;AAEM,SAAU,oBACd,WACA,UAAwB,CAAA,GAAE;AAE1B,QAAM,QAAQ,mBAAmB,SAAS;AAC1C,MAAI,CAAC;AAAO,UAAM,IAAI,sBAAsB,EAAE,WAAW,MAAM,QAAO,CAAE;AAExE,QAAM,SAAS,gBAAgB,MAAM,UAAU;AAC/C,QAAM,gBAAgB,CAAA;AACtB,QAAM,SAAS,OAAO;AACtB,WAAS,IAAI,GAAG,IAAI,QAAQ;AAC1B,kBAAc,KACZ,kBAAkB,OAAO,CAAC,GAAI;MAC5B,WAAW;MACX;MACA,MAAM;KACP,CAAC;AAEN,SAAO,EAAE,MAAM,MAAM,MAAM,MAAM,SAAS,QAAQ,cAAa;AACjE;AAEM,SAAU,oBACd,WACA,UAAwB,CAAA,GAAE;AAE1B,QAAM,QAAQ,mBAAmB,SAAS;AAC1C,MAAI,CAAC;AAAO,UAAM,IAAI,sBAAsB,EAAE,WAAW,MAAM,QAAO,CAAE;AAExE,QAAM,SAAS,gBAAgB,MAAM,UAAU;AAC/C,QAAM,gBAAgB,CAAA;AACtB,QAAM,SAAS,OAAO;AACtB,WAAS,IAAI,GAAG,IAAI,QAAQ;AAC1B,kBAAc,KACZ,kBAAkB,OAAO,CAAC,GAAI,EAAE,SAAS,MAAM,QAAO,CAAE,CAAC;AAE7D,SAAO,EAAE,MAAM,MAAM,MAAM,MAAM,SAAS,QAAQ,cAAa;AACjE;AAEM,SAAU,0BACd,WACA,UAAwB,CAAA,GAAE;AAE1B,QAAM,QAAQ,yBAAyB,SAAS;AAChD,MAAI,CAAC;AACH,UAAM,IAAI,sBAAsB,EAAE,WAAW,MAAM,cAAa,CAAE;AAEpE,QAAM,SAAS,gBAAgB,MAAM,UAAU;AAC/C,QAAM,gBAAgB,CAAA;AACtB,QAAM,SAAS,OAAO;AACtB,WAAS,IAAI,GAAG,IAAI,QAAQ;AAC1B,kBAAc,KACZ,kBAAkB,OAAO,CAAC,GAAI,EAAE,SAAS,MAAM,cAAa,CAAE,CAAC;AAEnE,SAAO;IACL,MAAM;IACN,iBAAiB,MAAM,mBAAmB;IAC1C,QAAQ;;AAEZ;AAEM,SAAU,uBAAuB,WAAiB;AACtD,QAAM,QAAQ,sBAAsB,SAAS;AAC7C,MAAI,CAAC;AAAO,UAAM,IAAI,sBAAsB,EAAE,WAAW,MAAM,WAAU,CAAE;AAE3E,SAAO;IACL,MAAM;IACN,iBAAiB,MAAM,mBAAmB;;AAE9C;AAEA,IAAM,gCACJ;AACF,IAAM,6BACJ;AACF,IAAM,sBAAsB;AAQtB,SAAU,kBAAkB,OAAe,SAAsB;AA3LvE;AA6LE,QAAM,oBAAoB,qBACxB,OACA,mCAAS,MACT,mCAAS,OAAO;AAElB,MAAI,eAAe,IAAI,iBAAiB;AACtC,WAAO,eAAe,IAAI,iBAAiB;AAE7C,QAAM,UAAU,aAAa,KAAK,KAAK;AACvC,QAAM,QAAQ,UAMZ,UAAU,6BAA6B,+BACvC,KAAK;AAEP,MAAI,CAAC;AAAO,UAAM,IAAI,sBAAsB,EAAE,MAAK,CAAE;AAErD,MAAI,MAAM,QAAQ,kBAAkB,MAAM,IAAI;AAC5C,UAAM,IAAI,8BAA8B,EAAE,OAAO,MAAM,MAAM,KAAI,CAAE;AAErE,QAAM,OAAO,MAAM,OAAO,EAAE,MAAM,MAAM,KAAI,IAAK,CAAA;AACjD,QAAM,UAAU,MAAM,aAAa,YAAY,EAAE,SAAS,KAAI,IAAK,CAAA;AACnE,QAAM,WAAU,mCAAS,YAAW,CAAA;AACpC,MAAI;AACJ,MAAI,aAAa,CAAA;AACjB,MAAI,SAAS;AACX,WAAO;AACP,UAAM,SAAS,gBAAgB,MAAM,IAAI;AACzC,UAAM,cAAc,CAAA;AACpB,UAAM,SAAS,OAAO;AACtB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAE/B,kBAAY,KAAK,kBAAkB,OAAO,CAAC,GAAI,EAAE,QAAO,CAAE,CAAC;IAC7D;AACA,iBAAa,EAAE,YAAY,YAAW;EACxC,WAAW,MAAM,QAAQ,SAAS;AAChC,WAAO;AACP,iBAAa,EAAE,YAAY,QAAQ,MAAM,IAAI,EAAC;EAChD,WAAW,oBAAoB,KAAK,MAAM,IAAI,GAAG;AAC/C,WAAO,GAAG,MAAM,IAAI;EACtB,OAAO;AACL,WAAO,MAAM;AACb,QAAI,GAAE,mCAAS,UAAS,aAAa,CAAC,eAAe,IAAI;AACvD,YAAM,IAAI,yBAAyB,EAAE,KAAI,CAAE;EAC/C;AAEA,MAAI,MAAM,UAAU;AAElB,QAAI,GAAC,8CAAS,cAAT,mBAAoB,QAApB,4BAA0B,MAAM;AACnC,YAAM,IAAI,qBAAqB;QAC7B;QACA,MAAM,mCAAS;QACf,UAAU,MAAM;OACjB;AAGH,QACE,kBAAkB,IAAI,MAAM,QAA4B,KACxD,CAAC,oBAAoB,MAAM,CAAC,CAAC,MAAM,KAAK;AAExC,YAAM,IAAI,6BAA6B;QACrC;QACA,MAAM,mCAAS;QACf,UAAU,MAAM;OACjB;EACL;AAEA,QAAM,eAAe;IACnB,MAAM,GAAG,IAAI,GAAG,MAAM,SAAS,EAAE;IACjC,GAAG;IACH,GAAG;IACH,GAAG;;AAEL,iBAAe,IAAI,mBAAmB,YAAY;AAClD,SAAO;AACT;AAGM,SAAU,gBACd,QACA,SAAmB,CAAA,GACnB,UAAU,IACV,QAAQ,GAAC;AAET,QAAM,SAAS,OAAO,KAAI,EAAG;AAE7B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAM,OAAO,OAAO,CAAC;AACrB,UAAM,OAAO,OAAO,MAAM,IAAI,CAAC;AAC/B,YAAQ,MAAM;MACZ,KAAK;AACH,eAAO,UAAU,IACb,gBAAgB,MAAM,CAAC,GAAG,QAAQ,QAAQ,KAAI,CAAE,CAAC,IACjD,gBAAgB,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,IAAI,KAAK;MAC9D,KAAK;AACH,eAAO,gBAAgB,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,IAAI,QAAQ,CAAC;MACrE,KAAK;AACH,eAAO,gBAAgB,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,IAAI,QAAQ,CAAC;MACrE;AACE,eAAO,gBAAgB,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,IAAI,KAAK;IACnE;EACF;AAEA,MAAI,YAAY;AAAI,WAAO;AAC3B,MAAI,UAAU;AAAG,UAAM,IAAI,wBAAwB,EAAE,SAAS,MAAK,CAAE;AAErE,SAAO,KAAK,QAAQ,KAAI,CAAE;AAC1B,SAAO;AACT;AAEM,SAAU,eACd,MAAY;AAEZ,SACE,SAAS,aACT,SAAS,UACT,SAAS,cACT,SAAS,YACT,WAAW,KAAK,IAAI,KACpB,aAAa,KAAK,IAAI;AAE1B;AAEA,IAAM,yBACJ;AAGI,SAAU,kBAAkB,MAAY;AAC5C,SACE,SAAS,aACT,SAAS,UACT,SAAS,cACT,SAAS,YACT,SAAS,WACT,WAAW,KAAK,IAAI,KACpB,aAAa,KAAK,IAAI,KACtB,uBAAuB,KAAK,IAAI;AAEpC;AAGM,SAAU,oBACd,MACA,SAAgB;AAKhB,SAAO,WAAW,SAAS,WAAW,SAAS,YAAY,SAAS;AACtE;;;AChVM,SAAU,aAAa,YAA6B;AAExD,QAAM,iBAA+B,CAAA;AACrC,QAAM,mBAAmB,WAAW;AACpC,WAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACzC,UAAM,YAAY,WAAW,CAAC;AAC9B,QAAI,CAAC,kBAAkB,SAAS;AAAG;AAEnC,UAAM,QAAQ,oBAAoB,SAAS;AAC3C,QAAI,CAAC;AAAO,YAAM,IAAI,sBAAsB,EAAE,WAAW,MAAM,SAAQ,CAAE;AAEzE,UAAM,aAAa,MAAM,WAAW,MAAM,GAAG;AAE7C,UAAM,aAA6B,CAAA;AACnC,UAAM,mBAAmB,WAAW;AACpC,aAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACzC,YAAM,WAAW,WAAW,CAAC;AAC7B,YAAM,UAAU,SAAS,KAAI;AAC7B,UAAI,CAAC;AAAS;AACd,YAAM,eAAe,kBAAkB,SAAS;QAC9C,MAAM;OACP;AACD,iBAAW,KAAK,YAAY;IAC9B;AAEA,QAAI,CAAC,WAAW;AAAQ,YAAM,IAAI,4BAA4B,EAAE,UAAS,CAAE;AAC3E,mBAAe,MAAM,IAAI,IAAI;EAC/B;AAGA,QAAM,kBAAgC,CAAA;AACtC,QAAM,UAAU,OAAO,QAAQ,cAAc;AAC7C,QAAM,gBAAgB,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,UAAM,CAAC,MAAM,UAAU,IAAI,QAAQ,CAAC;AACpC,oBAAgB,IAAI,IAAI,eAAe,YAAY,cAAc;EACnE;AAEA,SAAO;AACT;AAEA,IAAM,wBACJ;AAEF,SAAS,eACP,eACA,SACA,YAAY,oBAAI,IAAG,GAAU;AAE7B,QAAM,aAA6B,CAAA;AACnC,QAAM,SAAS,cAAc;AAC7B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAM,eAAe,cAAc,CAAC;AACpC,UAAM,UAAU,aAAa,KAAK,aAAa,IAAI;AACnD,QAAI;AAAS,iBAAW,KAAK,YAAY;SACpC;AACH,YAAM,QAAQ,UACZ,uBACA,aAAa,IAAI;AAEnB,UAAI,EAAC,+BAAO;AAAM,cAAM,IAAI,6BAA6B,EAAE,aAAY,CAAE;AAEzE,YAAM,EAAE,OAAO,KAAI,IAAK;AACxB,UAAI,QAAQ,SAAS;AACnB,YAAI,UAAU,IAAI,IAAI;AAAG,gBAAM,IAAI,uBAAuB,EAAE,KAAI,CAAE;AAElE,mBAAW,KAAK;UACd,GAAG;UACH,MAAM,QAAQ,SAAS,EAAE;UACzB,YAAY,eACV,QAAQ,IAAI,KAAK,CAAA,GACjB,SACA,oBAAI,IAAI,CAAC,GAAG,WAAW,IAAI,CAAC,CAAC;SAEhC;MACH,OAAO;AACL,YAAI,eAAe,IAAI;AAAG,qBAAW,KAAK,YAAY;;AACjD,gBAAM,IAAI,iBAAiB,EAAE,KAAI,CAAE;MAC1C;IACF;EACF;AAEA,SAAO;AACT;;;ACtCM,SAAU,SACd,YAI4B;AAE5B,QAAM,UAAU,aAAa,UAA+B;AAC5D,QAAM,MAAM,CAAA;AACZ,QAAM,SAAS,WAAW;AAC1B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAM,YAAa,WAAiC,CAAC;AACrD,QAAI,kBAAkB,SAAS;AAAG;AAClC,QAAI,KAAK,eAAe,WAAW,OAAO,CAAC;EAC7C;AACA,SAAO;AACT;;;ACEM,SAAU,aAGd,WAcG;AAEH,MAAI;AACJ,MAAI,OAAO,cAAc;AACvB,cAAU,eAAe,SAAS;OAC/B;AACH,UAAM,UAAU,aAAa,SAA8B;AAC3D,UAAM,SAAS,UAAU;AACzB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAM,aAAc,UAAgC,CAAC;AACrD,UAAI,kBAAkB,UAAU;AAAG;AACnC,gBAAU,eAAe,YAAY,OAAO;AAC5C;IACF;EACF;AAEA,MAAI,CAAC;AAAS,UAAM,IAAI,oBAAoB,EAAE,UAAS,CAAE;AACzD,SAAO;AACT;;;AClCM,SAAUA,mBAGd,OAcG;AAEH,MAAI;AACJ,MAAI,OAAO,UAAU;AACnB,mBAAe,kBAAmB,OAAO;MACvC;KACD;OACE;AACH,UAAM,UAAU,aAAa,KAA0B;AACvD,UAAM,SAAS,MAAM;AACrB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAM,YAAa,MAA4B,CAAC;AAChD,UAAI,kBAAkB,SAAS;AAAG;AAClC,qBAAe,kBAAmB,WAAW,EAAE,WAAW,QAAO,CAAE;AACnE;IACF;EACF;AAEA,MAAI,CAAC;AAAc,UAAM,IAAI,yBAAyB,EAAE,MAAK,CAAE;AAE/D,SAAO;AACT;;;ACdM,SAAU,mBAGd,QAcG;AAEH,QAAM,gBAAgC,CAAA;AACtC,MAAI,OAAO,WAAW,UAAU;AAC9B,UAAM,aAAa,gBAAgB,MAAM;AACzC,UAAM,SAAS,WAAW;AAC1B,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,oBAAc,KAAK,kBAAmB,WAAW,CAAC,GAAI,EAAE,UAAS,CAAE,CAAC;IACtE;EACF,OAAO;AACL,UAAM,UAAU,aAAa,MAA2B;AACxD,UAAM,SAAS,OAAO;AACtB,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAM,YAAa,OAA6B,CAAC;AACjD,UAAI,kBAAkB,SAAS;AAAG;AAClC,YAAM,aAAa,gBAAgB,SAAS;AAC5C,YAAMC,UAAS,WAAW;AAC1B,eAAS,IAAI,GAAG,IAAIA,SAAQ,KAAK;AAC/B,sBAAc,KACZ,kBAAmB,WAAW,CAAC,GAAI,EAAE,WAAW,QAAO,CAAE,CAAC;MAE9D;IACF;EACF;AAEA,MAAI,cAAc,WAAW;AAC3B,UAAM,IAAI,0BAA0B,EAAE,OAAM,CAAE;AAEhD,SAAO;AACT;;;AC7FA,IAAM,aAAa;AAYb,SAAU,mBAEd,cAA0B;AAG1B,MAAI,OAAO,aAAa;AACxB,MAAI,WAAW,KAAK,aAAa,IAAI,KAAK,gBAAgB,cAAc;AACtE,WAAO;AACP,UAAM,SAAS,aAAa,WAAW;AACvC,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,YAAM,YAAY,aAAa,WAAW,CAAC;AAC3C,cAAQ,mBAAmB,SAAS;AACpC,UAAI,IAAI,SAAS;AAAG,gBAAQ;IAC9B;AACA,UAAM,SAAS,UAA8B,YAAY,aAAa,IAAI;AAC1E,YAAQ,KAAI,iCAAQ,UAAS,EAAE;AAC/B,WAAO,mBAAmB;MACxB,GAAG;MACH;KACD;EACH;AAEA,MAAI,aAAa,gBAAgB,aAAa;AAC5C,WAAO,GAAG,IAAI;AAEhB,MAAI,aAAa;AAAM,WAAO,GAAG,IAAI,IAAI,aAAa,IAAI;AAC1D,SAAO;AACT;;;AChDM,SAAU,oBAKd,eAA4B;AAC5B,MAAI,SAAS;AACb,QAAM,SAAS,cAAc;AAC7B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAM,eAAe,cAAc,CAAC;AACpC,cAAU,mBAAmB,YAAY;AACzC,QAAI,MAAM,SAAS;AAAG,gBAAU;EAClC;AACA,SAAO;AACT;;;ACsCM,SAAU,cACd,SAAgB;AAtFlB;AA8FE,MAAI,QAAQ,SAAS;AACnB,WAAO,YAAY,QAAQ,IAAI,IAAI,oBACjC,QAAQ,MAAgB,CACzB,IACC,QAAQ,mBAAmB,QAAQ,oBAAoB,eACnD,IAAI,QAAQ,eAAe,KAC3B,EACN,KACE,aAAQ,YAAR,mBAAiB,UACb,aAAa,oBAAoB,QAAQ,OAAiB,CAAC,MAC3D,EACN;AACF,MAAI,QAAQ,SAAS;AACnB,WAAO,SAAS,QAAQ,IAAI,IAAI,oBAC9B,QAAQ,MAAgB,CACzB;AACH,MAAI,QAAQ,SAAS;AACnB,WAAO,SAAS,QAAQ,IAAI,IAAI,oBAC9B,QAAQ,MAAgB,CACzB;AACH,MAAI,QAAQ,SAAS;AACnB,WAAO,eAAe,oBAAoB,QAAQ,MAAgB,CAAC,IACjE,QAAQ,oBAAoB,YAAY,aAAa,EACvD;AACF,MAAI,QAAQ,SAAS;AACnB,WAAO,sBACL,QAAQ,oBAAoB,YAAY,aAAa,EACvD;AACF,SAAO;AACT;", "names": ["parseAbiParameter", "length"]}