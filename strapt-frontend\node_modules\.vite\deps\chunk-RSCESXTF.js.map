{"version": 3, "sources": ["../../preact/src/constants.js", "../../preact/src/util.js", "../../preact/src/options.js", "../../preact/src/create-element.js", "../../preact/src/component.js", "../../preact/src/diff/props.js", "../../preact/src/create-context.js", "../../preact/src/diff/children.js", "../../preact/src/diff/index.js", "../../preact/src/render.js", "../../preact/src/clone-element.js", "../../preact/src/diff/catch-error.js", "../../preact/hooks/src/index.js"], "sourcesContent": ["/** Normal hydration that attaches to a DOM tree but does not diff it. */\nexport const MODE_HYDRATE = 1 << 5;\n/** Signifies this VNode suspended on the previous render */\nexport const MODE_SUSPENDED = 1 << 7;\n/** Indicates that this node needs to be inserted while patching children */\nexport const INSERT_VNODE = 1 << 2;\n/** Indicates a VNode has been matched with another VNode in the diff */\nexport const MATCHED = 1 << 1;\n\n/** Reset all mode flags */\nexport const RESET_MODE = ~(MODE_HYDRATE | MODE_SUSPENDED);\n\nexport const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\nexport const XHTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\nexport const MATH_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n\nexport const NULL = null;\nexport const UNDEFINED = undefined;\nexport const EMPTY_OBJ = /** @type {any} */ ({});\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL =\n\t/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { EMPTY_ARR } from './constants';\n\nexport const isArray = Array.isArray;\n\n/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\t// @ts-expect-error We change the type of `obj` to be `O & P`\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Remove a child node from its parent if attached. This is a workaround for\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\n * is smaller than including a dedicated polyfill.\n * @param {import('./index').ContainerNode} node The node to remove\n */\nexport function removeNode(node) {\n\tif (node && node.parentNode) node.parentNode.removeChild(node);\n}\n\nexport const slice = EMPTY_ARR.slice;\n", "import { _catchError } from './diff/catch-error';\n\n/**\n * The `option` object can potentially contain callback functions\n * that are called during various stages of our renderer. This is the\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\n * for a full list of available option hooks (most editors/IDEs allow you to\n * ctrl+click or cmd+click on mac the type definition below).\n * @type {import('./internal').Options}\n */\nconst options = {\n\t_catchError\n};\n\nexport default options;\n", "import { slice } from './util';\nimport options from './options';\nimport { NULL, UNDEFINED } from './constants';\n\nlet vnodeId = 0;\n\n/**\n * Create an virtual node (used for JSX)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component constructor for this\n * virtual node\n * @param {object | null | undefined} [props] The properties of the virtual node\n * @param {Array<import('.').ComponentChildren>} [children] The children of the\n * virtual node\n * @returns {import('./internal').VNode}\n */\nexport function createElement(type, props, children) {\n\tlet normalizedProps = {},\n\t\tkey,\n\t\tref,\n\t\ti;\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse normalizedProps[i] = props[i];\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\t// If a Component VNode, check for and apply defaultProps\n\t// Note: type may be undefined in development, must never error here.\n\tif (typeof type == 'function' && type.defaultProps != NULL) {\n\t\tfor (i in type.defaultProps) {\n\t\t\tif (normalizedProps[i] == UNDEFINED) {\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn createVNode(type, normalizedProps, key, ref, NULL);\n}\n\n/**\n * Create a VNode (used internally by Preact)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\n * Constructor for this virtual node\n * @param {object | string | number | null} props The properties of this virtual node.\n * If this virtual node represents a text node, this is the text of the node (string or number).\n * @param {string | number | null} key The key for this virtual node, used when\n * diffing it against its children\n * @param {import('./internal').VNode[\"ref\"]} ref The ref property that will\n * receive a reference to its created child\n * @returns {import('./internal').VNode}\n */\nexport function createVNode(type, props, key, ref, original) {\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\n\t// Do not inline into createElement and coerceToVNode!\n\t/** @type {import('./internal').VNode} */\n\tconst vnode = {\n\t\ttype,\n\t\tprops,\n\t\tkey,\n\t\tref,\n\t\t_children: NULL,\n\t\t_parent: NULL,\n\t\t_depth: 0,\n\t\t_dom: NULL,\n\t\t_component: NULL,\n\t\tconstructor: UNDEFINED,\n\t\t_original: original == NULL ? ++vnodeId : original,\n\t\t_index: -1,\n\t\t_flags: 0\n\t};\n\n\t// Only invoke the vnode hook if this was *not* a direct copy:\n\tif (original == NULL && options.vnode != NULL) options.vnode(vnode);\n\n\treturn vnode;\n}\n\nexport function createRef() {\n\treturn { current: NULL };\n}\n\nexport function Fragment(props) {\n\treturn props.children;\n}\n\n/**\n * Check if a the argument is a valid Preact VNode.\n * @param {*} vnode\n * @returns {vnode is VNode}\n */\nexport const isValidElement = vnode =>\n\tvnode != NULL && vnode.constructor == UNDEFINED;\n", "import { assign } from './util';\nimport { diff, commitRoot } from './diff/index';\nimport options from './options';\nimport { Fragment } from './create-element';\nimport { MODE_HYDRATE, NULL } from './constants';\n\n/**\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\n * trigger rendering\n * @param {object} props The initial component props\n * @param {object} context The initial context from parent components'\n * getChildContext\n */\nexport function BaseComponent(props, context) {\n\tthis.props = props;\n\tthis.context = context;\n}\n\n/**\n * Update component state and schedule a re-render.\n * @this {import('./internal').Component}\n * @param {object | ((s: object, p: object) => object)} update A hash of state\n * properties to update with new values or a function that given the current\n * state and props returns a new partial state\n * @param {() => void} [callback] A function to be called once component state is\n * updated\n */\nBaseComponent.prototype.setState = function (update, callback) {\n\t// only clone state when copying to nextState the first time.\n\tlet s;\n\tif (this._nextState != NULL && this._nextState != this.state) {\n\t\ts = this._nextState;\n\t} else {\n\t\ts = this._nextState = assign({}, this.state);\n\t}\n\n\tif (typeof update == 'function') {\n\t\t// Some libraries like `immer` mark the current state as readonly,\n\t\t// preventing us from mutating it, so we need to clone it. See #2716\n\t\tupdate = update(assign({}, s), this.props);\n\t}\n\n\tif (update) {\n\t\tassign(s, update);\n\t}\n\n\t// Skip update if updater function returned null\n\tif (update == NULL) return;\n\n\tif (this._vnode) {\n\t\tif (callback) {\n\t\t\tthis._stateCallbacks.push(callback);\n\t\t}\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Immediately perform a synchronous re-render of the component\n * @this {import('./internal').Component}\n * @param {() => void} [callback] A function to be called after component is\n * re-rendered\n */\nBaseComponent.prototype.forceUpdate = function (callback) {\n\tif (this._vnode) {\n\t\t// Set render mode so that we can differentiate where the render request\n\t\t// is coming from. We need this because forceUpdate should never call\n\t\t// shouldComponentUpdate\n\t\tthis._force = true;\n\t\tif (callback) this._renderCallbacks.push(callback);\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\n * Virtual DOM is generally constructed via [JSX](https://jasonformat.com/wtf-is-jsx).\n * @param {object} props Props (eg: JSX attributes) received from parent\n * element/component\n * @param {object} state The component's current state\n * @param {object} context Context object, as returned by the nearest\n * ancestor's `getChildContext()`\n * @returns {ComponentChildren | void}\n */\nBaseComponent.prototype.render = Fragment;\n\n/**\n * @param {import('./internal').VNode} vnode\n * @param {number | null} [childIndex]\n */\nexport function getDomSibling(vnode, childIndex) {\n\tif (childIndex == NULL) {\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\n\t\treturn vnode._parent\n\t\t\t? getDomSibling(vnode._parent, vnode._index + 1)\n\t\t\t: NULL;\n\t}\n\n\tlet sibling;\n\tfor (; childIndex < vnode._children.length; childIndex++) {\n\t\tsibling = vnode._children[childIndex];\n\n\t\tif (sibling != NULL && sibling._dom != NULL) {\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\n\t\t\treturn sibling._dom;\n\t\t}\n\t}\n\n\t// If we get here, we have not found a DOM node in this vnode's children.\n\t// We must resume from this vnode's sibling (in it's parent _children array)\n\t// Only climb up and search the parent if we aren't searching through a DOM\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\n\t// the search)\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : NULL;\n}\n\n/**\n * Trigger in-place re-rendering of a component.\n * @param {import('./internal').Component} component The component to rerender\n */\nfunction renderComponent(component) {\n\tlet oldVNode = component._vnode,\n\t\toldDom = oldVNode._dom,\n\t\tcommitQueue = [],\n\t\trefQueue = [];\n\n\tif (component._parentDom) {\n\t\tconst newVNode = assign({}, oldVNode);\n\t\tnewVNode._original = oldVNode._original + 1;\n\t\tif (options.vnode) options.vnode(newVNode);\n\n\t\tdiff(\n\t\t\tcomponent._parentDom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tcomponent._globalContext,\n\t\t\tcomponent._parentDom.namespaceURI,\n\t\t\toldVNode._flags & MODE_HYDRATE ? [oldDom] : NULL,\n\t\t\tcommitQueue,\n\t\t\toldDom == NULL ? getDomSibling(oldVNode) : oldDom,\n\t\t\t!!(oldVNode._flags & MODE_HYDRATE),\n\t\t\trefQueue\n\t\t);\n\n\t\tnewVNode._original = oldVNode._original;\n\t\tnewVNode._parent._children[newVNode._index] = newVNode;\n\t\tcommitRoot(commitQueue, newVNode, refQueue);\n\n\t\tif (newVNode._dom != oldDom) {\n\t\t\tupdateParentDomPointers(newVNode);\n\t\t}\n\t}\n}\n\n/**\n * @param {import('./internal').VNode} vnode\n */\nfunction updateParentDomPointers(vnode) {\n\tif ((vnode = vnode._parent) != NULL && vnode._component != NULL) {\n\t\tvnode._dom = vnode._component.base = NULL;\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\tlet child = vnode._children[i];\n\t\t\tif (child != NULL && child._dom != NULL) {\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn updateParentDomPointers(vnode);\n\t}\n}\n\n/**\n * The render queue\n * @type {Array<import('./internal').Component>}\n */\nlet rerenderQueue = [];\n\n/*\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\n * do, and when their effects will be applied. See the links below for some further reading on designing\n * asynchronous APIs.\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\n */\n\nlet prevDebounce;\n\nconst defer =\n\ttypeof Promise == 'function'\n\t\t? Promise.prototype.then.bind(Promise.resolve())\n\t\t: setTimeout;\n\n/**\n * Enqueue a rerender of a component\n * @param {import('./internal').Component} c The component to rerender\n */\nexport function enqueueRender(c) {\n\tif (\n\t\t(!c._dirty &&\n\t\t\t(c._dirty = true) &&\n\t\t\trerenderQueue.push(c) &&\n\t\t\t!process._rerenderCount++) ||\n\t\tprevDebounce != options.debounceRendering\n\t) {\n\t\tprevDebounce = options.debounceRendering;\n\t\t(prevDebounce || defer)(process);\n\t}\n}\n\n/**\n * @param {import('./internal').Component} a\n * @param {import('./internal').Component} b\n */\nconst depthSort = (a, b) => a._vnode._depth - b._vnode._depth;\n\n/** Flush the render queue by rerendering all queued components */\nfunction process() {\n\tlet c,\n\t\tl = 1;\n\n\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\n\t// process() calls from getting scheduled while `queue` is still being consumed.\n\twhile (rerenderQueue.length) {\n\t\t// Keep the rerender queue sorted by (depth, insertion order). The queue\n\t\t// will initially be sorted on the first iteration only if it has more than 1 item.\n\t\t//\n\t\t// New items can be added to the queue e.g. when rerendering a provider, so we want to\n\t\t// keep the order from top to bottom with those new items so we can handle them in a\n\t\t// single pass\n\t\tif (rerenderQueue.length > l) {\n\t\t\trerenderQueue.sort(depthSort);\n\t\t}\n\n\t\tc = rerenderQueue.shift();\n\t\tl = rerenderQueue.length;\n\n\t\tif (c._dirty) {\n\t\t\trenderComponent(c);\n\t\t}\n\t}\n\tprocess._rerenderCount = 0;\n}\n\nprocess._rerenderCount = 0;\n", "import { IS_NON_DIMENSIONAL, NULL, SVG_NAMESPACE } from '../constants';\nimport options from '../options';\n\nfunction setStyle(style, key, value) {\n\tif (key[0] == '-') {\n\t\tstyle.setProperty(key, value == NULL ? '' : value);\n\t} else if (value == NULL) {\n\t\tstyle[key] = '';\n\t} else if (typeof value != 'number' || IS_NON_DIMENSIONAL.test(key)) {\n\t\tstyle[key] = value;\n\t} else {\n\t\tstyle[key] = value + 'px';\n\t}\n}\n\nconst CAPTURE_REGEX = /(PointerCapture)$|Capture$/i;\n\n// A logical clock to solve issues like https://github.com/preactjs/preact/issues/3927.\n// When the DOM performs an event it leaves micro-ticks in between bubbling up which means that\n// an event can trigger on a newly reated DOM-node while the event bubbles up.\n//\n// Originally inspired by Vue\n// (https://github.com/vuejs/core/blob/caeb8a68811a1b0f79/packages/runtime-dom/src/modules/events.ts#L90-L101),\n// but modified to use a logical clock instead of Date.now() in case event handlers get attached\n// and events get dispatched during the same millisecond.\n//\n// The clock is incremented after each new event dispatch. This allows 1 000 000 new events\n// per second for over 280 years before the value reaches Number.MAX_SAFE_INTEGER (2**53 - 1).\nlet eventClock = 0;\n\n/**\n * Set a property value on a DOM node\n * @param {import('../internal').PreactElement} dom The DOM node to modify\n * @param {string} name The name of the property to set\n * @param {*} value The value to set the property to\n * @param {*} oldValue The old value the property had\n * @param {string} namespace Whether or not this DOM node is an SVG node or not\n */\nexport function setProperty(dom, name, value, oldValue, namespace) {\n\tlet useCapture;\n\n\to: if (name == 'style') {\n\t\tif (typeof value == 'string') {\n\t\t\tdom.style.cssText = value;\n\t\t} else {\n\t\t\tif (typeof oldValue == 'string') {\n\t\t\t\tdom.style.cssText = oldValue = '';\n\t\t\t}\n\n\t\t\tif (oldValue) {\n\t\t\t\tfor (name in oldValue) {\n\t\t\t\t\tif (!(value && name in value)) {\n\t\t\t\t\t\tsetStyle(dom.style, name, '');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (value) {\n\t\t\t\tfor (name in value) {\n\t\t\t\t\tif (!oldValue || value[name] != oldValue[name]) {\n\t\t\t\t\t\tsetStyle(dom.style, name, value[name]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\n\telse if (name[0] == 'o' && name[1] == 'n') {\n\t\tuseCapture = name != (name = name.replace(CAPTURE_REGEX, '$1'));\n\n\t\t// Infer correct casing for DOM built-in events:\n\t\tif (\n\t\t\tname.toLowerCase() in dom ||\n\t\t\tname == 'onFocusOut' ||\n\t\t\tname == 'onFocusIn'\n\t\t)\n\t\t\tname = name.toLowerCase().slice(2);\n\t\telse name = name.slice(2);\n\n\t\tif (!dom._listeners) dom._listeners = {};\n\t\tdom._listeners[name + useCapture] = value;\n\n\t\tif (value) {\n\t\t\tif (!oldValue) {\n\t\t\t\tvalue._attached = eventClock;\n\t\t\t\tdom.addEventListener(\n\t\t\t\t\tname,\n\t\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\t\tuseCapture\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tvalue._attached = oldValue._attached;\n\t\t\t}\n\t\t} else {\n\t\t\tdom.removeEventListener(\n\t\t\t\tname,\n\t\t\t\tuseCapture ? eventProxyCapture : eventProxy,\n\t\t\t\tuseCapture\n\t\t\t);\n\t\t}\n\t} else {\n\t\tif (namespace == SVG_NAMESPACE) {\n\t\t\t// Normalize incorrect prop usage for SVG:\n\t\t\t// - xlink:href / xlinkHref --> href (xlink:href was removed from SVG and isn't needed)\n\t\t\t// - className --> class\n\t\t\tname = name.replace(/xlink(H|:h)/, 'h').replace(/sName$/, 's');\n\t\t} else if (\n\t\t\tname != 'width' &&\n\t\t\tname != 'height' &&\n\t\t\tname != 'href' &&\n\t\t\tname != 'list' &&\n\t\t\tname != 'form' &&\n\t\t\t// Default value in browsers is `-1` and an empty string is\n\t\t\t// cast to `0` instead\n\t\t\tname != 'tabIndex' &&\n\t\t\tname != 'download' &&\n\t\t\tname != 'rowSpan' &&\n\t\t\tname != 'colSpan' &&\n\t\t\tname != 'role' &&\n\t\t\tname != 'popover' &&\n\t\t\tname in dom\n\t\t) {\n\t\t\ttry {\n\t\t\t\tdom[name] = value == NULL ? '' : value;\n\t\t\t\t// labelled break is 1b smaller here than a return statement (sorry)\n\t\t\t\tbreak o;\n\t\t\t} catch (e) {}\n\t\t}\n\n\t\t// aria- and data- attributes have no boolean representation.\n\t\t// A `false` value is different from the attribute not being\n\t\t// present, so we can't remove it. For non-boolean aria\n\t\t// attributes we could treat false as a removal, but the\n\t\t// amount of exceptions would cost too many bytes. On top of\n\t\t// that other frameworks generally stringify `false`.\n\n\t\tif (typeof value == 'function') {\n\t\t\t// never serialize functions as attribute values\n\t\t} else if (value != NULL && (value !== false || name[4] == '-')) {\n\t\t\tdom.setAttribute(name, name == 'popover' && value == true ? '' : value);\n\t\t} else {\n\t\t\tdom.removeAttribute(name);\n\t\t}\n\t}\n}\n\n/**\n * Create an event proxy function.\n * @param {boolean} useCapture Is the event handler for the capture phase.\n * @private\n */\nfunction createEventProxy(useCapture) {\n\t/**\n\t * Proxy an event to hooked event handlers\n\t * @param {import('../internal').PreactEvent} e The event object from the browser\n\t * @private\n\t */\n\treturn function (e) {\n\t\tif (this._listeners) {\n\t\t\tconst eventHandler = this._listeners[e.type + useCapture];\n\t\t\tif (e._dispatched == NULL) {\n\t\t\t\te._dispatched = eventClock++;\n\n\t\t\t\t// When `e._dispatched` is smaller than the time when the targeted event\n\t\t\t\t// handler was attached we know we have bubbled up to an element that was added\n\t\t\t\t// during patching the DOM.\n\t\t\t} else if (e._dispatched < eventHandler._attached) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\treturn eventHandler(options.event ? options.event(e) : e);\n\t\t}\n\t};\n}\n\nconst eventProxy = createEventProxy(false);\nconst eventProxyCapture = createEventProxy(true);\n", "import { enqueueRender } from './component';\nimport { NULL } from './constants';\n\nexport let i = 0;\n\nexport function createContext(defaultValue) {\n\tfunction Context(props) {\n\t\tif (!this.getChildContext) {\n\t\t\t/** @type {Set<import('./internal').Component> | null} */\n\t\t\tlet subs = new Set();\n\t\t\tlet ctx = {};\n\t\t\tctx[Context._id] = this;\n\n\t\t\tthis.getChildContext = () => ctx;\n\n\t\t\tthis.componentWillUnmount = () => {\n\t\t\t\tsubs = NULL;\n\t\t\t};\n\n\t\t\tthis.shouldComponentUpdate = function (_props) {\n\t\t\t\t// @ts-expect-error even\n\t\t\t\tif (this.props.value != _props.value) {\n\t\t\t\t\tsubs.forEach(c => {\n\t\t\t\t\t\tc._force = true;\n\t\t\t\t\t\tenqueueRender(c);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t};\n\n\t\t\tthis.sub = c => {\n\t\t\t\tsubs.add(c);\n\t\t\t\tlet old = c.componentWillUnmount;\n\t\t\t\tc.componentWillUnmount = () => {\n\t\t\t\t\tif (subs) {\n\t\t\t\t\t\tsubs.delete(c);\n\t\t\t\t\t}\n\t\t\t\t\tif (old) old.call(c);\n\t\t\t\t};\n\t\t\t};\n\t\t}\n\n\t\treturn props.children;\n\t}\n\n\tContext._id = '__cC' + i++;\n\tContext._defaultValue = defaultValue;\n\n\t/** @type {import('./internal').FunctionComponent} */\n\tContext.Consumer = (props, contextValue) => {\n\t\treturn props.children(contextValue);\n\t};\n\n\t// we could also get rid of _contextRef entirely\n\tContext.Provider =\n\t\tContext._contextRef =\n\t\tContext.Consumer.contextType =\n\t\t\tContext;\n\n\treturn Context;\n}\n", "import { diff, unmount, applyRef } from './index';\nimport { createVNode, Fragment } from '../create-element';\nimport {\n\tEMPTY_OBJ,\n\tEMPTY_ARR,\n\tINSERT_VNODE,\n\tMATCHED,\n\tUNDEFINED,\n\tNULL\n} from '../constants';\nimport { isArray } from '../util';\nimport { getDomSibling } from '../component';\n\n/**\n * @typedef {import('../internal').ComponentChildren} ComponentChildren\n * @typedef {import('../internal').Component} Component\n * @typedef {import('../internal').PreactElement} PreactElement\n * @typedef {import('../internal').VNode} VNode\n */\n\n/**\n * Diff the children of a virtual node\n * @param {PreactElement} parentDom The DOM element whose children are being\n * diffed\n * @param {ComponentChildren[]} renderResult\n * @param {VNode} newParentVNode The new virtual node whose children should be\n * diff'ed against oldParentVNode\n * @param {VNode} oldParentVNode The old virtual node whose children should be\n * diff'ed against newParentVNode\n * @param {object} globalContext The current context object - modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diffChildren(\n\tparentDom,\n\trenderResult,\n\tnewParentVNode,\n\toldParentVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\tlet i,\n\t\t/** @type {VNode} */\n\t\toldVNode,\n\t\t/** @type {VNode} */\n\t\tchildVNode,\n\t\t/** @type {PreactElement} */\n\t\tnewDom,\n\t\t/** @type {PreactElement} */\n\t\tfirstChildDom;\n\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\n\t// as EMPTY_OBJ._children should be `undefined`.\n\t/** @type {VNode[]} */\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\n\n\tlet newChildrenLength = renderResult.length;\n\n\toldDom = constructNewChildrenArray(\n\t\tnewParentVNode,\n\t\trenderResult,\n\t\toldChildren,\n\t\toldDom,\n\t\tnewChildrenLength\n\t);\n\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\tchildVNode = newParentVNode._children[i];\n\t\tif (childVNode == NULL) continue;\n\n\t\t// At this point, constructNewChildrenArray has assigned _index to be the\n\t\t// matchingIndex for this VNode's oldVNode (or -1 if there is no oldVNode).\n\t\tif (childVNode._index == -1) {\n\t\t\toldVNode = EMPTY_OBJ;\n\t\t} else {\n\t\t\toldVNode = oldChildren[childVNode._index] || EMPTY_OBJ;\n\t\t}\n\n\t\t// Update childVNode._index to its final index\n\t\tchildVNode._index = i;\n\n\t\t// Morph the old element into the new one, but don't append it to the dom yet\n\t\tlet result = diff(\n\t\t\tparentDom,\n\t\t\tchildVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\toldDom,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\n\t\t// Adjust DOM nodes\n\t\tnewDom = childVNode._dom;\n\t\tif (childVNode.ref && oldVNode.ref != childVNode.ref) {\n\t\t\tif (oldVNode.ref) {\n\t\t\t\tapplyRef(oldVNode.ref, NULL, childVNode);\n\t\t\t}\n\t\t\trefQueue.push(\n\t\t\t\tchildVNode.ref,\n\t\t\t\tchildVNode._component || newDom,\n\t\t\t\tchildVNode\n\t\t\t);\n\t\t}\n\n\t\tif (firstChildDom == NULL && newDom != NULL) {\n\t\t\tfirstChildDom = newDom;\n\t\t}\n\n\t\tif (\n\t\t\tchildVNode._flags & INSERT_VNODE ||\n\t\t\toldVNode._children === childVNode._children\n\t\t) {\n\t\t\toldDom = insert(childVNode, oldDom, parentDom);\n\t\t} else if (typeof childVNode.type == 'function' && result !== UNDEFINED) {\n\t\t\toldDom = result;\n\t\t} else if (newDom) {\n\t\t\toldDom = newDom.nextSibling;\n\t\t}\n\n\t\t// Unset diffing flags\n\t\tchildVNode._flags &= ~(INSERT_VNODE | MATCHED);\n\t}\n\n\tnewParentVNode._dom = firstChildDom;\n\n\treturn oldDom;\n}\n\n/**\n * @param {VNode} newParentVNode\n * @param {ComponentChildren[]} renderResult\n * @param {VNode[]} oldChildren\n */\nfunction constructNewChildrenArray(\n\tnewParentVNode,\n\trenderResult,\n\toldChildren,\n\toldDom,\n\tnewChildrenLength\n) {\n\t/** @type {number} */\n\tlet i;\n\t/** @type {VNode} */\n\tlet childVNode;\n\t/** @type {VNode} */\n\tlet oldVNode;\n\n\tlet oldChildrenLength = oldChildren.length,\n\t\tremainingOldChildren = oldChildrenLength;\n\n\tlet skew = 0;\n\n\tnewParentVNode._children = new Array(newChildrenLength);\n\tfor (i = 0; i < newChildrenLength; i++) {\n\t\t// @ts-expect-error We are reusing the childVNode variable to hold both the\n\t\t// pre and post normalized childVNode\n\t\tchildVNode = renderResult[i];\n\n\t\tif (\n\t\t\tchildVNode == NULL ||\n\t\t\ttypeof childVNode == 'boolean' ||\n\t\t\ttypeof childVNode == 'function'\n\t\t) {\n\t\t\tnewParentVNode._children[i] = NULL;\n\t\t\tcontinue;\n\t\t}\n\t\t// If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\n\t\t// or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\n\t\t// it's own DOM & etc. pointers\n\t\telse if (\n\t\t\ttypeof childVNode == 'string' ||\n\t\t\ttypeof childVNode == 'number' ||\n\t\t\t// eslint-disable-next-line valid-typeof\n\t\t\ttypeof childVNode == 'bigint' ||\n\t\t\tchildVNode.constructor == String\n\t\t) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tNULL,\n\t\t\t\tchildVNode,\n\t\t\t\tNULL,\n\t\t\t\tNULL,\n\t\t\t\tNULL\n\t\t\t);\n\t\t} else if (isArray(childVNode)) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tFragment,\n\t\t\t\t{ children: childVNode },\n\t\t\t\tNULL,\n\t\t\t\tNULL,\n\t\t\t\tNULL\n\t\t\t);\n\t\t} else if (childVNode.constructor == UNDEFINED && childVNode._depth > 0) {\n\t\t\t// VNode is already in use, clone it. This can happen in the following\n\t\t\t// scenario:\n\t\t\t//   const reuse = <div />\n\t\t\t//   <div>{reuse}<span />{reuse}</div>\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tchildVNode.type,\n\t\t\t\tchildVNode.props,\n\t\t\t\tchildVNode.key,\n\t\t\t\tchildVNode.ref ? childVNode.ref : NULL,\n\t\t\t\tchildVNode._original\n\t\t\t);\n\t\t} else {\n\t\t\tchildVNode = newParentVNode._children[i] = childVNode;\n\t\t}\n\n\t\tconst skewedIndex = i + skew;\n\t\tchildVNode._parent = newParentVNode;\n\t\tchildVNode._depth = newParentVNode._depth + 1;\n\n\t\t// Temporarily store the matchingIndex on the _index property so we can pull\n\t\t// out the oldVNode in diffChildren. We'll override this to the VNode's\n\t\t// final index after using this property to get the oldVNode\n\t\tconst matchingIndex = (childVNode._index = findMatchingIndex(\n\t\t\tchildVNode,\n\t\t\toldChildren,\n\t\t\tskewedIndex,\n\t\t\tremainingOldChildren\n\t\t));\n\n\t\toldVNode = NULL;\n\t\tif (matchingIndex != -1) {\n\t\t\toldVNode = oldChildren[matchingIndex];\n\t\t\tremainingOldChildren--;\n\t\t\tif (oldVNode) {\n\t\t\t\toldVNode._flags |= MATCHED;\n\t\t\t}\n\t\t}\n\n\t\t// Here, we define isMounting for the purposes of the skew diffing\n\t\t// algorithm. Nodes that are unsuspending are considered mounting and we detect\n\t\t// this by checking if oldVNode._original == null\n\t\tconst isMounting = oldVNode == NULL || oldVNode._original == NULL;\n\n\t\tif (isMounting) {\n\t\t\tif (matchingIndex == -1) {\n\t\t\t\t// When the array of children is growing we need to decrease the skew\n\t\t\t\t// as we are adding a new element to the array.\n\t\t\t\t// Example:\n\t\t\t\t// [1, 2, 3] --> [0, 1, 2, 3]\n\t\t\t\t// oldChildren   newChildren\n\t\t\t\t//\n\t\t\t\t// The new element is at index 0, so our skew is 0,\n\t\t\t\t// we need to decrease the skew as we are adding a new element.\n\t\t\t\t// The decrease will cause us to compare the element at position 1\n\t\t\t\t// with value 1 with the element at position 0 with value 0.\n\t\t\t\t//\n\t\t\t\t// A linear concept is applied when the array is shrinking,\n\t\t\t\t// if the length is unchanged we can assume that no skew\n\t\t\t\t// changes are needed.\n\t\t\t\tif (newChildrenLength > oldChildrenLength) {\n\t\t\t\t\tskew--;\n\t\t\t\t} else if (newChildrenLength < oldChildrenLength) {\n\t\t\t\t\tskew++;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// If we are mounting a DOM VNode, mark it for insertion\n\t\t\tif (typeof childVNode.type != 'function') {\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t} else if (matchingIndex != skewedIndex) {\n\t\t\t// When we move elements around i.e. [0, 1, 2] --> [1, 0, 2]\n\t\t\t// --> we diff 1, we find it at position 1 while our skewed index is 0 and our skew is 0\n\t\t\t//     we set the skew to 1 as we found an offset.\n\t\t\t// --> we diff 0, we find it at position 0 while our skewed index is at 2 and our skew is 1\n\t\t\t//     this makes us increase the skew again.\n\t\t\t// --> we diff 2, we find it at position 2 while our skewed index is at 4 and our skew is 2\n\t\t\t//\n\t\t\t// this becomes an optimization question where currently we see a 1 element offset as an insertion\n\t\t\t// or deletion i.e. we optimize for [0, 1, 2] --> [9, 0, 1, 2]\n\t\t\t// while a more than 1 offset we see as a swap.\n\t\t\t// We could probably build heuristics for having an optimized course of action here as well, but\n\t\t\t// might go at the cost of some bytes.\n\t\t\t//\n\t\t\t// If we wanted to optimize for i.e. only swaps we'd just do the last two code-branches and have\n\t\t\t// only the first item be a re-scouting and all the others fall in their skewed counter-part.\n\t\t\t// We could also further optimize for swaps\n\t\t\tif (matchingIndex == skewedIndex - 1) {\n\t\t\t\tskew--;\n\t\t\t} else if (matchingIndex == skewedIndex + 1) {\n\t\t\t\tskew++;\n\t\t\t} else {\n\t\t\t\tif (matchingIndex > skewedIndex) {\n\t\t\t\t\tskew--;\n\t\t\t\t} else {\n\t\t\t\t\tskew++;\n\t\t\t\t}\n\n\t\t\t\t// Move this VNode's DOM if the original index (matchingIndex) doesn't\n\t\t\t\t// match the new skew index (i + new skew)\n\t\t\t\t// In the former two branches we know that it matches after skewing\n\t\t\t\tchildVNode._flags |= INSERT_VNODE;\n\t\t\t}\n\t\t}\n\t}\n\n\t// Remove remaining oldChildren if there are any. Loop forwards so that as we\n\t// unmount DOM from the beginning of the oldChildren, we can adjust oldDom to\n\t// point to the next child, which needs to be the first DOM node that won't be\n\t// unmounted.\n\tif (remainingOldChildren) {\n\t\tfor (i = 0; i < oldChildrenLength; i++) {\n\t\t\toldVNode = oldChildren[i];\n\t\t\tif (oldVNode != NULL && (oldVNode._flags & MATCHED) == 0) {\n\t\t\t\tif (oldVNode._dom == oldDom) {\n\t\t\t\t\toldDom = getDomSibling(oldVNode);\n\t\t\t\t}\n\n\t\t\t\tunmount(oldVNode, oldVNode);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn oldDom;\n}\n\n/**\n * @param {VNode} parentVNode\n * @param {PreactElement} oldDom\n * @param {PreactElement} parentDom\n * @returns {PreactElement}\n */\nfunction insert(parentVNode, oldDom, parentDom) {\n\t// Note: VNodes in nested suspended trees may be missing _children.\n\n\tif (typeof parentVNode.type == 'function') {\n\t\tlet children = parentVNode._children;\n\t\tfor (let i = 0; children && i < children.length; i++) {\n\t\t\tif (children[i]) {\n\t\t\t\t// If we enter this code path on sCU bailout, where we copy\n\t\t\t\t// oldVNode._children to newVNode._children, we need to update the old\n\t\t\t\t// children's _parent pointer to point to the newVNode (parentVNode\n\t\t\t\t// here).\n\t\t\t\tchildren[i]._parent = parentVNode;\n\t\t\t\toldDom = insert(children[i], oldDom, parentDom);\n\t\t\t}\n\t\t}\n\n\t\treturn oldDom;\n\t} else if (parentVNode._dom != oldDom) {\n\t\tif (oldDom && parentVNode.type && !parentDom.contains(oldDom)) {\n\t\t\toldDom = getDomSibling(parentVNode);\n\t\t}\n\t\tparentDom.insertBefore(parentVNode._dom, oldDom || NULL);\n\t\toldDom = parentVNode._dom;\n\t}\n\n\tdo {\n\t\toldDom = oldDom && oldDom.nextSibling;\n\t} while (oldDom != NULL && oldDom.nodeType == 8);\n\n\treturn oldDom;\n}\n\n/**\n * Flatten and loop through the children of a virtual node\n * @param {ComponentChildren} children The unflattened children of a virtual\n * node\n * @returns {VNode[]}\n */\nexport function toChildArray(children, out) {\n\tout = out || [];\n\tif (children == NULL || typeof children == 'boolean') {\n\t} else if (isArray(children)) {\n\t\tchildren.some(child => {\n\t\t\ttoChildArray(child, out);\n\t\t});\n\t} else {\n\t\tout.push(children);\n\t}\n\treturn out;\n}\n\n/**\n * @param {VNode} childVNode\n * @param {VNode[]} oldChildren\n * @param {number} skewedIndex\n * @param {number} remainingOldChildren\n * @returns {number}\n */\nfunction findMatchingIndex(\n\tchildVNode,\n\toldChildren,\n\tskewedIndex,\n\tremainingOldChildren\n) {\n\tconst key = childVNode.key;\n\tconst type = childVNode.type;\n\tlet oldVNode = oldChildren[skewedIndex];\n\n\t// We only need to perform a search if there are more children\n\t// (remainingOldChildren) to search. However, if the oldVNode we just looked\n\t// at skewedIndex was not already used in this diff, then there must be at\n\t// least 1 other (so greater than 1) remainingOldChildren to attempt to match\n\t// against. So the following condition checks that ensuring\n\t// remainingOldChildren > 1 if the oldVNode is not already used/matched. Else\n\t// if the oldVNode was null or matched, then there could needs to be at least\n\t// 1 (aka `remainingOldChildren > 0`) children to find and compare against.\n\t//\n\t// If there is an unkeyed functional VNode, that isn't a built-in like our Fragment,\n\t// we should not search as we risk re-using state of an unrelated VNode. (reverted for now)\n\tlet shouldSearch =\n\t\t// (typeof type != 'function' || type === Fragment || key) &&\n\t\tremainingOldChildren >\n\t\t(oldVNode != NULL && (oldVNode._flags & MATCHED) == 0 ? 1 : 0);\n\n\tif (\n\t\t(oldVNode === NULL && childVNode.key == null) ||\n\t\t(oldVNode &&\n\t\t\tkey == oldVNode.key &&\n\t\t\ttype == oldVNode.type &&\n\t\t\t(oldVNode._flags & MATCHED) == 0)\n\t) {\n\t\treturn skewedIndex;\n\t} else if (shouldSearch) {\n\t\tlet x = skewedIndex - 1;\n\t\tlet y = skewedIndex + 1;\n\t\twhile (x >= 0 || y < oldChildren.length) {\n\t\t\tif (x >= 0) {\n\t\t\t\toldVNode = oldChildren[x];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) == 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype == oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn x;\n\t\t\t\t}\n\t\t\t\tx--;\n\t\t\t}\n\n\t\t\tif (y < oldChildren.length) {\n\t\t\t\toldVNode = oldChildren[y];\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\t(oldVNode._flags & MATCHED) == 0 &&\n\t\t\t\t\tkey == oldVNode.key &&\n\t\t\t\t\ttype == oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\treturn y;\n\t\t\t\t}\n\t\t\t\ty++;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn -1;\n}\n", "import {\n\tEMPTY_OBJ,\n\tMATH_NAMESPACE,\n\tMODE_HYDRATE,\n\tMODE_SUSPENDED,\n\tNULL,\n\tRESET_MODE,\n\tSVG_NAMESPACE,\n\tUNDEFINED,\n\tXHTML_NAMESPACE\n} from '../constants';\nimport { BaseComponent, getDomSibling } from '../component';\nimport { Fragment } from '../create-element';\nimport { diffChildren } from './children';\nimport { setProperty } from './props';\nimport { assign, isArray, removeNode, slice } from '../util';\nimport options from '../options';\n\n/**\n * @typedef {import('../internal').ComponentChildren} ComponentChildren\n * @typedef {import('../internal').Component} Component\n * @typedef {import('../internal').PreactElement} PreactElement\n * @typedef {import('../internal').VNode} VNode\n */\n\n/**\n * @template {any} T\n * @typedef {import('../internal').Ref<T>} Ref<T>\n */\n\n/**\n * Diff two virtual nodes and apply proper changes to the DOM\n * @param {PreactElement} parentDom The parent of the DOM element\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object. Modified by\n * getChildContext\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {PreactElement} oldDom The current attached DOM element any new dom\n * elements should be placed around. Likely `null` on first render (except when\n * hydrating). Can be a sibling DOM element when diffing Fragments that have\n * siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n */\nexport function diff(\n\tparentDom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating,\n\trefQueue\n) {\n\t/** @type {any} */\n\tlet tmp,\n\t\tnewType = newVNode.type;\n\n\t// When passing through createElement it assigns the object\n\t// constructor as undefined. This to prevent JSON-injection.\n\tif (newVNode.constructor != UNDEFINED) return NULL;\n\n\t// If the previous diff bailed out, resume creating/hydrating.\n\tif (oldVNode._flags & MODE_SUSPENDED) {\n\t\tisHydrating = !!(oldVNode._flags & MODE_HYDRATE);\n\t\toldDom = newVNode._dom = oldVNode._dom;\n\t\texcessDomChildren = [oldDom];\n\t}\n\n\tif ((tmp = options._diff)) tmp(newVNode);\n\n\touter: if (typeof newType == 'function') {\n\t\ttry {\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\n\t\t\tlet newProps = newVNode.props;\n\t\t\tconst isClassComponent =\n\t\t\t\t'prototype' in newType && newType.prototype.render;\n\n\t\t\t// Necessary for createContext api. Setting this property will pass\n\t\t\t// the context value as `this.context` just for this component.\n\t\t\ttmp = newType.contextType;\n\t\t\tlet provider = tmp && globalContext[tmp._id];\n\t\t\tlet componentContext = tmp\n\t\t\t\t? provider\n\t\t\t\t\t? provider.props.value\n\t\t\t\t\t: tmp._defaultValue\n\t\t\t\t: globalContext;\n\n\t\t\t// Get component and set it to `c`\n\t\t\tif (oldVNode._component) {\n\t\t\t\tc = newVNode._component = oldVNode._component;\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\n\t\t\t} else {\n\t\t\t\t// Instantiate the new component\n\t\t\t\tif (isClassComponent) {\n\t\t\t\t\t// @ts-expect-error The check above verifies that newType is suppose to be constructed\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\n\t\t\t\t} else {\n\t\t\t\t\t// @ts-expect-error Trust me, Component implements the interface we want\n\t\t\t\t\tnewVNode._component = c = new BaseComponent(\n\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t);\n\t\t\t\t\tc.constructor = newType;\n\t\t\t\t\tc.render = doRender;\n\t\t\t\t}\n\t\t\t\tif (provider) provider.sub(c);\n\n\t\t\t\tc.props = newProps;\n\t\t\t\tif (!c.state) c.state = {};\n\t\t\t\tc.context = componentContext;\n\t\t\t\tc._globalContext = globalContext;\n\t\t\t\tisNew = c._dirty = true;\n\t\t\t\tc._renderCallbacks = [];\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t}\n\n\t\t\t// Invoke getDerivedStateFromProps\n\t\t\tif (isClassComponent && c._nextState == NULL) {\n\t\t\t\tc._nextState = c.state;\n\t\t\t}\n\n\t\t\tif (isClassComponent && newType.getDerivedStateFromProps != NULL) {\n\t\t\t\tif (c._nextState == c.state) {\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\n\t\t\t\t}\n\n\t\t\t\tassign(\n\t\t\t\t\tc._nextState,\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\n\t\t\t\t);\n\t\t\t}\n\n\t\t\toldProps = c.props;\n\t\t\toldState = c.state;\n\t\t\tc._vnode = newVNode;\n\n\t\t\t// Invoke pre-render lifecycle methods\n\t\t\tif (isNew) {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == NULL &&\n\t\t\t\t\tc.componentWillMount != NULL\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillMount();\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidMount != NULL) {\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (\n\t\t\t\t\tisClassComponent &&\n\t\t\t\t\tnewType.getDerivedStateFromProps == NULL &&\n\t\t\t\t\tnewProps !== oldProps &&\n\t\t\t\t\tc.componentWillReceiveProps != NULL\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t(!c._force &&\n\t\t\t\t\t\tc.shouldComponentUpdate != NULL &&\n\t\t\t\t\t\tc.shouldComponentUpdate(\n\t\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\t\tc._nextState,\n\t\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t\t) === false) ||\n\t\t\t\t\tnewVNode._original == oldVNode._original\n\t\t\t\t) {\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\n\t\t\t\t\tif (newVNode._original != oldVNode._original) {\n\t\t\t\t\t\t// When we are dealing with a bail because of sCU we have to update\n\t\t\t\t\t\t// the props, state and dirty-state.\n\t\t\t\t\t\t// when we are dealing with strict-equality we don't as the child could still\n\t\t\t\t\t\t// be dirtied see #3883\n\t\t\t\t\t\tc.props = newProps;\n\t\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t\t\tc._dirty = false;\n\t\t\t\t\t}\n\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t\tnewVNode._children.some(vnode => {\n\t\t\t\t\t\tif (vnode) vnode._parent = newVNode;\n\t\t\t\t\t});\n\n\t\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t\t}\n\t\t\t\t\tc._stateCallbacks = [];\n\n\t\t\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\t\t\tcommitQueue.push(c);\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\n\t\t\t\tif (c.componentWillUpdate != NULL) {\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (isClassComponent && c.componentDidUpdate != NULL) {\n\t\t\t\t\tc._renderCallbacks.push(() => {\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tc.context = componentContext;\n\t\t\tc.props = newProps;\n\t\t\tc._parentDom = parentDom;\n\t\t\tc._force = false;\n\n\t\t\tlet renderHook = options._render,\n\t\t\t\tcount = 0;\n\t\t\tif (isClassComponent) {\n\t\t\t\tc.state = c._nextState;\n\t\t\t\tc._dirty = false;\n\n\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\tfor (let i = 0; i < c._stateCallbacks.length; i++) {\n\t\t\t\t\tc._renderCallbacks.push(c._stateCallbacks[i]);\n\t\t\t\t}\n\t\t\t\tc._stateCallbacks = [];\n\t\t\t} else {\n\t\t\t\tdo {\n\t\t\t\t\tc._dirty = false;\n\t\t\t\t\tif (renderHook) renderHook(newVNode);\n\n\t\t\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t\t\t// Handle setState called in render, see #2553\n\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t} while (c._dirty && ++count < 25);\n\t\t\t}\n\n\t\t\t// Handle setState called in render, see #2553\n\t\t\tc.state = c._nextState;\n\n\t\t\tif (c.getChildContext != NULL) {\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\n\t\t\t}\n\n\t\t\tif (isClassComponent && !isNew && c.getSnapshotBeforeUpdate != NULL) {\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\n\t\t\t}\n\n\t\t\tlet isTopLevelFragment =\n\t\t\t\ttmp != NULL && tmp.type === Fragment && tmp.key == NULL;\n\t\t\tlet renderResult = tmp;\n\n\t\t\tif (isTopLevelFragment) {\n\t\t\t\trenderResult = cloneNode(tmp.props.children);\n\t\t\t}\n\n\t\t\toldDom = diffChildren(\n\t\t\t\tparentDom,\n\t\t\t\tisArray(renderResult) ? renderResult : [renderResult],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnamespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\toldDom,\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\tc.base = newVNode._dom;\n\n\t\t\t// We successfully rendered this VNode, unset any stored hydration/bailout state:\n\t\t\tnewVNode._flags &= RESET_MODE;\n\n\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\tcommitQueue.push(c);\n\t\t\t}\n\n\t\t\tif (clearProcessingException) {\n\t\t\t\tc._pendingError = c._processingException = NULL;\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tnewVNode._original = NULL;\n\t\t\t// if hydrating or creating initial tree, bailout preserves DOM:\n\t\t\tif (isHydrating || excessDomChildren != NULL) {\n\t\t\t\tif (e.then) {\n\t\t\t\t\tnewVNode._flags |= isHydrating\n\t\t\t\t\t\t? MODE_HYDRATE | MODE_SUSPENDED\n\t\t\t\t\t\t: MODE_SUSPENDED;\n\n\t\t\t\t\twhile (oldDom && oldDom.nodeType == 8 && oldDom.nextSibling) {\n\t\t\t\t\t\toldDom = oldDom.nextSibling;\n\t\t\t\t\t}\n\n\t\t\t\t\texcessDomChildren[excessDomChildren.indexOf(oldDom)] = NULL;\n\t\t\t\t\tnewVNode._dom = oldDom;\n\t\t\t\t} else {\n\t\t\t\t\tfor (let i = excessDomChildren.length; i--; ) {\n\t\t\t\t\t\tremoveNode(excessDomChildren[i]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t}\n\t\t\toptions._catchError(e, newVNode, oldVNode);\n\t\t}\n\t} else if (\n\t\texcessDomChildren == NULL &&\n\t\tnewVNode._original == oldVNode._original\n\t) {\n\t\tnewVNode._children = oldVNode._children;\n\t\tnewVNode._dom = oldVNode._dom;\n\t} else {\n\t\toldDom = newVNode._dom = diffElementNodes(\n\t\t\toldVNode._dom,\n\t\t\tnewVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tnamespace,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\tisHydrating,\n\t\t\trefQueue\n\t\t);\n\t}\n\n\tif ((tmp = options.diffed)) tmp(newVNode);\n\n\treturn newVNode._flags & MODE_SUSPENDED ? undefined : oldDom;\n}\n\n/**\n * @param {Array<Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {VNode} root\n */\nexport function commitRoot(commitQueue, root, refQueue) {\n\tfor (let i = 0; i < refQueue.length; i++) {\n\t\tapplyRef(refQueue[i], refQueue[++i], refQueue[++i]);\n\t}\n\n\tif (options._commit) options._commit(root, commitQueue);\n\n\tcommitQueue.some(c => {\n\t\ttry {\n\t\t\t// @ts-expect-error Reuse the commitQueue variable here so the type changes\n\t\t\tcommitQueue = c._renderCallbacks;\n\t\t\tc._renderCallbacks = [];\n\t\t\tcommitQueue.some(cb => {\n\t\t\t\t// @ts-expect-error See above comment on commitQueue\n\t\t\t\tcb.call(c);\n\t\t\t});\n\t\t} catch (e) {\n\t\t\toptions._catchError(e, c._vnode);\n\t\t}\n\t});\n}\n\nfunction cloneNode(node) {\n\tif (\n\t\ttypeof node != 'object' ||\n\t\tnode == NULL ||\n\t\t(node._depth && node._depth > 0)\n\t) {\n\t\treturn node;\n\t}\n\n\tif (isArray(node)) {\n\t\treturn node.map(cloneNode);\n\t}\n\n\treturn assign({}, node);\n}\n\n/**\n * Diff two virtual nodes representing DOM element\n * @param {PreactElement} dom The DOM element representing the virtual nodes\n * being diffed\n * @param {VNode} newVNode The new virtual node\n * @param {VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object\n * @param {string} namespace Current namespace of the DOM node (HTML, SVG, or MathML)\n * @param {Array<PreactElement>} excessDomChildren\n * @param {Array<Component>} commitQueue List of components which have callbacks\n * to invoke in commitRoot\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @param {any[]} refQueue an array of elements needed to invoke refs\n * @returns {PreactElement}\n */\nfunction diffElementNodes(\n\tdom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tnamespace,\n\texcessDomChildren,\n\tcommitQueue,\n\tisHydrating,\n\trefQueue\n) {\n\tlet oldProps = oldVNode.props;\n\tlet newProps = newVNode.props;\n\tlet nodeType = /** @type {string} */ (newVNode.type);\n\t/** @type {any} */\n\tlet i;\n\t/** @type {{ __html?: string }} */\n\tlet newHtml;\n\t/** @type {{ __html?: string }} */\n\tlet oldHtml;\n\t/** @type {ComponentChildren} */\n\tlet newChildren;\n\tlet value;\n\tlet inputValue;\n\tlet checked;\n\n\t// Tracks entering and exiting namespaces when descending through the tree.\n\tif (nodeType == 'svg') namespace = SVG_NAMESPACE;\n\telse if (nodeType == 'math') namespace = MATH_NAMESPACE;\n\telse if (!namespace) namespace = XHTML_NAMESPACE;\n\n\tif (excessDomChildren != NULL) {\n\t\tfor (i = 0; i < excessDomChildren.length; i++) {\n\t\t\tvalue = excessDomChildren[i];\n\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\n\t\t\t// argument matches an element in excessDomChildren, remove it from\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\n\t\t\tif (\n\t\t\t\tvalue &&\n\t\t\t\t'setAttribute' in value == !!nodeType &&\n\t\t\t\t(nodeType ? value.localName == nodeType : value.nodeType == 3)\n\t\t\t) {\n\t\t\t\tdom = value;\n\t\t\t\texcessDomChildren[i] = NULL;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (dom == NULL) {\n\t\tif (nodeType == NULL) {\n\t\t\treturn document.createTextNode(newProps);\n\t\t}\n\n\t\tdom = document.createElementNS(\n\t\t\tnamespace,\n\t\t\tnodeType,\n\t\t\tnewProps.is && newProps\n\t\t);\n\n\t\t// we are creating a new node, so we can assume this is a new subtree (in\n\t\t// case we are hydrating), this deopts the hydrate\n\t\tif (isHydrating) {\n\t\t\tif (options._hydrationMismatch)\n\t\t\t\toptions._hydrationMismatch(newVNode, excessDomChildren);\n\t\t\tisHydrating = false;\n\t\t}\n\t\t// we created a new parent, so none of the previously attached children can be reused:\n\t\texcessDomChildren = NULL;\n\t}\n\n\tif (nodeType == NULL) {\n\t\t// During hydration, we still have to split merged text from SSR'd HTML.\n\t\tif (oldProps !== newProps && (!isHydrating || dom.data != newProps)) {\n\t\t\tdom.data = newProps;\n\t\t}\n\t} else {\n\t\t// If excessDomChildren was not null, repopulate it with the current element's children:\n\t\texcessDomChildren = excessDomChildren && slice.call(dom.childNodes);\n\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\n\n\t\t// If we are in a situation where we are not hydrating but are using\n\t\t// existing DOM (e.g. replaceNode) we should read the existing DOM\n\t\t// attributes to diff them\n\t\tif (!isHydrating && excessDomChildren != NULL) {\n\t\t\toldProps = {};\n\t\t\tfor (i = 0; i < dom.attributes.length; i++) {\n\t\t\t\tvalue = dom.attributes[i];\n\t\t\t\toldProps[value.name] = value.value;\n\t\t\t}\n\t\t}\n\n\t\tfor (i in oldProps) {\n\t\t\tvalue = oldProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\toldHtml = value;\n\t\t\t} else if (!(i in newProps)) {\n\t\t\t\tif (\n\t\t\t\t\t(i == 'value' && 'defaultValue' in newProps) ||\n\t\t\t\t\t(i == 'checked' && 'defaultChecked' in newProps)\n\t\t\t\t) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tsetProperty(dom, i, NULL, value, namespace);\n\t\t\t}\n\t\t}\n\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\n\t\t// @TODO we should warn in debug mode when props don't match here.\n\t\tfor (i in newProps) {\n\t\t\tvalue = newProps[i];\n\t\t\tif (i == 'children') {\n\t\t\t\tnewChildren = value;\n\t\t\t} else if (i == 'dangerouslySetInnerHTML') {\n\t\t\t\tnewHtml = value;\n\t\t\t} else if (i == 'value') {\n\t\t\t\tinputValue = value;\n\t\t\t} else if (i == 'checked') {\n\t\t\t\tchecked = value;\n\t\t\t} else if (\n\t\t\t\t(!isHydrating || typeof value == 'function') &&\n\t\t\t\toldProps[i] !== value\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, value, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\n\t\tif (newHtml) {\n\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\n\t\t\tif (\n\t\t\t\t!isHydrating &&\n\t\t\t\t(!oldHtml ||\n\t\t\t\t\t(newHtml.__html != oldHtml.__html && newHtml.__html != dom.innerHTML))\n\t\t\t) {\n\t\t\t\tdom.innerHTML = newHtml.__html;\n\t\t\t}\n\n\t\t\tnewVNode._children = [];\n\t\t} else {\n\t\t\tif (oldHtml) dom.innerHTML = '';\n\n\t\t\tdiffChildren(\n\t\t\t\t// @ts-expect-error\n\t\t\t\tnewVNode.type == 'template' ? dom.content : dom,\n\t\t\t\tisArray(newChildren) ? newChildren : [newChildren],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnodeType == 'foreignObject' ? XHTML_NAMESPACE : namespace,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\texcessDomChildren\n\t\t\t\t\t? excessDomChildren[0]\n\t\t\t\t\t: oldVNode._children && getDomSibling(oldVNode, 0),\n\t\t\t\tisHydrating,\n\t\t\t\trefQueue\n\t\t\t);\n\n\t\t\t// Remove children that are not part of any vnode.\n\t\t\tif (excessDomChildren != NULL) {\n\t\t\t\tfor (i = excessDomChildren.length; i--; ) {\n\t\t\t\t\tremoveNode(excessDomChildren[i]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// As above, don't diff props during hydration\n\t\tif (!isHydrating) {\n\t\t\ti = 'value';\n\t\t\tif (nodeType == 'progress' && inputValue == NULL) {\n\t\t\t\tdom.removeAttribute('value');\n\t\t\t} else if (\n\t\t\t\tinputValue != UNDEFINED &&\n\t\t\t\t// #2756 For the <progress>-element the initial value is 0,\n\t\t\t\t// despite the attribute not being present. When the attribute\n\t\t\t\t// is missing the progress bar is treated as indeterminate.\n\t\t\t\t// To fix that we'll always update it when it is 0 for progress elements\n\t\t\t\t(inputValue !== dom[i] ||\n\t\t\t\t\t(nodeType == 'progress' && !inputValue) ||\n\t\t\t\t\t// This is only for IE 11 to fix <select> value not being updated.\n\t\t\t\t\t// To avoid a stale select value we need to set the option.value\n\t\t\t\t\t// again, which triggers IE11 to re-evaluate the select value\n\t\t\t\t\t(nodeType == 'option' && inputValue != oldProps[i]))\n\t\t\t) {\n\t\t\t\tsetProperty(dom, i, inputValue, oldProps[i], namespace);\n\t\t\t}\n\n\t\t\ti = 'checked';\n\t\t\tif (checked != UNDEFINED && checked != dom[i]) {\n\t\t\t\tsetProperty(dom, i, checked, oldProps[i], namespace);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn dom;\n}\n\n/**\n * Invoke or update a ref, depending on whether it is a function or object ref.\n * @param {Ref<any> & { _unmount?: unknown }} ref\n * @param {any} value\n * @param {VNode} vnode\n */\nexport function applyRef(ref, value, vnode) {\n\ttry {\n\t\tif (typeof ref == 'function') {\n\t\t\tlet hasRefUnmount = typeof ref._unmount == 'function';\n\t\t\tif (hasRefUnmount) {\n\t\t\t\t// @ts-ignore TS doesn't like moving narrowing checks into variables\n\t\t\t\tref._unmount();\n\t\t\t}\n\n\t\t\tif (!hasRefUnmount || value != NULL) {\n\t\t\t\t// Store the cleanup function on the function\n\t\t\t\t// instance object itself to avoid shape\n\t\t\t\t// transitioning vnode\n\t\t\t\tref._unmount = ref(value);\n\t\t\t}\n\t\t} else ref.current = value;\n\t} catch (e) {\n\t\toptions._catchError(e, vnode);\n\t}\n}\n\n/**\n * Unmount a virtual node from the tree and apply DOM changes\n * @param {VNode} vnode The virtual node to unmount\n * @param {VNode} parentVNode The parent of the VNode that initiated the unmount\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\n * current element is already detached from the DOM.\n */\nexport function unmount(vnode, parentVNode, skipRemove) {\n\tlet r;\n\tif (options.unmount) options.unmount(vnode);\n\n\tif ((r = vnode.ref)) {\n\t\tif (!r.current || r.current == vnode._dom) {\n\t\t\tapplyRef(r, NULL, parentVNode);\n\t\t}\n\t}\n\n\tif ((r = vnode._component) != NULL) {\n\t\tif (r.componentWillUnmount) {\n\t\t\ttry {\n\t\t\t\tr.componentWillUnmount();\n\t\t\t} catch (e) {\n\t\t\t\toptions._catchError(e, parentVNode);\n\t\t\t}\n\t\t}\n\n\t\tr.base = r._parentDom = NULL;\n\t}\n\n\tif ((r = vnode._children)) {\n\t\tfor (let i = 0; i < r.length; i++) {\n\t\t\tif (r[i]) {\n\t\t\t\tunmount(\n\t\t\t\t\tr[i],\n\t\t\t\t\tparentVNode,\n\t\t\t\t\tskipRemove || typeof vnode.type != 'function'\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (!skipRemove) {\n\t\tremoveNode(vnode._dom);\n\t}\n\n\tvnode._component = vnode._parent = vnode._dom = UNDEFINED;\n}\n\n/** The `.render()` method for a PFC backing instance. */\nfunction doRender(props, state, context) {\n\treturn this.constructor(props, context);\n}\n", "import { EMPTY_OBJ, NULL } from './constants';\nimport { commitRoot, diff } from './diff/index';\nimport { createElement, Fragment } from './create-element';\nimport options from './options';\nimport { slice } from './util';\n\n/**\n * Render a Preact virtual node into a DOM element\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to render into\n * @param {import('./internal').PreactElement | object} [replaceNode] Optional: Attempt to re-use an\n * existing DOM tree rooted at `replaceNode`\n */\nexport function render(vnode, parentDom, replaceNode) {\n\t// https://github.com/preactjs/preact/issues/3794\n\tif (parentDom == document) {\n\t\tparentDom = document.documentElement;\n\t}\n\n\tif (options._root) options._root(vnode, parentDom);\n\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we are in\n\t// hydration mode or not by passing the `hydrate` function instead of a DOM\n\t// element..\n\tlet isHydrating = typeof replaceNode == 'function';\n\n\t// To be able to support calling `render()` multiple times on the same\n\t// DOM node, we need to obtain a reference to the previous tree. We do\n\t// this by assigning a new `_children` property to DOM nodes which points\n\t// to the last rendered tree. By default this property is not present, which\n\t// means that we are mounting a new tree for the first time.\n\tlet oldVNode = isHydrating\n\t\t? NULL\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\n\n\tvnode = ((!isHydrating && replaceNode) || parentDom)._children =\n\t\tcreateElement(Fragment, NULL, [vnode]);\n\n\t// List of effects that need to be called after diffing.\n\tlet commitQueue = [],\n\t\trefQueue = [];\n\tdiff(\n\t\tparentDom,\n\t\t// Determine the new vnode tree and store it on the DOM element on\n\t\t// our custom `_children` property.\n\t\tvnode,\n\t\toldVNode || EMPTY_OBJ,\n\t\tEMPTY_OBJ,\n\t\tparentDom.namespaceURI,\n\t\t!isHydrating && replaceNode\n\t\t\t? [replaceNode]\n\t\t\t: oldVNode\n\t\t\t\t? NULL\n\t\t\t\t: parentDom.firstChild\n\t\t\t\t\t? slice.call(parentDom.childNodes)\n\t\t\t\t\t: NULL,\n\t\tcommitQueue,\n\t\t!isHydrating && replaceNode\n\t\t\t? replaceNode\n\t\t\t: oldVNode\n\t\t\t\t? oldVNode._dom\n\t\t\t\t: parentDom.firstChild,\n\t\tisHydrating,\n\t\trefQueue\n\t);\n\n\t// Flush all queued effects\n\tcommitRoot(commitQueue, vnode, refQueue);\n}\n\n/**\n * Update an existing DOM element with data from a Preact virtual node\n * @param {import('./internal').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to update\n */\nexport function hydrate(vnode, parentDom) {\n\trender(vnode, parentDom, hydrate);\n}\n", "import { assign, slice } from './util';\nimport { createVNode } from './create-element';\nimport { NULL, UNDEFINED } from './constants';\n\n/**\n * Clones the given VNode, optionally adding attributes/props and replacing its\n * children.\n * @param {import('./internal').VNode} vnode The virtual DOM element to clone\n * @param {object} props Attributes/props to add when cloning\n * @param {Array<import('./internal').ComponentChildren>} rest Any additional arguments will be used\n * as replacement children.\n * @returns {import('./internal').VNode}\n */\nexport function cloneElement(vnode, props, children) {\n\tlet normalizedProps = assign({}, vnode.props),\n\t\tkey,\n\t\tref,\n\t\ti;\n\n\tlet defaultProps;\n\n\tif (vnode.type && vnode.type.defaultProps) {\n\t\tdefaultProps = vnode.type.defaultProps;\n\t}\n\n\tfor (i in props) {\n\t\tif (i == 'key') key = props[i];\n\t\telse if (i == 'ref') ref = props[i];\n\t\telse if (props[i] == UNDEFINED && defaultProps != UNDEFINED) {\n\t\t\tnormalizedProps[i] = defaultProps[i];\n\t\t} else {\n\t\t\tnormalizedProps[i] = props[i];\n\t\t}\n\t}\n\n\tif (arguments.length > 2) {\n\t\tnormalizedProps.children =\n\t\t\targuments.length > 3 ? slice.call(arguments, 2) : children;\n\t}\n\n\treturn createVNode(\n\t\tvnode.type,\n\t\tnormalizedProps,\n\t\tkey || vnode.key,\n\t\tref || vnode.ref,\n\t\tNULL\n\t);\n}\n", "import { NULL } from '../constants';\n\n/**\n * Find the closest error boundary to a thrown error and call it\n * @param {object} error The thrown value\n * @param {import('../internal').VNode} vnode The vnode that threw the error that was caught (except\n * for unmounting when this parameter is the highest parent that was being\n * unmounted)\n * @param {import('../internal').VNode} [oldVNode]\n * @param {import('../internal').ErrorInfo} [errorInfo]\n */\nexport function _catchError(error, vnode, oldVNode, errorInfo) {\n\t/** @type {import('../internal').Component} */\n\tlet component,\n\t\t/** @type {import('../internal').ComponentType} */\n\t\tctor,\n\t\t/** @type {boolean} */\n\t\thandled;\n\n\tfor (; (vnode = vnode._parent); ) {\n\t\tif ((component = vnode._component) && !component._processingException) {\n\t\t\ttry {\n\t\t\t\tctor = component.constructor;\n\n\t\t\t\tif (ctor && ctor.getDerivedStateFromError != NULL) {\n\t\t\t\t\tcomponent.setState(ctor.getDerivedStateFromError(error));\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\tif (component.componentDidCatch != NULL) {\n\t\t\t\t\tcomponent.componentDidCatch(error, errorInfo || {});\n\t\t\t\t\thandled = component._dirty;\n\t\t\t\t}\n\n\t\t\t\t// This is an error boundary. Mark it as having bailed out, and whether it was mid-hydration.\n\t\t\t\tif (handled) {\n\t\t\t\t\treturn (component._pendingError = component);\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\terror = e;\n\t\t\t}\n\t\t}\n\t}\n\n\tthrow error;\n}\n", "import { options as _options } from 'preact';\n\n/** @type {number} */\nlet currentIndex;\n\n/** @type {import('./internal').Component} */\nlet currentComponent;\n\n/** @type {import('./internal').Component} */\nlet previousComponent;\n\n/** @type {number} */\nlet currentHook = 0;\n\n/** @type {Array<import('./internal').Component>} */\nlet afterPaintEffects = [];\n\n// Cast to use internal Options type\nconst options = /** @type {import('./internal').Options} */ (_options);\n\nlet oldBeforeDiff = options._diff;\nlet oldBeforeRender = options._render;\nlet oldAfterDiff = options.diffed;\nlet oldCommit = options._commit;\nlet oldBeforeUnmount = options.unmount;\nlet oldRoot = options._root;\n\nconst RAF_TIMEOUT = 100;\nlet prevRaf;\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions._diff = vnode => {\n\tcurrentComponent = null;\n\tif (oldBeforeDiff) oldBeforeDiff(vnode);\n};\n\noptions._root = (vnode, parentDom) => {\n\tif (vnode && parentDom._children && parentDom._children._mask) {\n\t\tvnode._mask = parentDom._children._mask;\n\t}\n\n\tif (oldRoot) oldRoot(vnode, parentDom);\n};\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions._render = vnode => {\n\tif (oldBeforeRender) oldBeforeRender(vnode);\n\n\tcurrentComponent = vnode._component;\n\tcurrentIndex = 0;\n\n\tconst hooks = currentComponent.__hooks;\n\tif (hooks) {\n\t\tif (previousComponent === currentComponent) {\n\t\t\thooks._pendingEffects = [];\n\t\t\tcurrentComponent._renderCallbacks = [];\n\t\t\thooks._list.forEach(hookItem => {\n\t\t\t\tif (hookItem._nextValue) {\n\t\t\t\t\thookItem._value = hookItem._nextValue;\n\t\t\t\t}\n\t\t\t\thookItem._pendingArgs = hookItem._nextValue = undefined;\n\t\t\t});\n\t\t} else {\n\t\t\thooks._pendingEffects.forEach(invokeCleanup);\n\t\t\thooks._pendingEffects.forEach(invokeEffect);\n\t\t\thooks._pendingEffects = [];\n\t\t\tcurrentIndex = 0;\n\t\t}\n\t}\n\tpreviousComponent = currentComponent;\n};\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions.diffed = vnode => {\n\tif (oldAfterDiff) oldAfterDiff(vnode);\n\n\tconst c = vnode._component;\n\tif (c && c.__hooks) {\n\t\tif (c.__hooks._pendingEffects.length) afterPaint(afterPaintEffects.push(c));\n\t\tc.__hooks._list.forEach(hookItem => {\n\t\t\tif (hookItem._pendingArgs) {\n\t\t\t\thookItem._args = hookItem._pendingArgs;\n\t\t\t}\n\t\t\thookItem._pendingArgs = undefined;\n\t\t});\n\t}\n\tpreviousComponent = currentComponent = null;\n};\n\n// TODO: Improve typing of commitQueue parameter\n/** @type {(vnode: import('./internal').VNode, commitQueue: any) => void} */\noptions._commit = (vnode, commitQueue) => {\n\tcommitQueue.some(component => {\n\t\ttry {\n\t\t\tcomponent._renderCallbacks.forEach(invokeCleanup);\n\t\t\tcomponent._renderCallbacks = component._renderCallbacks.filter(cb =>\n\t\t\t\tcb._value ? invokeEffect(cb) : true\n\t\t\t);\n\t\t} catch (e) {\n\t\t\tcommitQueue.some(c => {\n\t\t\t\tif (c._renderCallbacks) c._renderCallbacks = [];\n\t\t\t});\n\t\t\tcommitQueue = [];\n\t\t\toptions._catchError(e, component._vnode);\n\t\t}\n\t});\n\n\tif (oldCommit) oldCommit(vnode, commitQueue);\n};\n\n/** @type {(vnode: import('./internal').VNode) => void} */\noptions.unmount = vnode => {\n\tif (oldBeforeUnmount) oldBeforeUnmount(vnode);\n\n\tconst c = vnode._component;\n\tif (c && c.__hooks) {\n\t\tlet hasErrored;\n\t\tc.__hooks._list.forEach(s => {\n\t\t\ttry {\n\t\t\t\tinvokeCleanup(s);\n\t\t\t} catch (e) {\n\t\t\t\thasErrored = e;\n\t\t\t}\n\t\t});\n\t\tc.__hooks = undefined;\n\t\tif (hasErrored) options._catchError(hasErrored, c._vnode);\n\t}\n};\n\n/**\n * Get a hook's state from the currentComponent\n * @param {number} index The index of the hook to get\n * @param {number} type The index of the hook to get\n * @returns {any}\n */\nfunction getHookState(index, type) {\n\tif (options._hook) {\n\t\toptions._hook(currentComponent, index, currentHook || type);\n\t}\n\tcurrentHook = 0;\n\n\t// Largely inspired by:\n\t// * https://github.com/michael-klein/funcy.js/blob/f6be73468e6ec46b0ff5aa3cc4c9baf72a29025a/src/hooks/core_hooks.mjs\n\t// * https://github.com/michael-klein/funcy.js/blob/650beaa58c43c33a74820a3c98b3c7079cf2e333/src/renderer.mjs\n\t// Other implementations to look at:\n\t// * https://codesandbox.io/s/mnox05qp8\n\tconst hooks =\n\t\tcurrentComponent.__hooks ||\n\t\t(currentComponent.__hooks = {\n\t\t\t_list: [],\n\t\t\t_pendingEffects: []\n\t\t});\n\n\tif (index >= hooks._list.length) {\n\t\thooks._list.push({});\n\t}\n\n\treturn hooks._list[index];\n}\n\n/**\n * @template {unknown} S\n * @param {import('./index').Dispatch<import('./index').StateUpdater<S>>} [initialState]\n * @returns {[S, (state: S) => void]}\n */\nexport function useState(initialState) {\n\tcurrentHook = 1;\n\treturn useReducer(invokeOrReturn, initialState);\n}\n\n/**\n * @template {unknown} S\n * @template {unknown} A\n * @param {import('./index').Reducer<S, A>} reducer\n * @param {import('./index').Dispatch<import('./index').StateUpdater<S>>} initialState\n * @param {(initialState: any) => void} [init]\n * @returns {[ S, (state: S) => void ]}\n */\nexport function useReducer(reducer, initialState, init) {\n\t/** @type {import('./internal').ReducerHookState} */\n\tconst hookState = getHookState(currentIndex++, 2);\n\thookState._reducer = reducer;\n\tif (!hookState._component) {\n\t\thookState._value = [\n\t\t\t!init ? invokeOrReturn(undefined, initialState) : init(initialState),\n\n\t\t\taction => {\n\t\t\t\tconst currentValue = hookState._nextValue\n\t\t\t\t\t? hookState._nextValue[0]\n\t\t\t\t\t: hookState._value[0];\n\t\t\t\tconst nextValue = hookState._reducer(currentValue, action);\n\n\t\t\t\tif (currentValue !== nextValue) {\n\t\t\t\t\thookState._nextValue = [nextValue, hookState._value[1]];\n\t\t\t\t\thookState._component.setState({});\n\t\t\t\t}\n\t\t\t}\n\t\t];\n\n\t\thookState._component = currentComponent;\n\n\t\tif (!currentComponent._hasScuFromHooks) {\n\t\t\tcurrentComponent._hasScuFromHooks = true;\n\t\t\tlet prevScu = currentComponent.shouldComponentUpdate;\n\t\t\tconst prevCWU = currentComponent.componentWillUpdate;\n\n\t\t\t// If we're dealing with a forced update `shouldComponentUpdate` will\n\t\t\t// not be called. But we use that to update the hook values, so we\n\t\t\t// need to call it.\n\t\t\tcurrentComponent.componentWillUpdate = function (p, s, c) {\n\t\t\t\tif (this._force) {\n\t\t\t\t\tlet tmp = prevScu;\n\t\t\t\t\t// Clear to avoid other sCU hooks from being called\n\t\t\t\t\tprevScu = undefined;\n\t\t\t\t\tupdateHookState(p, s, c);\n\t\t\t\t\tprevScu = tmp;\n\t\t\t\t}\n\n\t\t\t\tif (prevCWU) prevCWU.call(this, p, s, c);\n\t\t\t};\n\n\t\t\t// This SCU has the purpose of bailing out after repeated updates\n\t\t\t// to stateful hooks.\n\t\t\t// we store the next value in _nextValue[0] and keep doing that for all\n\t\t\t// state setters, if we have next states and\n\t\t\t// all next states within a component end up being equal to their original state\n\t\t\t// we are safe to bail out for this specific component.\n\t\t\t/**\n\t\t\t *\n\t\t\t * @type {import('./internal').Component[\"shouldComponentUpdate\"]}\n\t\t\t */\n\t\t\t// @ts-ignore - We don't use TS to downtranspile\n\t\t\t// eslint-disable-next-line no-inner-declarations\n\t\t\tfunction updateHookState(p, s, c) {\n\t\t\t\tif (!hookState._component.__hooks) return true;\n\n\t\t\t\t/** @type {(x: import('./internal').HookState) => x is import('./internal').ReducerHookState} */\n\t\t\t\tconst isStateHook = x => !!x._component;\n\t\t\t\tconst stateHooks =\n\t\t\t\t\thookState._component.__hooks._list.filter(isStateHook);\n\n\t\t\t\tconst allHooksEmpty = stateHooks.every(x => !x._nextValue);\n\t\t\t\t// When we have no updated hooks in the component we invoke the previous SCU or\n\t\t\t\t// traverse the VDOM tree further.\n\t\t\t\tif (allHooksEmpty) {\n\t\t\t\t\treturn prevScu ? prevScu.call(this, p, s, c) : true;\n\t\t\t\t}\n\n\t\t\t\t// We check whether we have components with a nextValue set that\n\t\t\t\t// have values that aren't equal to one another this pushes\n\t\t\t\t// us to update further down the tree\n\t\t\t\tlet shouldUpdate = hookState._component.props !== p;\n\t\t\t\tstateHooks.forEach(hookItem => {\n\t\t\t\t\tif (hookItem._nextValue) {\n\t\t\t\t\t\tconst currentValue = hookItem._value[0];\n\t\t\t\t\t\thookItem._value = hookItem._nextValue;\n\t\t\t\t\t\thookItem._nextValue = undefined;\n\t\t\t\t\t\tif (currentValue !== hookItem._value[0]) shouldUpdate = true;\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\treturn prevScu\n\t\t\t\t\t? prevScu.call(this, p, s, c) || shouldUpdate\n\t\t\t\t\t: shouldUpdate;\n\t\t\t}\n\n\t\t\tcurrentComponent.shouldComponentUpdate = updateHookState;\n\t\t}\n\t}\n\n\treturn hookState._nextValue || hookState._value;\n}\n\n/**\n * @param {import('./internal').Effect} callback\n * @param {unknown[]} args\n * @returns {void}\n */\nexport function useEffect(callback, args) {\n\t/** @type {import('./internal').EffectHookState} */\n\tconst state = getHookState(currentIndex++, 3);\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\n\t\tstate._value = callback;\n\t\tstate._pendingArgs = args;\n\n\t\tcurrentComponent.__hooks._pendingEffects.push(state);\n\t}\n}\n\n/**\n * @param {import('./internal').Effect} callback\n * @param {unknown[]} args\n * @returns {void}\n */\nexport function useLayoutEffect(callback, args) {\n\t/** @type {import('./internal').EffectHookState} */\n\tconst state = getHookState(currentIndex++, 4);\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\n\t\tstate._value = callback;\n\t\tstate._pendingArgs = args;\n\n\t\tcurrentComponent._renderCallbacks.push(state);\n\t}\n}\n\n/** @type {(initialValue: unknown) => unknown} */\nexport function useRef(initialValue) {\n\tcurrentHook = 5;\n\treturn useMemo(() => ({ current: initialValue }), []);\n}\n\n/**\n * @param {object} ref\n * @param {() => object} createHandle\n * @param {unknown[]} args\n * @returns {void}\n */\nexport function useImperativeHandle(ref, createHandle, args) {\n\tcurrentHook = 6;\n\tuseLayoutEffect(\n\t\t() => {\n\t\t\tif (typeof ref == 'function') {\n\t\t\t\tconst result = ref(createHandle());\n\t\t\t\treturn () => {\n\t\t\t\t\tref(null);\n\t\t\t\t\tif (result && typeof result == 'function') result();\n\t\t\t\t};\n\t\t\t} else if (ref) {\n\t\t\t\tref.current = createHandle();\n\t\t\t\treturn () => (ref.current = null);\n\t\t\t}\n\t\t},\n\t\targs == null ? args : args.concat(ref)\n\t);\n}\n\n/**\n * @template {unknown} T\n * @param {() => T} factory\n * @param {unknown[]} args\n * @returns {T}\n */\nexport function useMemo(factory, args) {\n\t/** @type {import('./internal').MemoHookState<T>} */\n\tconst state = getHookState(currentIndex++, 7);\n\tif (argsChanged(state._args, args)) {\n\t\tstate._value = factory();\n\t\tstate._args = args;\n\t\tstate._factory = factory;\n\t}\n\n\treturn state._value;\n}\n\n/**\n * @param {() => void} callback\n * @param {unknown[]} args\n * @returns {() => void}\n */\nexport function useCallback(callback, args) {\n\tcurrentHook = 8;\n\treturn useMemo(() => callback, args);\n}\n\n/**\n * @param {import('./internal').PreactContext} context\n */\nexport function useContext(context) {\n\tconst provider = currentComponent.context[context._id];\n\t// We could skip this call here, but than we'd not call\n\t// `options._hook`. We need to do that in order to make\n\t// the devtools aware of this hook.\n\t/** @type {import('./internal').ContextHookState} */\n\tconst state = getHookState(currentIndex++, 9);\n\t// The devtools needs access to the context object to\n\t// be able to pull of the default value when no provider\n\t// is present in the tree.\n\tstate._context = context;\n\tif (!provider) return context._defaultValue;\n\t// This is probably not safe to convert to \"!\"\n\tif (state._value == null) {\n\t\tstate._value = true;\n\t\tprovider.sub(currentComponent);\n\t}\n\treturn provider.props.value;\n}\n\n/**\n * Display a custom label for a custom hook for the devtools panel\n * @type {<T>(value: T, cb?: (value: T) => string | number) => void}\n */\nexport function useDebugValue(value, formatter) {\n\tif (options.useDebugValue) {\n\t\toptions.useDebugValue(\n\t\t\tformatter ? formatter(value) : /** @type {any}*/ (value)\n\t\t);\n\t}\n}\n\n/**\n * @param {(error: unknown, errorInfo: import('preact').ErrorInfo) => void} cb\n * @returns {[unknown, () => void]}\n */\nexport function useErrorBoundary(cb) {\n\t/** @type {import('./internal').ErrorBoundaryHookState} */\n\tconst state = getHookState(currentIndex++, 10);\n\tconst errState = useState();\n\tstate._value = cb;\n\tif (!currentComponent.componentDidCatch) {\n\t\tcurrentComponent.componentDidCatch = (err, errorInfo) => {\n\t\t\tif (state._value) state._value(err, errorInfo);\n\t\t\terrState[1](err);\n\t\t};\n\t}\n\treturn [\n\t\terrState[0],\n\t\t() => {\n\t\t\terrState[1](undefined);\n\t\t}\n\t];\n}\n\n/** @type {() => string} */\nexport function useId() {\n\t/** @type {import('./internal').IdHookState} */\n\tconst state = getHookState(currentIndex++, 11);\n\tif (!state._value) {\n\t\t// Grab either the root node or the nearest async boundary node.\n\t\t/** @type {import('./internal').VNode} */\n\t\tlet root = currentComponent._vnode;\n\t\twhile (root !== null && !root._mask && root._parent !== null) {\n\t\t\troot = root._parent;\n\t\t}\n\n\t\tlet mask = root._mask || (root._mask = [0, 0]);\n\t\tstate._value = 'P' + mask[0] + '-' + mask[1]++;\n\t}\n\n\treturn state._value;\n}\n\n/**\n * After paint effects consumer.\n */\nfunction flushAfterPaintEffects() {\n\tlet component;\n\twhile ((component = afterPaintEffects.shift())) {\n\t\tif (!component._parentDom || !component.__hooks) continue;\n\t\ttry {\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeCleanup);\n\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeEffect);\n\t\t\tcomponent.__hooks._pendingEffects = [];\n\t\t} catch (e) {\n\t\t\tcomponent.__hooks._pendingEffects = [];\n\t\t\toptions._catchError(e, component._vnode);\n\t\t}\n\t}\n}\n\nlet HAS_RAF = typeof requestAnimationFrame == 'function';\n\n/**\n * Schedule a callback to be invoked after the browser has a chance to paint a new frame.\n * Do this by combining requestAnimationFrame (rAF) + setTimeout to invoke a callback after\n * the next browser frame.\n *\n * Also, schedule a timeout in parallel to the the rAF to ensure the callback is invoked\n * even if RAF doesn't fire (for example if the browser tab is not visible)\n *\n * @param {() => void} callback\n */\nfunction afterNextFrame(callback) {\n\tconst done = () => {\n\t\tclearTimeout(timeout);\n\t\tif (HAS_RAF) cancelAnimationFrame(raf);\n\t\tsetTimeout(callback);\n\t};\n\tconst timeout = setTimeout(done, RAF_TIMEOUT);\n\n\tlet raf;\n\tif (HAS_RAF) {\n\t\traf = requestAnimationFrame(done);\n\t}\n}\n\n// Note: if someone used options.debounceRendering = requestAnimationFrame,\n// then effects will ALWAYS run on the NEXT frame instead of the current one, incurring a ~16ms delay.\n// Perhaps this is not such a big deal.\n/**\n * Schedule afterPaintEffects flush after the browser paints\n * @param {number} newQueueLength\n * @returns {void}\n */\nfunction afterPaint(newQueueLength) {\n\tif (newQueueLength === 1 || prevRaf !== options.requestAnimationFrame) {\n\t\tprevRaf = options.requestAnimationFrame;\n\t\t(prevRaf || afterNextFrame)(flushAfterPaintEffects);\n\t}\n}\n\n/**\n * @param {import('./internal').HookState} hook\n * @returns {void}\n */\nfunction invokeCleanup(hook) {\n\t// A hook cleanup can introduce a call to render which creates a new root, this will call options.vnode\n\t// and move the currentComponent away.\n\tconst comp = currentComponent;\n\tlet cleanup = hook._cleanup;\n\tif (typeof cleanup == 'function') {\n\t\thook._cleanup = undefined;\n\t\tcleanup();\n\t}\n\n\tcurrentComponent = comp;\n}\n\n/**\n * Invoke a Hook's effect\n * @param {import('./internal').EffectHookState} hook\n * @returns {void}\n */\nfunction invokeEffect(hook) {\n\t// A hook call can introduce a call to render which creates a new root, this will call options.vnode\n\t// and move the currentComponent away.\n\tconst comp = currentComponent;\n\thook._cleanup = hook._value();\n\tcurrentComponent = comp;\n}\n\n/**\n * @param {unknown[]} oldArgs\n * @param {unknown[]} newArgs\n * @returns {boolean}\n */\nfunction argsChanged(oldArgs, newArgs) {\n\treturn (\n\t\t!oldArgs ||\n\t\toldArgs.length !== newArgs.length ||\n\t\tnewArgs.some((arg, index) => arg !== oldArgs[index])\n\t);\n}\n\n/**\n * @template Arg\n * @param {Arg} arg\n * @param {(arg: Arg) => any} f\n * @returns {any}\n */\nfunction invokeOrReturn(arg, f) {\n\treturn typeof f == 'function' ? f(arg) : f;\n}\n"], "mappings": ";;;;;;AACO;;;;;;;;;;;;;;;ACUA,SAASA,EAAOC,IAAKC,IAAAA;AAE3B,WAASC,MAAKD,GAAOD,CAAAA,GAAIE,EAAAA,IAAKD,GAAMC,EAAAA;AACpC,SAA6BF;AAC9B;AAQgB,SAAAG,EAAWC,IAAAA;AACtBA,EAAAA,MAAQA,GAAKC,cAAYD,GAAKC,WAAWC,YAAYF,EAAAA;AAC1D;AEVgB,SAAAG,EAAcC,IAAMP,IAAOQ,IAAAA;AAC1C,MACCC,IACAC,IACAT,IAHGU,KAAkB,CAAA;AAItB,OAAKV,MAAKD,GACA,UAALC,KAAYQ,KAAMT,GAAMC,EAAAA,IACd,SAALA,KAAYS,KAAMV,GAAMC,EAAAA,IAC5BU,GAAgBV,EAAAA,IAAKD,GAAMC,EAAAA;AAUjC,MAPIW,UAAUC,SAAS,MACtBF,GAAgBH,WACfI,UAAUC,SAAS,IAAIC,EAAMC,KAAKH,WAAW,CAAA,IAAKJ,KAKjC,cAAA,OAARD,MHjBQ,QGiBcA,GAAKS,aACrC,MAAKf,MAAKM,GAAKS,aHjBQC,SGkBlBN,GAAgBV,EAAAA,MACnBU,GAAgBV,EAAAA,IAAKM,GAAKS,aAAaf,EAAAA;AAK1C,SAAOiB,EAAYX,IAAMI,IAAiBF,IAAKC,IHzB5B,IAAA;AG0BpB;AAcgB,SAAAQ,EAAYX,IAAMP,IAAOS,IAAKC,IAAKS,IAAAA;AAIlD,MAAMC,KAAQ,EACbb,MAAAA,IACAP,OAAAA,IACAS,KAAAA,IACAC,KAAAA,IACAW,KHjDkB,MGkDlBC,IHlDkB,MGmDlBC,KAAQ,GACRC,KHpDkB,MGqDlBC,KHrDkB,MGsDlBC,aAAAA,QACAC,KHvDkB,QGuDPR,KAAAA,EAAqBS,IAAUT,IAC1CU,KAAAA,IACAC,KAAQ,EAAA;AAMT,SH/DmB,QG6DfX,MH7De,QG6DKY,EAAQX,SAAeW,EAAQX,MAAMA,EAAAA,GAEtDA;AACR;AAAA,SAEgBY,IAAAA;AACf,SAAO,EAAEC,SHnEU,KAAA;AGoEpB;AAEgB,SAAAC,EAASlC,IAAAA;AACxB,SAAOA,GAAMQ;AACd;AC3EO,SAAS2B,EAAcnC,IAAOoC,IAAAA;AACpCC,OAAKrC,QAAQA,IACbqC,KAAKD,UAAUA;AAChB;AAAA,SA0EgBE,EAAclB,IAAOmB,IAAAA;AACpC,MJ3EmB,QI2EfA,GAEH,QAAOnB,GAAKE,KACTgB,EAAclB,GAAKE,IAAUF,GAAKS,MAAU,CAAA,IJ9E7B;AImFnB,WADIW,IACGD,KAAanB,GAAKC,IAAWR,QAAQ0B,KAG3C,KJtFkB,SIoFlBC,KAAUpB,GAAKC,IAAWkB,EAAAA,MJpFR,QIsFKC,GAAOhB,IAI7B,QAAOgB,GAAOhB;AAShB,SAA4B,cAAA,OAAdJ,GAAMb,OAAqB+B,EAAclB,EAAAA,IJnGpC;AIoGpB;AA2CA,SAASqB,EAAwBrB,IAAAA;AAAjC,MAGWnB,IACJyC;AAHN,MJhJmB,SIgJdtB,KAAQA,GAAKE,OJhJC,QIgJoBF,GAAKK,KAAqB;AAEhE,SADAL,GAAKI,MAAQJ,GAAKK,IAAYkB,OJjJZ,MIkJT1C,KAAI,GAAGA,KAAImB,GAAKC,IAAWR,QAAQZ,KAE3C,KJpJiB,SImJbyC,KAAQtB,GAAKC,IAAWpB,EAAAA,MJnJX,QIoJIyC,GAAKlB,KAAe;AACxCJ,MAAAA,GAAKI,MAAQJ,GAAKK,IAAYkB,OAAOD,GAAKlB;AAC1C;IACD;AAGD,WAAOiB,EAAwBrB,EAAAA;EAChC;AACD;AA4BgB,SAAAwB,EAAcC,IAAAA;AAAAA,GAAAA,CAE1BA,GAACC,QACDD,GAACC,MAAAA,SACFC,EAAcC,KAAKH,EAAAA,KAAAA,CAClBI,EAAOC,SACTC,KAAgBpB,EAAQqB,wBAExBD,IAAepB,EAAQqB,sBACNC,GAAOJ,CAAAA;AAE1B;AASA,SAASA,IAAAA;AAMR,WALIJ,IAnGoBS,IAOjBC,IANHC,IACHC,IACAC,IACAC,IAgGAC,KAAI,GAIEb,EAAclC,SAOhBkC,GAAclC,SAAS+C,MAC1Bb,EAAcc,KAAKC,CAAAA,GAGpBjB,KAAIE,EAAcgB,MAAAA,GAClBH,KAAIb,EAAclC,QAEdgC,GAACC,QA/GCS,KAAAA,QALNE,MADGD,MADoBF,KAuHNT,IAtHMlB,KACNH,KACjBkC,KAAc,CAAA,GACdC,KAAW,CAAA,GAERL,GAASU,SACNT,KAAWzD,EAAO,CAAA,GAAI0D,EAAAA,GACpB7B,MAAa6B,GAAQ7B,MAAa,GACtCI,EAAQX,SAAOW,EAAQX,MAAMmC,EAAAA,GAEjCU,EACCX,GAASU,KACTT,IACAC,IACAF,GAASY,KACTZ,GAASU,IAAYG,cJzII,KI0IzBX,GAAQ1B,MAAyB,CAAC2B,EAAAA,IJ3HjB,MI4HjBC,IJ5HiB,QI6HjBD,KAAiBnB,EAAckB,EAAAA,IAAYC,IAAAA,CAAAA,EJ5IlB,KI6ItBD,GAAQ1B,MACX6B,EAAAA,GAGDJ,GAAQ5B,MAAa6B,GAAQ7B,KAC7B4B,GAAQjC,GAAAD,IAAmBkC,GAAQ1B,GAAAA,IAAW0B,IAC9Ca,EAAWV,IAAaH,IAAUI,EAAAA,GAE9BJ,GAAQ/B,OAASiC,MACpBhB,EAAwBc,EAAAA;AA6F1BN,IAAOC,MAAkB;AAC1B;AAAA,SG3MgBmB,EACfC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAlB,IACAD,IACAoB,IACAlB,IAAAA;AAAAA,MAEI1D,IAEHuD,IAEAsB,IAEAC,IAEAC,IAiCIC,IA5BDC,KAAeT,MAAkBA,GAAcpD,OAAe8D,GAE9DC,KAAoBb,GAAa1D;AAUrC,OARA4C,KAAS4B,EACRb,IACAD,IACAW,IACAzB,IACA2B,EAAAA,GAGInF,KAAI,GAAGA,KAAImF,IAAmBnF,KPhEhB,UOiElB6E,KAAaN,GAAcnD,IAAWpB,EAAAA,OAMrCuD,KAAAA,MADGsB,GAAUjD,MACFyD,IAEAJ,GAAYJ,GAAUjD,GAAAA,KAAYyD,GAI9CR,GAAUjD,MAAU5B,IAGhBgF,KAAShB,EACZK,IACAQ,IACAtB,IACAkB,IACAC,IACAC,IACAlB,IACAD,IACAoB,IACAlB,EAAAA,GAIDoB,KAASD,GAAUtD,KACfsD,GAAWpE,OAAO8C,GAAS9C,OAAOoE,GAAWpE,QAC5C8C,GAAS9C,OACZ6E,EAAS/B,GAAS9C,KPjGF,MOiGaoE,EAAAA,GAE9BnB,GAASX,KACR8B,GAAWpE,KACXoE,GAAUrD,OAAesD,IACzBD,EAAAA,IPtGgB,QO0GdE,MP1Gc,QO0GWD,OAC5BC,KAAgBD,KPtHS,IO0HzBD,GAAUhD,OACV0B,GAAQnC,QAAeyD,GAAUzD,MAEjCoC,KAAS+B,EAAOV,IAAYrB,IAAQa,EAAAA,IACA,cAAA,OAAnBQ,GAAWvE,QAAAA,WAAsB0E,KAClDxB,KAASwB,KACCF,OACVtB,KAASsB,GAAOU,cAIjBX,GAAUhD,OAAAA;AAKX,SAFA0C,GAAchD,MAAQwD,IAEfvB;AACR;AAOA,SAAS4B,EACRb,IACAD,IACAW,IACAzB,IACA2B,IAAAA;AALD,MAQKnF,IAEA6E,IAEAtB,IA8DGkC,IAOAC,IAnEHC,KAAoBV,GAAYrE,QACnCgF,KAAuBD,IAEpBE,KAAO;AAGX,OADAtB,GAAcnD,MAAa,IAAI0E,MAAMX,EAAAA,GAChCnF,KAAI,GAAGA,KAAImF,IAAmBnF,KP3JhB,UO8JlB6E,KAAaP,GAAatE,EAAAA,MAIJ,aAAA,OAAd6E,MACc,cAAA,OAAdA,MA8CFY,KAAczF,KAAI6F,KA/BvBhB,KAAaN,GAAcnD,IAAWpB,EAAAA,IANjB,YAAA,OAAd6E,MACc,YAAA,OAAdA,MAEc,YAAA,OAAdA,MACPA,GAAWpD,eAAesE,SAEiB9E,EPlL1B,MOoLhB4D,IPpLgB,MAAA,MAAA,IAAA,IOyLPmB,EAAQnB,EAAAA,IACyB5D,EAC1CgB,GACA,EAAE1B,UAAUsE,GAAAA,GP5LI,MAAA,MAAA,IAAA,IACK7D,QOgMZ6D,GAAWpD,eAA4BoD,GAAUvD,MAAU,IAK1BL,EAC1C4D,GAAWvE,MACXuE,GAAW9E,OACX8E,GAAWrE,KACXqE,GAAWpE,MAAMoE,GAAWpE,MP1MZ,MO2MhBoE,GAAUnD,GAAAA,IAGgCmD,IAIlCxD,KAAWkD,IACrBM,GAAUvD,MAAUiD,GAAcjD,MAAU,GAY5CiC,KP/NkB,MAAA,OOwNZmC,KAAiBb,GAAUjD,MAAUqE,EAC1CpB,IACAI,IACAQ,IACAG,EAAAA,OAMAA,OADArC,KAAW0B,GAAYS,EAAAA,OAGtBnC,GAAQ1B,OP7OW,KASH,QO2OC0B,MP3OD,QO2OqBA,GAAQ7B,OAAAA,MAG1CgE,OAeCP,KAAoBQ,KACvBE,OACUV,KAAoBQ,MAC9BE,OAK4B,cAAA,OAAnBhB,GAAWvE,SACrBuE,GAAUhD,OPjRc,MOmRf6D,MAAiBD,OAiBvBC,MAAiBD,KAAc,IAClCI,OACUH,MAAiBD,KAAc,IACzCI,QAEIH,KAAgBD,KACnBI,OAEAA,MAMDhB,GAAUhD,OPlTc,OOgLzB0C,GAAcnD,IAAWpB,EAAAA,IPrKR;AOgTnB,MAAI4F,GACH,MAAK5F,KAAI,GAAGA,KAAI2F,IAAmB3F,KPjTjB,UOkTjBuD,KAAW0B,GAAYjF,EAAAA,MACgC,MP5TnC,IO4TKuD,GAAQ1B,SAC5B0B,GAAQhC,OAASiC,OACpBA,KAASnB,EAAckB,EAAAA,IAGxB2C,EAAQ3C,IAAUA,EAAAA;AAKrB,SAAOC;AACR;AAQA,SAAS+B,EAAOY,IAAa3C,IAAQa,IAAAA;AAArC,MAIM9D,IACKP;AAFV,MAA+B,cAAA,OAApBmG,GAAY7F,MAAoB;AAE1C,SADIC,KAAW4F,GAAW/E,KACjBpB,KAAI,GAAGO,MAAYP,KAAIO,GAASK,QAAQZ,KAC5CO,CAAAA,GAASP,EAAAA,MAKZO,GAASP,EAAAA,EAAEqB,KAAW8E,IACtB3C,KAAS+B,EAAOhF,GAASP,EAAAA,GAAIwD,IAAQa,EAAAA;AAIvC,WAAOb;EACR;AAAW2C,EAAAA,GAAW5E,OAASiC,OAC1BA,MAAU2C,GAAY7F,QAAAA,CAAS+D,GAAU+B,SAAS5C,EAAAA,MACrDA,KAASnB,EAAc8D,EAAAA,IAExB9B,GAAUgC,aAAaF,GAAW5E,KAAOiC,MP3VvB,IAAA,GO4VlBA,KAAS2C,GAAW5E;AAGrB,KAAA;AACCiC,IAAAA,KAASA,MAAUA,GAAOgC;EAAAA,SPhWR,QOiWVhC,MAAqC,KAAnBA,GAAO8C;AAElC,SAAO9C;AACR;AAAA,SAQgB+C,EAAahG,IAAUiG,IAAAA;AAUtC,SATAA,KAAMA,MAAO,CAAA,GP7WM,QO8WfjG,MAAuC,aAAA,OAAZA,OACpByF,EAAQzF,EAAAA,IAClBA,GAASkG,KAAK,SAAAhE,IAAAA;AACb8D,MAAa9D,IAAO+D,EAAAA;EACrB,CAAA,IAEAA,GAAIzD,KAAKxC,EAAAA,IAEHiG;AACR;AASA,SAASP,EACRpB,IACAI,IACAQ,IACAG,IAAAA;AAJD,MAmCMc,IACAC,IA9BCnG,KAAMqE,GAAWrE,KACjBF,KAAOuE,GAAWvE,MACpBiD,KAAW0B,GAAYQ,EAAAA;AAkB3B,MP1ZmB,SO2ZjBlC,MAAuC,QAAlBsB,GAAWrE,OAChC+C,MACA/C,MAAO+C,GAAS/C,OAChBF,MAAQiD,GAASjD,QACc,MPxaX,IOwanBiD,GAAQ1B,KAEV,QAAO4D;AAAAA,MAVPG,MPvZkB,QOwZjBrC,MAAmD,MPja/B,IOiaCA,GAAQ1B,OAA0B,IAAI,GAa5D,MAFI6E,KAAIjB,KAAc,GAClBkB,KAAIlB,KAAc,GACfiB,MAAK,KAAKC,KAAI1B,GAAYrE,UAAQ;AACxC,QAAI8F,MAAK,GAAG;AAEX,WADAnD,KAAW0B,GAAYyB,EAAAA,MAGS,MPnbb,IOmbjBnD,GAAQ1B,QACTrB,MAAO+C,GAAS/C,OAChBF,MAAQiD,GAASjD,KAEjB,QAAOoG;AAERA,MAAAA;IACD;AAEA,QAAIC,KAAI1B,GAAYrE,QAAQ;AAE3B,WADA2C,KAAW0B,GAAY0B,EAAAA,MAGS,MPhcb,IOgcjBpD,GAAQ1B,QACTrB,MAAO+C,GAAS/C,OAChBF,MAAQiD,GAASjD,KAEjB,QAAOqG;AAERA,MAAAA;IACD;EACD;AAGD,SAAA;AACD;AFhdA,SAASC,EAASC,IAAOrG,IAAKsG,IAAAA;AACf,SAAVtG,GAAI,CAAA,IACPqG,GAAME,YAAYvG,ILWA,QKXKsG,KAAgB,KAAKA,EAAAA,IAE5CD,GAAMrG,EAAAA,ILSY,QKVRsG,KACG,KACa,YAAA,OAATA,MAAqBE,EAAmBC,KAAKzG,EAAAA,IACjDsG,KAEAA,KAAQ;AAEvB;AAyBgB,SAAAC,EAAYG,IAAKC,IAAML,IAAOM,IAAU1C,IAAAA;AACvD,MAAI2C;AAEJC,IAAG,KAAY,WAARH,GACN,KAAoB,YAAA,OAATL,GACVI,CAAAA,GAAIL,MAAMU,UAAUT;OACd;AAKN,QAJuB,YAAA,OAAZM,OACVF,GAAIL,MAAMU,UAAUH,KAAW,KAG5BA,GACH,MAAKD,MAAQC,GACNN,CAAAA,MAASK,MAAQL,MACtBF,EAASM,GAAIL,OAAOM,IAAM,EAAA;AAK7B,QAAIL,GACH,MAAKK,MAAQL,GACPM,CAAAA,MAAYN,GAAMK,EAAAA,KAASC,GAASD,EAAAA,KACxCP,EAASM,GAAIL,OAAOM,IAAML,GAAMK,EAAAA,CAAAA;EAIpC;WAGmB,OAAXA,GAAK,CAAA,KAAwB,OAAXA,GAAK,CAAA,EAC/BE,CAAAA,KAAaF,OAASA,KAAOA,GAAKK,QAAQC,GAAe,IAAA,IAQxDN,KAJAA,GAAKO,YAAAA,KAAiBR,MACd,gBAARC,MACQ,eAARA,KAEOA,GAAKO,YAAAA,EAAc7G,MAAM,CAAA,IACrBsG,GAAKtG,MAAM,CAAA,GAElBqG,GAAGvD,MAAauD,GAAGvD,IAAc,CAAA,IACtCuD,GAAGvD,EAAYwD,KAAOE,EAAAA,IAAcP,IAEhCA,KACEM,KAQJN,GAAMa,IAAYP,GAASO,KAP3Bb,GAAMa,IAAYC,GAClBV,GAAIW,iBACHV,IACAE,KAAaS,IAAoBC,GACjCV,EAAAA,KAMFH,GAAIc,oBACHb,IACAE,KAAaS,IAAoBC,GACjCV,EAAAA;OAGI;AACN,QLzF2B,gCKyFvB3C,GAIHyC,CAAAA,KAAOA,GAAKK,QAAQ,eAAe,GAAA,EAAKA,QAAQ,UAAU,GAAA;aAElD,WAARL,MACQ,YAARA,MACQ,UAARA,MACQ,UAARA,MACQ,UAARA,MAGQ,cAARA,MACQ,cAARA,MACQ,aAARA,MACQ,aAARA,MACQ,UAARA,MACQ,aAARA,MACAA,MAAQD,GAER,KAAA;AACCA,MAAAA,GAAIC,EAAAA,IL3GY,QK2GJL,KAAgB,KAAKA;AAEjC,YAAMQ;IAAAA,SACEW,IAAAA;IAAAA;AAUU,kBAAA,OAATnB,OLxHO,QK0HPA,MAAAA,UAAkBA,MAA8B,OAAXK,GAAK,CAAA,IAGpDD,GAAIgB,gBAAgBf,EAAAA,IAFpBD,GAAIiB,aAAahB,IAAc,aAARA,MAA8B,KAATL,KAAgB,KAAKA,EAAAA;EAInE;AACD;AAOA,SAASsB,EAAiBf,IAAAA;AAMzB,SAAA,SAAiBY,IAAAA;AAChB,QAAI7F,KAAIuB,GAAa;AACpB,UAAM0E,KAAejG,KAAIuB,EAAYsE,GAAE3H,OAAO+G,EAAAA;AAC9C,ULhJiB,QKgJbY,GAAEK,EACLL,CAAAA,GAAEK,IAAcV;eAKNK,GAAEK,IAAcD,GAAaV,EACvC;AAED,aAAOU,GAAavG,EAAQyG,QAAQzG,EAAQyG,MAAMN,EAAAA,IAAKA,EAAAA;IACxD;EACD;AACD;AAAA,SG5HgBjE,EACfK,IACAf,IACAC,IACAkB,IACAC,IACAC,IACAlB,IACAD,IACAoB,IACAlB,IAAAA;AAAAA,MAGI8E,IAkBE5F,IAAG6F,IAAOC,IAAUC,IAAUC,IAAUC,IACxCC,IACEC,IAMFC,IACAC,IAyGOjJ,IA4BPkJ,IACHC,IASSnJ,IA6BNsE,IAgDOtE,IAtPZoJ,KAAU9F,GAAShD;AAIpB,MRjDwBU,QQiDpBsC,GAAS7B,YAA0B,QRlDpB;AAbU,QQkEzB8B,GAAQ1B,QACX+C,KAAAA,CAAAA,ERrE0B,KQqETrB,GAAQ1B,MAEzB8C,KAAoB,CADpBnB,KAASF,GAAQ/B,MAAQgC,GAAQhC,GAAAA,KAI7BiH,KAAM1G,EAAOR,QAASkH,GAAIlF,EAAAA;AAE/B+F,IAAO,KAAsB,cAAA,OAAXD,GACjB,KAAA;AAkEC,QAhEIN,KAAWxF,GAASvD,OAClBgJ,KACL,eAAeK,MAAWA,GAAQE,UAAUC,QAKzCP,MADJR,KAAMY,GAAQI,gBACQ/E,GAAc+D,GAAGhH,GAAAA,GACnCyH,KAAmBT,KACpBQ,KACCA,GAASjJ,MAAM+G,QACf0B,GAAGnH,KACJoD,IAGClB,GAAQ/B,MAEXqH,MADAjG,KAAIU,GAAQ9B,MAAc+B,GAAQ/B,KACNH,KAAwBuB,GAAC6G,OAGjDV,KAEHzF,GAAQ9B,MAAcoB,KAAI,IAAIwG,GAAQN,IAAUG,EAAAA,KAGhD3F,GAAQ9B,MAAcoB,KAAI,IAAIV,EAC7B4G,IACAG,EAAAA,GAEDrG,GAAEnB,cAAc2H,IAChBxG,GAAE2G,SAASG,IAERV,MAAUA,GAASW,IAAI/G,EAAAA,GAE3BA,GAAE7C,QAAQ+I,IACLlG,GAAEgH,UAAOhH,GAAEgH,QAAQ,CAAA,IACxBhH,GAAET,UAAU8G,IACZrG,GAACqB,MAAkBQ,IACnBgE,KAAQ7F,GAACC,MAAAA,MACTD,GAACiH,MAAoB,CAAA,GACrBjH,GAACkH,MAAmB,CAAA,IAIjBf,MR5Ga,QQ4GOnG,GAACmH,QACxBnH,GAACmH,MAAcnH,GAAEgH,QAGdb,MRhHa,QQgHOK,GAAQY,6BAC3BpH,GAACmH,OAAenH,GAAEgH,UACrBhH,GAACmH,MAAclK,EAAO,CAAA,GAAI+C,GAACmH,GAAAA,IAG5BlK,EACC+C,GAACmH,KACDX,GAAQY,yBAAyBlB,IAAUlG,GAACmH,GAAAA,CAAAA,IAI9CrB,KAAW9F,GAAE7C,OACb4I,KAAW/F,GAAEgH,OACbhH,GAAClB,MAAU4B,IAGPmF,GAEFM,CAAAA,MRlIe,QQmIfK,GAAQY,4BRnIO,QQoIfpH,GAAEqH,sBAEFrH,GAAEqH,mBAAAA,GAGClB,MRzIY,QQyIQnG,GAAEsH,qBACzBtH,GAACiH,IAAkB9G,KAAKH,GAAEsH,iBAAAA;SAErB;AAUN,UARCnB,MR9Ie,QQ+IfK,GAAQY,4BACRlB,OAAaJ,MRhJE,QQiJf9F,GAAEuH,6BAEFvH,GAAEuH,0BAA0BrB,IAAUG,EAAAA,GAAAA,CAIpCrG,GAACrB,ORvJY,QQwJdqB,GAAEwH,yBAAAA,UACFxH,GAAEwH,sBACDtB,IACAlG,GAACmH,KACDd,EAAAA,KAEF3F,GAAQ5B,OAAc6B,GAAQ7B,KAC7B;AAkBD,aAhBI4B,GAAQ5B,OAAc6B,GAAQ7B,QAKjCkB,GAAE7C,QAAQ+I,IACVlG,GAAEgH,QAAQhH,GAACmH,KACXnH,GAACC,MAAAA,QAGFS,GAAQ/B,MAAQgC,GAAQhC,KACxB+B,GAAQlC,MAAamC,GAAQnC,KAC7BkC,GAAQlC,IAAWqF,KAAK,SAAAtF,IAAAA;AACnBA,UAAAA,OAAOA,GAAKE,KAAWiC;QAC5B,CAAA,GAEStD,KAAI,GAAGA,KAAI4C,GAACkH,IAAiBlJ,QAAQZ,KAC7C4C,CAAAA,GAACiH,IAAkB9G,KAAKH,GAACkH,IAAiB9J,EAAAA,CAAAA;AAE3C4C,QAAAA,GAACkH,MAAmB,CAAA,GAEhBlH,GAACiH,IAAkBjJ,UACtB6C,GAAYV,KAAKH,EAAAA;AAGlB,cAAMyG;MACP;AR3LgB,cQ6LZzG,GAAEyH,uBACLzH,GAAEyH,oBAAoBvB,IAAUlG,GAACmH,KAAad,EAAAA,GAG3CF,MRjMY,QQiMQnG,GAAE0H,sBACzB1H,GAACiH,IAAkB9G,KAAK,WAAA;AACvBH,QAAAA,GAAE0H,mBAAmB5B,IAAUC,IAAUC,EAAAA;MAC1C,CAAA;IAEF;AASA,QAPAhG,GAAET,UAAU8G,IACZrG,GAAE7C,QAAQ+I,IACVlG,GAACmB,MAAcM,IACfzB,GAACrB,MAAAA,OAEG2H,KAAapH,EAAOmB,KACvBkG,KAAQ,GACLJ,IAAkB;AAQrB,WAPAnG,GAAEgH,QAAQhH,GAACmH,KACXnH,GAACC,MAAAA,OAEGqG,MAAYA,GAAW5F,EAAAA,GAE3BkF,KAAM5F,GAAE2G,OAAO3G,GAAE7C,OAAO6C,GAAEgH,OAAOhH,GAAET,OAAAA,GAE1BnC,KAAI,GAAGA,KAAI4C,GAACkH,IAAiBlJ,QAAQZ,KAC7C4C,CAAAA,GAACiH,IAAkB9G,KAAKH,GAACkH,IAAiB9J,EAAAA,CAAAA;AAE3C4C,MAAAA,GAACkH,MAAmB,CAAA;IACrB,MACC,IAAA;AACClH,MAAAA,GAACC,MAAAA,OACGqG,MAAYA,GAAW5F,EAAAA,GAE3BkF,KAAM5F,GAAE2G,OAAO3G,GAAE7C,OAAO6C,GAAEgH,OAAOhH,GAAET,OAAAA,GAGnCS,GAAEgH,QAAQhH,GAACmH;IAAAA,SACHnH,GAACC,OAAAA,EAAasG,KAAQ;AAIhCvG,IAAAA,GAAEgH,QAAQhH,GAACmH,KRxOM,QQ0ObnH,GAAE2H,oBACL9F,KAAgB5E,EAAOA,EAAO,CAAE,GAAE4E,EAAAA,GAAgB7B,GAAE2H,gBAAAA,CAAAA,IAGjDxB,MAAAA,CAAqBN,MR9OR,QQ8OiB7F,GAAE4H,4BACnC5B,KAAWhG,GAAE4H,wBAAwB9B,IAAUC,EAAAA,IAK5CrE,KAAekE,IRpPF,QQmPhBA,MAAeA,GAAIlI,SAAS2B,KRnPZ,QQmPwBuG,GAAIhI,QAI5C8D,KAAemG,EAAUjC,GAAIzI,MAAMQ,QAAAA,IAGpCiD,KAASY,EACRC,IACA2B,EAAQ1B,EAAAA,IAAgBA,KAAe,CAACA,EAAAA,GACxChB,IACAC,IACAkB,IACAC,IACAC,IACAlB,IACAD,IACAoB,IACAlB,EAAAA,GAGDd,GAAEF,OAAOY,GAAQ/B,KAGjB+B,GAAQzB,OAAAA,MAEJe,GAACiH,IAAkBjJ,UACtB6C,GAAYV,KAAKH,EAAAA,GAGdiG,OACHjG,GAAC6G,MAAiB7G,GAACvB,KRlRH;EQ6SlB,SAzBS4G,IAAAA;AAGR,QAFA3E,GAAQ5B,MRrRS,MQuRbkD,MRvRa,QQuRED,GAClB,KAAIsD,GAAEyC,MAAM;AAKX,WAJApH,GAAQzB,OAAW+C,KAChB+F,MRvSsB,KQ0SlBnH,MAA6B,KAAnBA,GAAO8C,YAAiB9C,GAAOgC,cAC/ChC,CAAAA,KAASA,GAAOgC;AAGjBb,MAAAA,GAAkBA,GAAkBiG,QAAQpH,EAAAA,CAAAA,IRjS7B,MQkSfF,GAAQ/B,MAAQiC;IACjB,MACC,MAASxD,KAAI2E,GAAkB/D,QAAQZ,OACtCC,GAAW0E,GAAkB3E,EAAAA,CAAAA;QAI/BsD,CAAAA,GAAQ/B,MAAQgC,GAAQhC,KACxB+B,GAAQlC,MAAamC,GAAQnC;AAE9BU,MAAOP,IAAa0G,IAAG3E,IAAUC,EAAAA;EAClC;MR7SkB,SQ+SlBoB,MACArB,GAAQ5B,OAAc6B,GAAQ7B,OAE9B4B,GAAQlC,MAAamC,GAAQnC,KAC7BkC,GAAQ/B,MAAQgC,GAAQhC,OAExBiC,KAASF,GAAQ/B,MAAQsJ,EACxBtH,GAAQhC,KACR+B,IACAC,IACAkB,IACAC,IACAC,IACAlB,IACAmB,IACAlB,EAAAA;AAMF,UAFK8E,KAAM1G,EAAQgJ,WAAStC,GAAIlF,EAAAA,GR/UH,MQiVtBA,GAAQzB,MAAAA,SAAuC2B;AACvD;AAAA,SAOgBW,EAAWV,IAAasH,IAAMrH,IAAAA;AAC7C,WAAS1D,KAAI,GAAGA,KAAI0D,GAAS9C,QAAQZ,KACpCsF,GAAS5B,GAAS1D,EAAAA,GAAI0D,GAAAA,EAAW1D,EAAAA,GAAI0D,GAAAA,EAAW1D,EAAAA,CAAAA;AAG7C8B,IAAON,OAAUM,EAAON,IAASuJ,IAAMtH,EAAAA,GAE3CA,GAAYgD,KAAK,SAAA7D,IAAAA;AAChB,QAAA;AAECa,MAAAA,KAAcb,GAACiH,KACfjH,GAACiH,MAAoB,CAAA,GACrBpG,GAAYgD,KAAK,SAAAuE,IAAAA;AAEhBA,QAAAA,GAAGlK,KAAK8B,EAAAA;MACT,CAAA;IAGD,SAFSqF,IAAAA;AACRnG,QAAOP,IAAa0G,IAAGrF,GAAClB,GAAAA;IACzB;EACD,CAAA;AACD;AAEA,SAAS+I,EAAUvK,IAAAA;AAClB,SACgB,YAAA,OAARA,MRpWW,QQqWlBA,MACCA,GAAIoB,OAAWpB,GAAIoB,MAAU,IAEvBpB,KAGJ8F,EAAQ9F,EAAAA,IACJA,GAAK+K,IAAIR,CAAAA,IAGV5K,EAAO,CAAE,GAAEK,EAAAA;AACnB;AAiBA,SAAS2K,EACR3D,IACA5D,IACAC,IACAkB,IACAC,IACAC,IACAlB,IACAmB,IACAlB,IAAAA;AATD,MAeK1D,IAEAkL,IAEAC,IAEAC,IACAtE,IACAuE,IACAC,IAbA5C,KAAWnF,GAASxD,OACpB+I,KAAWxF,GAASvD,OACpBuG,KAAkChD,GAAShD;AAkB/C,MAJgB,SAAZgG,KAAmB5B,KRhaK,+BQiaP,UAAZ4B,KAAoB5B,KR/ZA,uCQganBA,OAAWA,KRjaS,iCAGX,QQgafC;AACH,SAAK3E,KAAI,GAAGA,KAAI2E,GAAkB/D,QAAQZ,KAMzC,MALA8G,KAAQnC,GAAkB3E,EAAAA,MAOzB,kBAAkB8G,MAAAA,CAAAA,CAAWR,OAC5BA,KAAWQ,GAAMyE,aAAajF,KAA6B,KAAlBQ,GAAMR,WAC/C;AACDY,MAAAA,KAAMJ,IACNnC,GAAkB3E,EAAAA,IR7aF;AQ8ahB;IACD;;AAIF,MRnbmB,QQmbfkH,IAAa;AAChB,QRpbkB,QQobdZ,GACH,QAAOkF,SAASC,eAAe3C,EAAAA;AAGhC5B,IAAAA,KAAMsE,SAASE,gBACdhH,IACA4B,IACAwC,GAAS6C,MAAM7C,EAAAA,GAKZlE,OACC9C,EAAO8J,OACV9J,EAAO8J,IAAoBtI,IAAUqB,EAAAA,GACtCC,KAAAA,QAGDD,KRtckB;EQucnB;AAEA,MRzcmB,QQycf2B,GAECoC,CAAAA,OAAaI,MAAclE,MAAesC,GAAI2E,QAAQ/C,OACzD5B,GAAI2E,OAAO/C;OAEN;AASN,QAPAnE,KAAoBA,MAAqB9D,EAAMC,KAAKoG,GAAI4E,UAAAA,GAExDpD,KAAWnF,GAASxD,SAASsF,GAAAA,CAKxBT,MRvda,QQudED,GAEnB,MADA+D,KAAW,CAAA,GACN1I,KAAI,GAAGA,KAAIkH,GAAI6E,WAAWnL,QAAQZ,KAEtC0I,CAAAA,IADA5B,KAAQI,GAAI6E,WAAW/L,EAAAA,GACRmH,IAAAA,IAAQL,GAAMA;AAI/B,SAAK9G,MAAK0I,GAET,KADA5B,KAAQ4B,GAAS1I,EAAAA,GACR,cAALA,GAAAA;aACY,6BAALA,GACVmL,CAAAA,KAAUrE;aACJ,EAAM9G,MAAK8I,KAAW;AAC5B,UACO,WAAL9I,MAAgB,kBAAkB8I,MAC7B,aAAL9I,MAAkB,oBAAoB8I,GAEvC;AAED/B,QAAYG,IAAKlH,IR3eD,MQ2eU8G,IAAOpC,EAAAA;IAClC;AAKD,SAAK1E,MAAK8I,GACThC,CAAAA,KAAQgC,GAAS9I,EAAAA,GACR,cAALA,KACHoL,KAActE,KACC,6BAAL9G,KACVkL,KAAUpE,KACK,WAAL9G,KACVqL,KAAavE,KACE,aAAL9G,KACVsL,KAAUxE,KAERlC,MAA+B,cAAA,OAATkC,MACxB4B,GAAS1I,EAAAA,MAAO8G,MAEhBC,EAAYG,IAAKlH,IAAG8G,IAAO4B,GAAS1I,EAAAA,GAAI0E,EAAAA;AAK1C,QAAIwG,GAGDtG,CAAAA,MACCuG,OACAD,GAAOc,UAAWb,GAAOa,UAAWd,GAAOc,UAAW9E,GAAI+E,eAE5D/E,GAAI+E,YAAYf,GAAOc,SAGxB1I,GAAQlC,MAAa,CAAA;aAEjB+J,OAASjE,GAAI+E,YAAY,KAE7B7H,EAEkB,cAAjBd,GAAShD,OAAqB4G,GAAIgF,UAAUhF,IAC5ClB,EAAQoF,EAAAA,IAAeA,KAAc,CAACA,EAAAA,GACtC9H,IACAC,IACAkB,IACY,mBAAZ6B,KR5hB2B,iCQ4hBqB5B,IAChDC,IACAlB,IACAkB,KACGA,GAAkB,CAAA,IAClBpB,GAAQnC,OAAciB,EAAckB,IAAU,CAAA,GACjDqB,IACAlB,EAAAA,GRhiBgB,QQoiBbiB,GACH,MAAK3E,KAAI2E,GAAkB/D,QAAQZ,OAClCC,GAAW0E,GAAkB3E,EAAAA,CAAAA;AAM3B4E,IAAAA,OACJ5E,KAAI,SACY,cAAZsG,MR9iBa,QQ8iBa+E,KAC7BnE,GAAIgB,gBAAgB,OAAA,IR9iBClH,QQgjBrBqK,OAKCA,OAAenE,GAAIlH,EAAAA,KACN,cAAZsG,MAAAA,CAA2B+E,MAIf,YAAZ/E,MAAwB+E,MAAc3C,GAAS1I,EAAAA,MAEjD+G,EAAYG,IAAKlH,IAAGqL,IAAY3C,GAAS1I,EAAAA,GAAI0E,EAAAA,GAG9C1E,KAAI,WR/jBkBgB,QQgkBlBsK,MAAwBA,MAAWpE,GAAIlH,EAAAA,KAC1C+G,EAAYG,IAAKlH,IAAGsL,IAAS5C,GAAS1I,EAAAA,GAAI0E,EAAAA;EAG7C;AAEA,SAAOwC;AACR;AAQgB,SAAA5B,EAAS7E,IAAKqG,IAAO3F,IAAAA;AACpC,MAAA;AACC,QAAkB,cAAA,OAAPV,IAAmB;AAC7B,UAAI0L,KAAuC,cAAA,OAAhB1L,GAAGoB;AAC1BsK,MAAAA,MAEH1L,GAAGoB,IAAAA,GAGCsK,MRzlBY,QQylBKrF,OAIrBrG,GAAGoB,MAAYpB,GAAIqG,EAAAA;IAErB,MAAOrG,CAAAA,GAAIuB,UAAU8E;EAGtB,SAFSmB,IAAAA;AACRnG,MAAOP,IAAa0G,IAAG9G,EAAAA;EACxB;AACD;AASgB,SAAA+E,EAAQ/E,IAAOgF,IAAaiG,IAAAA;AAA5B,MACXC,IAsBMrM;AAbV,MARI8B,EAAQoE,WAASpE,EAAQoE,QAAQ/E,EAAAA,IAEhCkL,KAAIlL,GAAMV,SACT4L,GAAErK,WAAWqK,GAAErK,WAAWb,GAAKI,OACnC+D,EAAS+G,IRlnBQ,MQknBClG,EAAAA,IRlnBD,SQsnBdkG,KAAIlL,GAAKK,MAAsB;AACnC,QAAI6K,GAAEC,qBACL,KAAA;AACCD,MAAAA,GAAEC,qBAAAA;IAGH,SAFSrE,IAAAA;AACRnG,QAAOP,IAAa0G,IAAG9B,EAAAA;IACxB;AAGDkG,IAAAA,GAAE3J,OAAO2J,GAACtI,MR/nBQ;EQgoBnB;AAEA,MAAKsI,KAAIlL,GAAKC,IACb,MAASpB,KAAI,GAAGA,KAAIqM,GAAEzL,QAAQZ,KACzBqM,CAAAA,GAAErM,EAAAA,KACLkG,EACCmG,GAAErM,EAAAA,GACFmG,IACAiG,MAAmC,cAAA,OAAdjL,GAAMb,IAAAA;AAM1B8L,EAAAA,MACJnM,EAAWkB,GAAKI,GAAAA,GAGjBJ,GAAKK,MAAcL,GAAKE,KAAWF,GAAKI,MAAAA;AACzC;AAGA,SAASmI,EAAS3J,IAAO6J,IAAOzH,IAAAA;AAC/B,SAAA,KAAYV,YAAY1B,IAAOoC,EAAAA;AAChC;AC3pBO,SAASoH,EAAOpI,IAAOkD,IAAWkI,IAAAA;AAAlC,MAWF3H,IAOArB,IAQAE,IACHC;AAzBGW,EAAAA,MAAamH,aAChBnH,KAAYmH,SAASgB,kBAGlB1K,EAAOT,MAAQS,EAAOT,GAAOF,IAAOkD,EAAAA,GAYpCd,MAPAqB,KAAoC,cAAA,OAAf2H,MTRN,OSiBfA,MAAeA,GAAWnL,OAAeiD,GAASjD,KAMlDqC,KAAc,CAAA,GACjBC,KAAW,CAAA,GACZM,EACCK,IAPDlD,MAAAA,CAAWyD,MAAe2H,MAAgBlI,IAASjD,MAClDf,EAAc4B,GTpBI,MSoBY,CAACd,EAAAA,CAAAA,GAU/BoC,MAAY8B,GACZA,GACAhB,GAAUH,cAAAA,CACTU,MAAe2H,KACb,CAACA,EAAAA,IACDhJ,KTnCe,OSqCdc,GAAUoI,aACT5L,EAAMC,KAAKuD,GAAUyH,UAAAA,ITtCR,MSwClBrI,IAAAA,CACCmB,MAAe2H,KACbA,KACAhJ,KACCA,GAAQhC,MACR8C,GAAUoI,YACd7H,IACAlB,EAAAA,GAIDS,EAAWV,IAAatC,IAAOuC,EAAAA;AAChC;AAOO,SAASgJ,EAAQvL,IAAOkD,IAAAA;AAC9BkF,IAAOpI,IAAOkD,IAAWqI,CAAAA;AAC1B;AAAA,SChEgBC,EAAaxL,IAAOpB,IAAOQ,IAAAA;AAAAA,MAEzCC,IACAC,IACAT,IAEGe,IALAL,KAAkBb,EAAO,CAAE,GAAEsB,GAAMpB,KAAAA;AAWvC,OAAKC,MAJDmB,GAAMb,QAAQa,GAAMb,KAAKS,iBAC5BA,KAAeI,GAAMb,KAAKS,eAGjBhB,GACA,UAALC,KAAYQ,KAAMT,GAAMC,EAAAA,IACd,SAALA,KAAYS,KAAMV,GAAMC,EAAAA,IAEhCU,GAAgBV,EAAAA,IVZMgB,QUWdjB,GAAMC,EAAAA,KVXQgB,QUWWD,KACZA,GAAaf,EAAAA,IAEbD,GAAMC,EAAAA;AAS7B,SALIW,UAAUC,SAAS,MACtBF,GAAgBH,WACfI,UAAUC,SAAS,IAAIC,EAAMC,KAAKH,WAAW,CAAA,IAAKJ,KAG7CU,EACNE,GAAMb,MACNI,IACAF,MAAOW,GAAMX,KACbC,MAAOU,GAAMV,KV5BK,IAAA;AU+BpB;AJ1CgB,SAAAmM,EAAcC,IAAAA;AAC7B,WAASC,GAAQ/M,IAAAA;AAAjB,QAGMgN,IACAC;AA+BL,WAlCK5K,KAAKmI,oBAELwC,KAAO,oBAAIE,QACXD,KAAM,CAAE,GACRF,GAAOtL,GAAAA,IAAQY,MAEnBA,KAAKmI,kBAAkB,WAAA;AAAM,aAAAyC;IAAG,GAEhC5K,KAAKkK,uBAAuB,WAAA;AAC3BS,MAAAA,KNAgB;IMCjB,GAEA3K,KAAKgI,wBAAwB,SAAU8C,IAAAA;AAElC9K,WAAKrC,MAAM+G,SAASoG,GAAOpG,SAC9BiG,GAAKI,QAAQ,SAAAvK,IAAAA;AACZA,QAAAA,GAACrB,MAAAA,MACDoB,EAAcC,EAAAA;MACf,CAAA;IAEF,GAEAR,KAAKuH,MAAM,SAAA/G,IAAAA;AACVmK,MAAAA,GAAKK,IAAIxK,EAAAA;AACT,UAAIyK,KAAMzK,GAAE0J;AACZ1J,MAAAA,GAAE0J,uBAAuB,WAAA;AACpBS,QAAAA,MACHA,GAAKO,OAAO1K,EAAAA,GAETyK,MAAKA,GAAIvM,KAAK8B,EAAAA;MACnB;IACD,IAGM7C,GAAMQ;EACd;AAgBA,SAdAuM,GAAOtL,MAAO,SAASxB,KACvB8M,GAAOzL,KAAiBwL,IAQxBC,GAAQS,WACPT,GAAOU,OANRV,GAAQW,WAAW,SAAC1N,IAAO2N,IAAAA;AAC1B,WAAO3N,GAAMQ,SAASmN,EAAAA;EACvB,GAKkBlE,cAChBsD,IAEKA;AACR;AN1DO,IC0BMjM,GChBPiB,GCPFH,GA2FSgM,GCmFT7K,GAWAI,GAEEE,GA0BAS,GC1MA4D,GAaFG,GAkJEG,GACAD,GC5KK9H,GNeEqF,GACAH,GACA8B,GClBAhB;ADDN;;IAiBMX,IAAgC,CAAG;AAjBzC,IAkBMH,IAAY,CAAA;AAlBlB,IAmBM8B,IACZ;AApBM,ICCMhB,IAAUF,MAAME;AAyBhBnF,QAAQqE,EAAUrE,OChBzBiB,IAAU,EACfP,KSDM,SAAqBqM,IAAOzM,IAAOoC,IAAUsK,IAAAA;AAQnD,eANIxK,IAEHyK,IAEAC,IAEO5M,KAAQA,GAAKE,KACpB,MAAKgC,KAAYlC,GAAKK,QAAAA,CAAiB6B,GAAShC,GAC/C,KAAA;AAcC,aAbAyM,KAAOzK,GAAU5B,gBXND,QWQJqM,GAAKE,6BAChB3K,GAAU4K,SAASH,GAAKE,yBAAyBJ,EAAAA,CAAAA,GACjDG,KAAU1K,GAASR,MXVJ,QWaZQ,GAAU6K,sBACb7K,GAAU6K,kBAAkBN,IAAOC,MAAa,CAAE,CAAA,GAClDE,KAAU1K,GAASR,MAIhBkL,GACH,QAAQ1K,GAASoG,MAAiBpG;MAIpC,SAFS4E,IAAAA;AACR2F,QAAAA,KAAQ3F;MACT;AAIF,YAAM2F;IACP,EAAA,GRzCIjM,IAAU,GA2FDgM,IAAiB,SAAAxM,IAAAA;AAAK,aH/Ef,QGgFnBA,MH/EwBH,QG+EPG,GAAMM;IAAwB,GCrEhDS,EAAcoH,UAAU2E,WAAW,SAAUE,IAAQC,IAAAA;AAEpD,UAAIC;AAEHA,MAAAA,KJfkB,QIcfjM,KAAI2H,OAAuB3H,KAAI2H,OAAe3H,KAAKwH,QAClDxH,KAAI2H,MAEJ3H,KAAI2H,MAAclK,EAAO,CAAE,GAAEuC,KAAKwH,KAAAA,GAGlB,cAAA,OAAVuE,OAGVA,KAASA,GAAOtO,EAAO,CAAA,GAAIwO,EAAAA,GAAIjM,KAAKrC,KAAAA,IAGjCoO,MACHtO,EAAOwO,IAAGF,EAAAA,GJ3BQ,QI+BfA,MAEA/L,KAAIV,QACH0M,MACHhM,KAAI0H,IAAiB/G,KAAKqL,EAAAA,GAE3BzL,EAAcP,IAAAA;IAEhB,GAQAF,EAAcoH,UAAUgF,cAAc,SAAUF,IAAAA;AAC3ChM,WAAIV,QAIPU,KAAIb,MAAAA,MACA6M,MAAUhM,KAAIyH,IAAkB9G,KAAKqL,EAAAA,GACzCzL,EAAcP,IAAAA;IAEhB,GAYAF,EAAcoH,UAAUC,SAAStH,GA8F7Ba,IAAgB,CAAA,GAadM,IACa,cAAA,OAAXmL,UACJA,QAAQjF,UAAUoB,KAAK8D,KAAKD,QAAQE,QAAAA,CAAAA,IACpCC,YAuBE7K,IAAY,SAAC8K,IAAGC,IAAAA;AAAAA,aAAMD,GAACjN,IAAAJ,MAAiBsN,GAAClN,IAAAJ;IAAc,GA8B7D0B,EAAOC,MAAkB,GCxOnBwE,IAAgB,+BAalBG,IAAa,GAkJXG,IAAaK,EAAAA,KAAiB,GAC9BN,IAAoBM,EAAAA,IAAiB,GC5KhCpI,IAAI;;;;;;;;;;;;;;;;;;;;AMoIf,SAAS6O,GAAaC,IAAOC,IAAAA;AACxBC,EAAAA,GAAOC,OACVD,GAAOC,IAAOC,IAAkBJ,IAAOK,MAAeJ,EAAAA,GAEvDI,KAAc;AAOd,MAAMC,KACLF,GAAgBG,QACfH,GAAgBG,MAAW,EAC3BC,IAAO,CAAA,GACPL,KAAiB,CAAA,EAAA;AAOnB,SAJIH,MAASM,GAAKE,GAAOC,UACxBH,GAAKE,GAAOE,KAAK,CAAE,CAAA,GAGbJ,GAAKE,GAAOR,EAAAA;AACpB;AAOO,SAASW,GAASC,IAAAA;AAExB,SADAP,KAAc,GACPQ,GAAWC,IAAgBF,EAAAA;AACnC;AAUgB,SAAAC,GAAWE,IAASH,IAAcI,IAAAA;AAEjD,MAAMC,KAAYlB,GAAamB,MAAgB,CAAA;AAE/C,MADAD,GAAUE,IAAWJ,IAAAA,CAChBE,GAASG,QACbH,GAAST,KAAU,CACjBQ,KAAiDA,GAAKJ,EAAAA,IAA/CE,GAAAA,QAA0BF,EAAAA,GAElC,SAAAS,IAAAA;AACC,QAAMC,KAAeL,GAASM,MAC3BN,GAASM,IAAY,CAAA,IACrBN,GAAST,GAAQ,CAAA,GACdgB,KAAYP,GAAUE,EAASG,IAAcD,EAAAA;AAE/CC,IAAAA,OAAiBE,OACpBP,GAASM,MAAc,CAACC,IAAWP,GAAST,GAAQ,CAAA,CAAA,GACpDS,GAASG,IAAYK,SAAS,CAAE,CAAA;EAElC,CAAA,GAGDR,GAASG,MAAchB,IAAAA,CAElBA,GAAgBsB,MAAmB;AAAA,QAgC9BC,KAAT,SAAyBC,IAAGC,IAAGC,IAAAA;AAC9B,UAAA,CAAKb,GAASG,IAAAb,IAAqB,QAAA;AAGnC,UACMwB,KACLd,GAASG,IAAAb,IAAAC,GAA0BwB,OAFhB,SAAAC,IAAAA;AAAC,eAAA,CAAA,CAAMA,GAACb;MAAW,CAAA;AAOvC,UAHsBW,GAAWG,MAAM,SAAAD,IAAAA;AAAC,eAAA,CAAKA,GAACV;MAAW,CAAA,EAIxD,QAAA,CAAOY,MAAUA,GAAQC,KAAKC,MAAMT,IAAGC,IAAGC,EAAAA;AAM3C,UAAIQ,KAAerB,GAASG,IAAYmB,UAAUX;AAUlD,aATAG,GAAWS,QAAQ,SAAAC,IAAAA;AAClB,YAAIA,GAAQlB,KAAa;AACxB,cAAMD,KAAemB,GAAQjC,GAAQ,CAAA;AACrCiC,UAAAA,GAAQjC,KAAUiC,GAAQlB,KAC1BkB,GAAQlB,MAAAA,QACJD,OAAiBmB,GAAQjC,GAAQ,CAAA,MAAI8B,KAAAA;QAC1C;MACD,CAAA,GAEOH,MACJA,GAAQC,KAAKC,MAAMT,IAAGC,IAAGC,EAAAA,KACzBQ;IACJ;AA9DAlC,IAAAA,GAAgBsB,MAAAA;AAChB,QAAIS,KAAU/B,GAAiBsC,uBACzBC,KAAUvC,GAAiBwC;AAKjCxC,IAAAA,GAAiBwC,sBAAsB,SAAUhB,IAAGC,IAAGC,IAAAA;AACtD,UAAIO,KAAIQ,KAAS;AAChB,YAAIC,KAAMX;AAEVA,QAAAA,KAAAA,QACAR,GAAgBC,IAAGC,IAAGC,EAAAA,GACtBK,KAAUW;MACX;AAEIH,MAAAA,MAASA,GAAQP,KAAKC,MAAMT,IAAGC,IAAGC,EAAAA;IACvC,GA+CA1B,GAAiBsC,wBAAwBf;EAC1C;AAGD,SAAOV,GAASM,OAAeN,GAAST;AACzC;AAOO,SAASuC,GAAUC,IAAUC,IAAAA;AAEnC,MAAMC,KAAQnD,GAAamB,MAAgB,CAAA;AAAA,GACtChB,GAAOiD,OAAiBC,GAAYF,GAAK3C,KAAQ0C,EAAAA,MACrDC,GAAK1C,KAAUwC,IACfE,GAAMG,IAAeJ,IAErB7C,GAAgBG,IAAAJ,IAAyBO,KAAKwC,EAAAA;AAEhD;AAOgB,SAAAI,GAAgBN,IAAUC,IAAAA;AAEzC,MAAMC,KAAQnD,GAAamB,MAAgB,CAAA;AAAA,GACtChB,GAAOiD,OAAiBC,GAAYF,GAAK3C,KAAQ0C,EAAAA,MACrDC,GAAK1C,KAAUwC,IACfE,GAAMG,IAAeJ,IAErB7C,GAAgBD,IAAkBO,KAAKwC,EAAAA;AAEzC;AAGO,SAASK,GAAOC,IAAAA;AAEtB,SADAnD,KAAc,GACPoD,GAAQ,WAAA;AAAO,WAAA,EAAEC,SAASF,GAAAA;EAAc,GAAG,CAAA,CAAA;AACnD;AAQgB,SAAAG,GAAoBC,IAAKC,IAAcZ,IAAAA;AACtD5C,EAAAA,KAAc,GACdiD,GACC,WAAA;AACC,QAAkB,cAAA,OAAPM,IAAmB;AAC7B,UAAME,KAASF,GAAIC,GAAAA,CAAAA;AACnB,aAAa,WAAA;AACZD,QAAAA,GAAI,IAAA,GACAE,MAA2B,cAAA,OAAVA,MAAsBA,GAAAA;MAC5C;IACD;AAAWF,QAAAA,GAEV,QADAA,GAAIF,UAAUG,GAAAA,GACP,WAAA;AAAA,aAAOD,GAAIF,UAAU;IAAI;EAElC,GACQ,QAART,KAAeA,KAAOA,GAAKc,OAAOH,EAAAA,CAAAA;AAEpC;AAQgB,SAAAH,GAAQO,IAASf,IAAAA;AAEhC,MAAMC,KAAQnD,GAAamB,MAAgB,CAAA;AAO3C,SANIkC,GAAYF,GAAK3C,KAAQ0C,EAAAA,MAC5BC,GAAK1C,KAAUwD,GAAAA,GACfd,GAAK3C,MAAS0C,IACdC,GAAK/C,MAAY6D,KAGXd,GAAK1C;AACb;AAOO,SAASyD,GAAYjB,IAAUC,IAAAA;AAErC,SADA5C,KAAc,GACPoD,GAAQ,WAAA;AAAA,WAAMT;EAAQ,GAAEC,EAAAA;AAChC;AAKO,SAASiB,GAAWC,IAAAA;AAC1B,MAAMC,KAAWhE,GAAiB+D,QAAQA,GAAO/C,GAAAA,GAK3C8B,KAAQnD,GAAamB,MAAgB,CAAA;AAK3C,SADAgC,GAAKpB,IAAYqC,IACZC,MAEe,QAAhBlB,GAAK1C,OACR0C,GAAK1C,KAAAA,MACL4D,GAASC,IAAIjE,EAAAA,IAEPgE,GAAS7B,MAAM+B,SANAH,GAAO3D;AAO9B;AAMgB,SAAA+D,GAAcD,IAAOE,IAAAA;AAChCtE,EAAAA,GAAQqE,iBACXrE,GAAQqE,cACPC,KAAYA,GAAUF,EAAAA,IAAMG,EAAA;AAG/B;AAMO,SAASC,GAAiBC,IAAAA;AAEhC,MAAMzB,KAAQnD,GAAamB,MAAgB,EAAA,GACrC0D,KAAWjE,GAAAA;AAQjB,SAPAuC,GAAK1C,KAAUmE,IACVvE,GAAiByE,sBACrBzE,GAAiByE,oBAAoB,SAACC,IAAKC,IAAAA;AACtC7B,IAAAA,GAAK1C,MAAS0C,GAAK1C,GAAQsE,IAAKC,EAAAA,GACpCH,GAAS,CAAA,EAAGE,EAAAA;EACb,IAEM,CACNF,GAAS,CAAA,GACT,WAAA;AACCA,IAAAA,GAAS,CAAA,EAAA,MAAGI;EACb,CAAA;AAEF;AAGO,SAASC,KAAAA;AAEf,MAAM/B,KAAQnD,GAAamB,MAAgB,EAAA;AAC3C,MAAA,CAAKgC,GAAK1C,IAAS;AAIlB,aADI0E,KAAO9E,GAAgB+E,KACX,SAATD,MAAAA,CAAkBA,GAAIE,OAA2B,SAAjBF,GAAI1E,KAC1C0E,CAAAA,KAAOA,GAAI1E;AAGZ,QAAI6E,KAAOH,GAAIE,QAAWF,GAAIE,MAAS,CAAC,GAAG,CAAA;AAC3ClC,IAAAA,GAAK1C,KAAU,MAAM6E,GAAK,CAAA,IAAK,MAAMA,GAAK,CAAA;EAC3C;AAEA,SAAOnC,GAAK1C;AACb;AAKA,SAAS8E,KAAAA;AAER,WADIC,IACIA,KAAYC,GAAkBC,MAAAA,IACrC,KAAKF,GAASG,OAAgBH,GAAShF,IACvC,KAAA;AACCgF,IAAAA,GAAShF,IAAAJ,IAAyBqC,QAAQmD,EAAAA,GAC1CJ,GAAShF,IAAAJ,IAAyBqC,QAAQoD,EAAAA,GAC1CL,GAAShF,IAAAJ,MAA2B,CAAA;EAIrC,SAHS0F,IAAAA;AACRN,IAAAA,GAAShF,IAAAJ,MAA2B,CAAA,GACpCD,GAAO2C,IAAagD,IAAGN,GAASJ,GAAAA;EACjC;AAEF;AAcA,SAASW,GAAe9C,IAAAA;AACvB,MAOI+C,IAPEC,KAAO,WAAA;AACZC,iBAAaC,EAAAA,GACTC,MAASC,qBAAqBL,EAAAA,GAClCM,WAAWrD,EAAAA;EACZ,GACMkD,KAAUG,WAAWL,IAlcR,GAAA;AAqcfG,EAAAA,OACHJ,KAAMO,sBAAsBN,EAAAA;AAE9B;AAqBA,SAASL,GAAcY,IAAAA;AAGtB,MAAMC,KAAOpG,IACTqG,KAAUF,GAAInF;AACI,gBAAA,OAAXqF,OACVF,GAAInF,MAAAA,QACJqF,GAAAA,IAGDrG,KAAmBoG;AACpB;AAOA,SAASZ,GAAaW,IAAAA;AAGrB,MAAMC,KAAOpG;AACbmG,EAAAA,GAAInF,MAAYmF,GAAI/F,GAAAA,GACpBJ,KAAmBoG;AACpB;AAOA,SAASpD,GAAYsD,IAASC,IAAAA;AAC7B,SAAA,CACED,MACDA,GAAQjG,WAAWkG,GAAQlG,UAC3BkG,GAAQC,KAAK,SAACC,IAAK7G,IAAAA;AAAU,WAAA6G,OAAQH,GAAQ1G,EAAAA;EAAM,CAAA;AAErD;AAQA,SAASc,GAAe+F,IAAKC,IAAAA;AAC5B,SAAmB,cAAA,OAALA,KAAkBA,GAAED,EAAAA,IAAOC;AAC1C;IApiBI5F,IAGAd,IAGA2G,IAmBAC,IAhBA3G,IAGAmF,IAGEtF,IAEF+G,IACAC,IACAC,IACAC,IACAC,IACAC,IAkbAnB;;;;AAxcJ,IASI9F,KAAc;AATlB,IAYImF,KAAoB,CAAA;AAZxB,IAeMtF,KAAuDqH;AAf7D,IAiBIN,KAAgB/G,GAAOsH;AAjB3B,IAkBIN,KAAkBhH,GAAOuH;AAlB7B,IAmBIN,KAAejH,GAAQwH;AAnB3B,IAoBIN,KAAYlH,GAAOkB;AApBvB,IAqBIiG,KAAmBnH,GAAQyH;AArB/B,IAsBIL,KAAUpH,GAAOM;AAMrBN,IAAAA,GAAOsH,MAAS,SAAAI,IAAAA;AACfxH,MAAAA,KAAmB,MACf6G,MAAeA,GAAcW,EAAAA;IAClC,GAEA1H,GAAOM,KAAS,SAACoH,IAAOC,IAAAA;AACnBD,MAAAA,MAASC,GAASC,OAAcD,GAASC,IAAA1C,QAC5CwC,GAAKxC,MAASyC,GAASC,IAAA1C,MAGpBkC,MAASA,GAAQM,IAAOC,EAAAA;IAC7B,GAGA3H,GAAOuH,MAAW,SAAAG,IAAAA;AACbV,MAAAA,MAAiBA,GAAgBU,EAAAA,GAGrC1G,KAAe;AAEf,UAAMZ,MAHNF,KAAmBwH,GAAKxG,KAGMb;AAC1BD,MAAAA,OACCyG,OAAsB3G,MACzBE,GAAKH,MAAmB,CAAA,GACxBC,GAAgBD,MAAoB,CAAA,GACpCG,GAAKE,GAAOgC,QAAQ,SAAAC,IAAAA;AACfA,QAAAA,GAAQlB,QACXkB,GAAQjC,KAAUiC,GAAQlB,MAE3BkB,GAASY,IAAeZ,GAAQlB,MAAAA;MACjC,CAAA,MAEAjB,GAAKH,IAAiBqC,QAAQmD,EAAAA,GAC9BrF,GAAKH,IAAiBqC,QAAQoD,EAAAA,GAC9BtF,GAAKH,MAAmB,CAAA,GACxBe,KAAe,KAGjB6F,KAAoB3G;IACrB,GAGAF,GAAQwH,SAAS,SAAAE,IAAAA;AACZT,MAAAA,MAAcA,GAAaS,EAAAA;AAE/B,UAAM9F,KAAI8F,GAAKxG;AACXU,MAAAA,MAAKA,GAACvB,QACLuB,GAACvB,IAAAJ,IAAyBM,WAgaR,MAha2B+E,GAAkB9E,KAAKoB,EAAAA,KAga7CkF,OAAY9G,GAAQoG,2BAC/CU,KAAU9G,GAAQoG,0BACNR,IAAgBR,EAAAA,IAja5BxD,GAACvB,IAAAC,GAAegC,QAAQ,SAAAC,IAAAA;AACnBA,QAAAA,GAASY,MACZZ,GAAQlC,MAASkC,GAASY,IAE3BZ,GAASY,IAAAA;MACV,CAAA,IAED0D,KAAoB3G,KAAmB;IACxC,GAIAF,GAAOkB,MAAW,SAACwG,IAAOG,IAAAA;AACzBA,MAAAA,GAAYnB,KAAK,SAAArB,IAAAA;AAChB,YAAA;AACCA,UAAAA,GAASpF,IAAkBqC,QAAQmD,EAAAA,GACnCJ,GAASpF,MAAoBoF,GAASpF,IAAkB6B,OAAO,SAAA2C,IAAAA;AAAE,mBAAA,CAChEA,GAAEnE,MAAUoF,GAAajB,EAAAA;UAAU,CAAA;QAQrC,SANSkB,IAAAA;AACRkC,UAAAA,GAAYnB,KAAK,SAAA9E,IAAAA;AACZA,YAAAA,GAAC3B,QAAmB2B,GAAC3B,MAAoB,CAAA;UAC9C,CAAA,GACA4H,KAAc,CAAA,GACd7H,GAAO2C,IAAagD,IAAGN,GAASJ,GAAAA;QACjC;MACD,CAAA,GAEIiC,MAAWA,GAAUQ,IAAOG,EAAAA;IACjC,GAGA7H,GAAQyH,UAAU,SAAAC,IAAAA;AACbP,MAAAA,MAAkBA,GAAiBO,EAAAA;AAEvC,UAEKI,IAFClG,KAAI8F,GAAKxG;AACXU,MAAAA,MAAKA,GAACvB,QAETuB,GAACvB,IAAAC,GAAegC,QAAQ,SAAAX,IAAAA;AACvB,YAAA;AACC8D,UAAAA,GAAc9D,EAAAA;QAGf,SAFSgE,IAAAA;AACRmC,UAAAA,KAAanC;QACd;MACD,CAAA,GACA/D,GAACvB,MAAAA,QACGyH,MAAY9H,GAAO2C,IAAamF,IAAYlG,GAACqD,GAAAA;IAEnD;AA4UA,IAAIgB,KAA0C,cAAA,OAAzBG;;;", "names": ["assign", "obj", "props", "i", "removeNode", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "type", "children", "key", "ref", "normalizedProps", "arguments", "length", "slice", "call", "defaultProps", "undefined", "createVNode", "original", "vnode", "__k", "__", "__b", "__e", "__c", "constructor", "__v", "vnodeId", "__i", "__u", "options", "createRef", "current", "Fragment", "BaseComponent", "context", "this", "getDomSibling", "childIndex", "sibling", "updateParentDomPointers", "child", "base", "enqueueRender", "c", "__d", "rerenderQueue", "push", "process", "__r", "prevDebounce", "debounceRendering", "defer", "component", "newVNode", "oldVNode", "oldDom", "commitQueue", "refQueue", "l", "sort", "depthSort", "shift", "__P", "diff", "__n", "namespaceURI", "commitRoot", "diff<PERSON><PERSON><PERSON><PERSON>", "parentDom", "renderResult", "newParentVNode", "oldParentVNode", "globalContext", "namespace", "excessDomChildren", "isHydrating", "childVNode", "newDom", "firstChildDom", "result", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EMPTY_ARR", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructNewChildrenArray", "EMPTY_OBJ", "applyRef", "insert", "nextS<PERSON>ling", "skewedIndex", "matchingIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remainingOldChildren", "skew", "Array", "String", "isArray", "findMatchingIndex", "unmount", "parentVNode", "contains", "insertBefore", "nodeType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "out", "some", "x", "y", "setStyle", "style", "value", "setProperty", "IS_NON_DIMENSIONAL", "test", "dom", "name", "oldValue", "useCapture", "o", "cssText", "replace", "CAPTURE_REGEX", "toLowerCase", "_attached", "eventClock", "addEventListener", "eventProxyCapture", "eventProxy", "removeEventListener", "e", "removeAttribute", "setAttribute", "createEventProxy", "<PERSON><PERSON><PERSON><PERSON>", "_dispatched", "event", "tmp", "isNew", "oldProps", "oldState", "snapshot", "clearProcessingException", "newProps", "isClassComponent", "provider", "componentContext", "renderHook", "count", "newType", "outer", "prototype", "render", "contextType", "__E", "doR<PERSON>", "sub", "state", "__h", "_sb", "__s", "getDerivedStateFromProps", "componentWillMount", "componentDidMount", "componentWillReceiveProps", "shouldComponentUpdate", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "cloneNode", "then", "MODE_HYDRATE", "indexOf", "diffElementNodes", "diffed", "root", "cb", "map", "newHtml", "oldHtml", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inputValue", "checked", "localName", "document", "createTextNode", "createElementNS", "is", "__m", "data", "childNodes", "attributes", "__html", "innerHTML", "content", "hasRefUnmount", "<PERSON><PERSON><PERSON><PERSON>", "r", "componentWillUnmount", "replaceNode", "documentElement", "<PERSON><PERSON><PERSON><PERSON>", "hydrate", "cloneElement", "createContext", "defaultValue", "Context", "subs", "ctx", "Set", "_props", "for<PERSON>ach", "add", "old", "delete", "Provider", "__l", "Consumer", "contextValue", "isValidElement", "error", "errorInfo", "ctor", "handled", "getDerivedStateFromError", "setState", "componentDidCatch", "update", "callback", "s", "forceUpdate", "Promise", "bind", "resolve", "setTimeout", "a", "b", "getHookState", "index", "type", "options", "__h", "currentComponent", "currentHook", "hooks", "__H", "__", "length", "push", "useState", "initialState", "useReducer", "invokeOrReturn", "reducer", "init", "hookState", "currentIndex", "_reducer", "__c", "action", "currentValue", "__N", "nextValue", "setState", "__f", "updateHookState", "p", "s", "c", "stateHooks", "filter", "x", "every", "prevScu", "call", "this", "shouldUpdate", "props", "for<PERSON>ach", "hookItem", "shouldComponentUpdate", "prevCWU", "componentWillUpdate", "__e", "tmp", "useEffect", "callback", "args", "state", "__s", "args<PERSON><PERSON><PERSON>", "_pendingArgs", "useLayoutEffect", "useRef", "initialValue", "useMemo", "current", "useImperativeHandle", "ref", "createHandle", "result", "concat", "factory", "useCallback", "useContext", "context", "provider", "sub", "value", "useDebugValue", "formatter", "n", "useErrorBoundary", "cb", "errState", "componentDidCatch", "err", "errorInfo", "undefined", "useId", "root", "__v", "__m", "mask", "flushAfterPaintEffects", "component", "afterPaintEffects", "shift", "__P", "invokeCleanup", "invokeEffect", "e", "afterNextFrame", "raf", "done", "clearTimeout", "timeout", "HAS_RAF", "cancelAnimationFrame", "setTimeout", "requestAnimationFrame", "hook", "comp", "cleanup", "oldArgs", "newArgs", "some", "arg", "f", "previousComponent", "prevRaf", "oldBeforeDiff", "oldBeforeRender", "oldAfterDiff", "old<PERSON><PERSON><PERSON>", "oldBeforeUnmount", "oldRoot", "_options", "__b", "__r", "diffed", "unmount", "vnode", "parentDom", "__k", "commitQueue", "hasErrored"]}