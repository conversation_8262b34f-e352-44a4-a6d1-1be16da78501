
import { Toaster as Sonner } from "sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { BrowserRouter, Routes, Route } from "react-router-dom";
// import { useIsMobile } from "@/hooks/use-mobile"; // No longer needed
import { ThemeProvider } from "@/components/ui/theme-provider";
// import { Loading } from "@/components/ui/loading"; // No longer needed for PageLoading

import { XellarProvider } from './providers/XellarProvider';
import { AppStateProvider } from './state/AppStateContext';
import { TransferStateProvider } from './state/TransferStateContext';
import { DataProvider } from './providers/DataProvider';
import TransactionDetector from './components/TransactionDetector';

// Import MaintenancePage
import MaintenancePage from "./pages/MaintenancePage";

// No longer needed as all pages are replaced by MaintenancePage
// import WalletCheck from './components/WalletCheck';
// import Layout from "./components/Layout";
// import DesktopLayout from "./components/DesktopLayout";

// Lazy loading not needed for a single maintenance page
// const Index = lazy(() => import("./pages/Index"));
// const Home = lazy(() => import("./pages/Home"));
// const Transfer = lazy(() => import("./pages/Transfer"));
// const Streams = lazy(() => import("./pages/OptimizedStreams"));
// const AutoRefreshStreams = lazy(() => import("./pages/AutoRefreshStreams"));
// const Pools = lazy(() => import("./pages/Pools"));
// const StraptDrop = lazy(() => import("./pages/EnhancedStraptDrop"));
// const StraptDropClaim = lazy(() => import("./pages/EnhancedStraptDropClaim"));
// const MyDrops = lazy(() => import("./pages/OptimizedMyDrops"));
// const Profile = lazy(() => import("./pages/OptimizedProfile"));
// const Claims = lazy(() => import("./pages/Claims"));
// const Savings = lazy(() => import("./pages/Savings"));
// const ComingSoon = lazy(() => import("./pages/ComingSoon"));
// const NotFound = lazy(() => import("./pages/NotFound"));


const App = () => {
  // const isMobile = useIsMobile(); // No longer needed

  // PageLoading fallback no longer needed if not using Suspense heavily
  // const PageLoading = () => (
  //   <div className="flex items-center justify-center min-h-[60vh]">
  //     <Loading size="lg" text="Loading page..." />
  //   </div>
  // );

  return (
    <ThemeProvider defaultTheme="dark" storageKey="strapt-theme">
      <XellarProvider>
        <AppStateProvider>
          <TransferStateProvider>
            <TooltipProvider>
              <Sonner position="top-right" />
              <BrowserRouter>
                <DataProvider>
                <TransactionDetector>
                <Routes>
                  {/* All paths now lead to MaintenancePage */}
                  <Route path="*" element={<MaintenancePage />} />
                </Routes>
                </TransactionDetector>
                </DataProvider>
              </BrowserRouter>
              </TooltipProvider>
          </TransferStateProvider>
        </AppStateProvider>
      </XellarProvider>
    </ThemeProvider>
  );
};

export default App;
