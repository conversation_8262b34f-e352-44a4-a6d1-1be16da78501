import {
  BaseError,
  Hydrate,
  WagmiContext,
  WagmiProvider,
  WagmiProviderNotFoundError,
  cookieStorage,
  cookieToInitialState,
  createConfig,
  createConnector,
  createStorage,
  deserialize,
  fallback,
  injected,
  mock,
  noopStorage,
  normalizeChainId,
  parseCookie,
  serialize,
  unstable_connector,
  useAccount,
  useAccountEffect,
  useBalance,
  useBlock,
  useBlockNumber,
  useBlockTransactionCount,
  useBytecode,
  useCall,
  useCallsStatus,
  useCapabilities,
  useChainId,
  useChains,
  useClient,
  useConfig,
  useConnect,
  useConnections,
  useConnectorClient,
  useConnectors,
  useDeployContract,
  useDisconnect,
  useEnsAddress,
  useEnsAvatar,
  useEnsName,
  useEnsResolver,
  useEnsText,
  useEstimateFeesPerGas,
  useEstimateGas,
  useEstimateMaxPriorityFeePerGas,
  useFeeHistory,
  useGasPrice,
  useInfiniteReadContracts,
  usePrepareTransactionRequest,
  useProof,
  usePublicClient,
  useReadContract,
  useReadContracts,
  useReconnect,
  useSendCalls,
  useSendTransaction,
  useShowCallsStatus,
  useSignMessage,
  useSignTypedData,
  useSimulateContract,
  useStorageAt,
  useSwitchAccount,
  useSwitchChain,
  useToken,
  useTransaction,
  useTransactionConfirmations,
  useTransactionCount,
  useTransactionReceipt,
  useVerifyMessage,
  useVerifyTypedData,
  useWaitForCallsStatus,
  useWaitForTransactionReceipt,
  useWalletClient,
  useWatchAsset,
  useWatchBlockNumber,
  useWatchBlocks,
  useWatchContractEvent,
  useWatchPendingTransactions,
  useWriteContract,
  version
} from "./chunk-PGPRL42D.js";
import "./chunk-YOLEXXBR.js";
import "./chunk-M6JLZ5G3.js";
import {
  ChainNotConfiguredError,
  ConnectorAccountNotFoundError,
  ConnectorAlreadyConnectedError,
  ConnectorChainMismatchError,
  ConnectorNotFoundError,
  ConnectorUnavailableReconnectingError,
  ProviderNotFoundError,
  SwitchChainNotSupportedError,
  deepEqual
} from "./chunk-I4O4IQL7.js";
import {
  custom,
  http,
  webSocket
} from "./chunk-ZFCONEF5.js";
import "./chunk-THK54BD4.js";
import "./chunk-EJHSWTG7.js";
import "./chunk-QGGPV2IE.js";
import "./chunk-6WCWOOPY.js";
import "./chunk-PLGNPOJG.js";
import "./chunk-27263VST.js";
import "./chunk-H56NVK4B.js";
import "./chunk-6VBNRQM5.js";
import "./chunk-P4JBPKOL.js";
import "./chunk-AGWB3WMV.js";
import "./chunk-XYQR56ZF.js";
import "./chunk-64NT3AJW.js";
export {
  BaseError,
  ChainNotConfiguredError,
  ConnectorAccountNotFoundError,
  ConnectorAlreadyConnectedError,
  ConnectorChainMismatchError,
  ConnectorNotFoundError,
  ConnectorUnavailableReconnectingError,
  WagmiContext as Context,
  Hydrate,
  ProviderNotFoundError,
  SwitchChainNotSupportedError,
  WagmiProvider as WagmiConfig,
  WagmiContext,
  WagmiProvider,
  WagmiProviderNotFoundError,
  cookieStorage,
  cookieToInitialState,
  createConfig,
  createConnector,
  createStorage,
  custom,
  deepEqual,
  deserialize,
  fallback,
  http,
  injected,
  mock,
  noopStorage,
  normalizeChainId,
  parseCookie,
  serialize,
  unstable_connector,
  useAccount,
  useAccountEffect,
  useBalance,
  useBlock,
  useBlockNumber,
  useBlockTransactionCount,
  useBytecode,
  useCall,
  useCallsStatus,
  useCapabilities,
  useChainId,
  useChains,
  useClient,
  useConfig,
  useConnect,
  useConnections,
  useConnectorClient,
  useConnectors,
  useInfiniteReadContracts as useContractInfiniteReads,
  useReadContract as useContractRead,
  useReadContracts as useContractReads,
  useWriteContract as useContractWrite,
  useDeployContract,
  useDisconnect,
  useEnsAddress,
  useEnsAvatar,
  useEnsName,
  useEnsResolver,
  useEnsText,
  useEstimateFeesPerGas,
  useEstimateGas,
  useEstimateMaxPriorityFeePerGas,
  useEstimateFeesPerGas as useFeeData,
  useFeeHistory,
  useGasPrice,
  useInfiniteReadContracts,
  usePrepareTransactionRequest,
  useProof,
  usePublicClient,
  useReadContract,
  useReadContracts,
  useReconnect,
  useSendCalls,
  useSendTransaction,
  useShowCallsStatus,
  useSignMessage,
  useSignTypedData,
  useSimulateContract,
  useStorageAt,
  useSwitchAccount,
  useSwitchChain,
  useToken,
  useTransaction,
  useTransactionConfirmations,
  useTransactionCount,
  useTransactionReceipt,
  useVerifyMessage,
  useVerifyTypedData,
  useWaitForCallsStatus,
  useWaitForTransactionReceipt,
  useWalletClient,
  useWatchAsset,
  useWatchBlockNumber,
  useWatchBlocks,
  useWatchContractEvent,
  useWatchPendingTransactions,
  useWriteContract,
  version,
  webSocket
};
//# sourceMappingURL=wagmi.js.map
