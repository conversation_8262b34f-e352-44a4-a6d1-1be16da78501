{"version": 3, "sources": ["../../@safe-global/safe-apps-sdk/src/version.ts", "../../@safe-global/safe-apps-sdk/src/communication/utils.ts", "../../@safe-global/safe-apps-sdk/src/communication/messageFormatter.ts", "../../@safe-global/safe-apps-sdk/src/communication/methods.ts", "../../@safe-global/safe-apps-sdk/src/communication/index.ts", "../../@safe-global/safe-apps-sdk/src/types/sdk.ts", "../../@safe-global/safe-apps-sdk/src/types/gateway.ts", "../../@safe-global/safe-apps-sdk/src/txs/index.ts", "../../@safe-global/safe-apps-sdk/src/eth/constants.ts", "../../@safe-global/safe-apps-sdk/src/eth/index.ts", "../../@safe-global/safe-apps-sdk/src/safe/signatures.ts", "../../@safe-global/safe-apps-sdk/src/types/permissions.ts", "../../@safe-global/safe-apps-sdk/src/wallet/index.ts", "../../@safe-global/safe-apps-sdk/src/decorators/requirePermissions.ts", "../../@safe-global/safe-apps-sdk/src/safe/index.ts", "../../@safe-global/safe-apps-sdk/src/sdk.ts", "../../@safe-global/safe-apps-sdk/src/index.ts"], "sourcesContent": ["export const getSDKVersion = () => '9.1.0';\n", "// i.e. 0-255 -> '00'-'ff'\nconst dec2hex = (dec: number): string => dec.toString(16).padStart(2, '0');\n\nconst generateId = (len: number): string => {\n  const arr = new Uint8Array((len || 40) / 2);\n  window.crypto.getRandomValues(arr);\n  return Array.from(arr, dec2hex).join('');\n};\n\nconst generateRequestId = (): string => {\n  if (typeof window !== 'undefined') {\n    return generateId(10);\n  }\n\n  return new Date().getTime().toString(36);\n};\n\nexport { generateRequestId };\n", "import { ErrorResponse, SDKRequestData, RequestId, SuccessResponse, MethodToResponse } from '../types/index.js';\nimport { getSDKVersion } from '../version.js';\nimport { Methods } from './methods.js';\nimport { generateRequestId } from './utils.js';\n\nclass MessageFormatter {\n  static makeRequest = <M extends Methods = Methods, P = unknown>(method: M, params: P): SDKRequestData<M, P> => {\n    const id = generateRequestId();\n\n    return {\n      id,\n      method,\n      params,\n      env: {\n        sdkVersion: getSDKVersion(),\n      },\n    };\n  };\n\n  static makeResponse = (id: RequestId, data: MethodToResponse[Methods], version: string): SuccessResponse => ({\n    id,\n    success: true,\n    version,\n    data,\n  });\n\n  static makeErrorResponse = (id: RequestId, error: string, version: string): ErrorResponse => ({\n    id,\n    success: false,\n    error,\n    version,\n  });\n}\n\nexport { MessageFormatter };\n", "export enum Methods {\n  sendTransactions = 'sendTransactions',\n  rpcCall = 'rpcCall',\n  getChainInfo = 'getChainInfo',\n  getSafeInfo = 'getSafeInfo',\n  getTxBySafeTxHash = 'getTxBySafeTxHash',\n  getSafeBalances = 'getSafeBalances',\n  signMessage = 'signMessage',\n  signTypedMessage = 'signTypedMessage',\n  getEnvironmentInfo = 'getEnvironmentInfo',\n  getOffChainSignature = 'getOffChainSignature',\n  requestAddressBook = 'requestAddressBook',\n  wallet_getPermissions = 'wallet_getPermissions',\n  wallet_requestPermissions = 'wallet_requestPermissions',\n}\n\nexport enum RestrictedMethods {\n  requestAddressBook = 'requestAddressBook',\n}\n", "import { MessageFormatter } from './messageFormatter.js';\nimport { Methods } from './methods.js';\nimport { InterfaceMessageEvent, Communicator, Response, SuccessResponse } from '../types/index.js';\n\n// eslint-disable-next-line\ntype Callback = (response: any) => void;\n\nclass PostMessageCommunicator implements Communicator {\n  private readonly allowedOrigins: RegExp[] | null = null;\n  private callbacks = new Map<string, Callback>();\n  private debugMode = false;\n  private isServer = typeof window === 'undefined';\n\n  constructor(allowedOrigins: RegExp[] | null = null, debugMode = false) {\n    this.allowedOrigins = allowedOrigins;\n    this.debugMode = debugMode;\n\n    if (!this.isServer) {\n      window.addEventListener('message', this.onParentMessage);\n    }\n  }\n\n  private isValidMessage = ({ origin, data, source }: InterfaceMessageEvent): boolean => {\n    const emptyOrMalformed = !data;\n    const sentFromParentEl = !this.isServer && source === window.parent;\n    const majorVersionNumber = typeof data.version !== 'undefined' && parseInt(data.version.split('.')[0]);\n    const allowedSDKVersion = typeof majorVersionNumber === 'number' && majorVersionNumber >= 1;\n    let validOrigin = true;\n    if (Array.isArray(this.allowedOrigins)) {\n      validOrigin = this.allowedOrigins.find((regExp) => regExp.test(origin)) !== undefined;\n    }\n\n    return !emptyOrMalformed && sentFromParentEl && allowedSDKVersion && validOrigin;\n  };\n\n  private logIncomingMessage = (msg: InterfaceMessageEvent): void => {\n    console.info(`Safe Apps SDK v1: A message was received from origin ${msg.origin}. `, msg.data);\n  };\n\n  private onParentMessage = (msg: InterfaceMessageEvent): void => {\n    if (this.isValidMessage(msg)) {\n      this.debugMode && this.logIncomingMessage(msg);\n      this.handleIncomingMessage(msg.data);\n    }\n  };\n\n  private handleIncomingMessage = (payload: InterfaceMessageEvent['data']): void => {\n    const { id } = payload;\n\n    const cb = this.callbacks.get(id);\n    if (cb) {\n      cb(payload);\n\n      this.callbacks.delete(id);\n    }\n  };\n\n  public send = <M extends Methods, P, R>(method: M, params: P): Promise<SuccessResponse<R>> => {\n    const request = MessageFormatter.makeRequest(method, params);\n\n    if (this.isServer) {\n      throw new Error(\"Window doesn't exist\");\n    }\n\n    window.parent.postMessage(request, '*');\n    return new Promise((resolve, reject) => {\n      this.callbacks.set(request.id, (response: Response<R>) => {\n        if (!response.success) {\n          reject(new Error(response.error));\n          return;\n        }\n\n        resolve(response);\n      });\n    });\n  };\n}\n\nexport default PostMessageCommunicator;\nexport * from './methods.js';\n", "import { ChainInfo as _ChainInfo } from '@safe-global/safe-gateway-typescript-sdk';\n\nexport type ChainInfo = Pick<\n  _ChainInfo,\n  'chainName' | 'chainId' | 'shortName' | 'nativeCurrency' | 'blockExplorerUriTemplate'\n>;\n\nexport { NativeCurrency } from '@safe-global/safe-gateway-typescript-sdk';\n\nexport type BaseTransaction = {\n  to: string;\n  value: string;\n  data: string;\n};\n\nexport type GetTxBySafeTxHashParams = {\n  safeTxHash: string;\n};\n\nexport interface SendTransactionRequestParams {\n  safeTxGas?: number;\n}\n\nexport interface SendTransactionsParams {\n  txs: BaseTransaction[];\n  params?: SendTransactionRequestParams;\n}\n\nexport type GetBalanceParams = { currency?: string };\n\nexport type SignMessageParams = {\n  message: string;\n};\n\nexport interface TypedDataDomain {\n  name?: string;\n  version?: string;\n  chainId?: string | number | bigint | { toNumber: () => number };\n  verifyingContract?: string;\n  salt?: string;\n}\n\nexport interface TypedDataTypes {\n  name: string;\n  type: string;\n}\n\nexport type TypedMessageTypes = { [key: string]: TypedDataTypes[] };\n\nexport type EIP712TypedData = {\n  domain: TypedDataDomain;\n  types: TypedMessageTypes;\n  message: Record<string, any>;\n  primaryType?: string;\n};\n\nexport type SignTypedMessageParams = {\n  typedData: EIP712TypedData;\n};\n\nexport type SendTransactionsResponse = {\n  safeTxHash: string;\n};\n\nexport type OffChainSignMessageResponse = {\n  messageHash: string;\n};\n\nexport type SignMessageResponse = SendTransactionsResponse | OffChainSignMessageResponse;\n\nexport type SafeInfo = {\n  safeAddress: string;\n  chainId: number;\n  threshold: number;\n  owners: string[];\n  isReadOnly: boolean;\n};\n\nexport type SafeInfoExtended = SafeInfo & {\n  nonce: number;\n  implementation: string;\n  modules: string[] | null;\n  fallbackHandler: string | null;\n  guard: string | null;\n  version: string | null;\n};\n\nexport type EnvironmentInfo = {\n  origin: string;\n};\n\nexport type PostMessageOptions = {\n  transfer?: any[];\n};\n\nexport type AddressBookItem = {\n  address: string;\n  chainId: string;\n  name: string;\n};\n\nexport const isObjectEIP712TypedData = (obj?: unknown): obj is EIP712TypedData => {\n  return typeof obj === 'object' && obj != null && 'domain' in obj && 'types' in obj && 'message' in obj;\n};\n", "import { SafeBalanceResponse, TransactionDetails, TokenInfo } from '@safe-global/safe-gateway-typescript-sdk';\n\nexport {\n  AddOwner,\n  ChangeImplementation,\n  ChangeThreshold,\n  Creation,\n  Custom,\n  DataDecoded,\n  DetailedExecutionInfo,\n  DisableModule,\n  EnableModule,\n  Erc20Transfer,\n  Erc721Transfer,\n  InternalTransaction,\n  ModuleExecutionDetails,\n  MultiSend,\n  MultisigConfirmation,\n  MultisigExecutionDetails,\n  NativeCoinTransfer,\n  Operation,\n  Parameter,\n  RemoveOwner,\n  SafeAppInfo,\n  SetFallbackHandler,\n  SettingsChange,\n  SettingsInfo,\n  SwapOwner,\n  TokenInfo,\n  TokenType,\n  TransactionData,\n  TransactionInfo,\n  TransactionStatus,\n  Transfer,\n  TransferDirection,\n  TransferInfo,\n} from '@safe-global/safe-gateway-typescript-sdk';\n\nexport type GatewayTransactionDetails = TransactionDetails;\n\nexport type TokenBalance = {\n  tokenInfo: TokenInfo;\n  balance: string;\n  fiatBalance: string;\n  fiatConversion: string;\n};\n\nexport type SafeBalances = SafeBalanceResponse;\n", "import { Methods } from '../communication/methods.js';\nimport {\n  GatewayTransactionDetails,\n  SignMessageParams,\n  SendTransactionsParams,\n  GetTxBySafeTxHashParams,\n  Communicator,\n  SendTransactionsResponse,\n  SignTypedMessageParams,\n  EIP712TypedData,\n  isObjectEIP712TypedData,\n  SignMessageResponse,\n} from '../types/index.js';\n\nclass TXs {\n  private readonly communicator: Communicator;\n\n  constructor(communicator: Communicator) {\n    this.communicator = communicator;\n  }\n\n  async getBySafeTxHash(safeTxHash: string): Promise<GatewayTransactionDetails> {\n    if (!safeTxHash) {\n      throw new Error('Invalid safeTxHash');\n    }\n\n    const response = await this.communicator.send<\n      Methods.getTxBySafeTxHash,\n      GetTxBySafeTxHashParams,\n      GatewayTransactionDetails\n    >(Methods.getTxBySafeTxHash, { safeTxHash });\n\n    return response.data;\n  }\n\n  async signMessage(message: string): Promise<SignMessageResponse> {\n    const messagePayload = {\n      message,\n    };\n\n    const response = await this.communicator.send<Methods.signMessage, SignMessageParams, SignMessageResponse>(\n      Methods.signMessage,\n      messagePayload,\n    );\n\n    return response.data;\n  }\n\n  async signTypedMessage(typedData: EIP712TypedData): Promise<SignMessageResponse> {\n    if (!isObjectEIP712TypedData(typedData)) {\n      throw new Error('Invalid typed data');\n    }\n\n    const response = await this.communicator.send<\n      Methods.signTypedMessage,\n      SignTypedMessageParams,\n      SignMessageResponse\n    >(Methods.signTypedMessage, { typedData });\n\n    return response.data;\n  }\n\n  async send({ txs, params }: SendTransactionsParams): Promise<SendTransactionsResponse> {\n    if (!txs || !txs.length) {\n      throw new Error('No transactions were passed');\n    }\n\n    const messagePayload = {\n      txs,\n      params,\n    };\n\n    const response = await this.communicator.send<\n      Methods.sendTransactions,\n      SendTransactionsParams,\n      SendTransactionsResponse\n    >(Methods.sendTransactions, messagePayload);\n\n    return response.data;\n  }\n}\n\nexport { TXs };\n", "export const RPC_CALLS = {\n  eth_call: 'eth_call',\n  eth_gasPrice: 'eth_gasPrice',\n  eth_getLogs: 'eth_getLogs',\n  eth_getBalance: 'eth_getBalance',\n  eth_getCode: 'eth_getCode',\n  eth_getBlockByHash: 'eth_getBlockByHash',\n  eth_getBlockByNumber: 'eth_getBlockByNumber',\n  eth_getStorageAt: 'eth_getStorageAt',\n  eth_getTransactionByHash: 'eth_getTransactionByHash',\n  eth_getTransactionReceipt: 'eth_getTransactionReceipt',\n  eth_getTransactionCount: 'eth_getTransactionCount',\n  eth_estimateGas: 'eth_estimateGas',\n  safe_setSettings: 'safe_setSettings',\n} as const;\n", "import { RPC_CALLS } from '../eth/constants.js';\nimport {\n  BlockNumberArg,\n  RpcCallNames,\n  Communicator,\n  Log,\n  BlockTransactionString,\n  BlockTransactionObject,\n  Web3TransactionObject,\n  RPCPayload,\n  TransactionConfig,\n  Web3TransactionReceiptObject,\n  PastLogsOptions,\n  SafeSettings,\n} from '../types/index.js';\nimport { Methods } from '../communication/methods.js';\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype Formatter = (arg: any) => any;\n\nconst inputFormatters: Record<string, Formatter> = {\n  defaultBlockParam: (arg = 'latest') => arg,\n  returnFullTxObjectParam: (arg = false): boolean => arg,\n  blockNumberToHex: (arg: BlockNumberArg): string =>\n    Number.isInteger(arg) ? `0x${arg.toString(16)}` : (arg as string),\n};\n\ntype BuildRequestArgs = {\n  call: RpcCallNames;\n  formatters?: (Formatter | null)[];\n};\n\nclass Eth {\n  public call;\n  public getBalance;\n  public getCode;\n  public getStorageAt;\n  public getPastLogs;\n  public getBlockByHash;\n  public getBlockByNumber;\n  public getTransactionByHash;\n  public getTransactionReceipt;\n  public getTransactionCount;\n  public getGasPrice;\n  public getEstimateGas;\n  public setSafeSettings;\n\n  private readonly communicator: Communicator;\n\n  constructor(communicator: Communicator) {\n    this.communicator = communicator;\n    this.call = this.buildRequest<[TransactionConfig, string?], string>({\n      call: RPC_CALLS.eth_call,\n      formatters: [null, inputFormatters.defaultBlockParam],\n    });\n    this.getBalance = this.buildRequest<[string, string?], string>({\n      call: RPC_CALLS.eth_getBalance,\n      formatters: [null, inputFormatters.defaultBlockParam],\n    });\n    this.getCode = this.buildRequest<[string, string?], string>({\n      call: RPC_CALLS.eth_getCode,\n      formatters: [null, inputFormatters.defaultBlockParam],\n    });\n    this.getStorageAt = this.buildRequest<[string, number, string?], string>({\n      call: RPC_CALLS.eth_getStorageAt,\n      formatters: [null, inputFormatters.blockNumberToHex, inputFormatters.defaultBlockParam],\n    });\n    this.getPastLogs = this.buildRequest<[PastLogsOptions], Log[]>({\n      call: RPC_CALLS.eth_getLogs,\n    });\n    this.getBlockByHash = this.buildRequest<[string, boolean?], BlockTransactionString | BlockTransactionObject>({\n      call: RPC_CALLS.eth_getBlockByHash,\n      formatters: [null, inputFormatters.returnFullTxObjectParam],\n    });\n    this.getBlockByNumber = this.buildRequest<\n      [BlockNumberArg, boolean?],\n      BlockTransactionString | BlockTransactionObject\n    >({\n      call: RPC_CALLS.eth_getBlockByNumber,\n      formatters: [inputFormatters.blockNumberToHex, inputFormatters.returnFullTxObjectParam],\n    });\n    this.getTransactionByHash = this.buildRequest<[string], Web3TransactionObject>({\n      call: RPC_CALLS.eth_getTransactionByHash,\n    });\n    this.getTransactionReceipt = this.buildRequest<[string], Web3TransactionReceiptObject>({\n      call: RPC_CALLS.eth_getTransactionReceipt,\n    });\n    this.getTransactionCount = this.buildRequest<[string, string?], string>({\n      call: RPC_CALLS.eth_getTransactionCount,\n      formatters: [null, inputFormatters.defaultBlockParam],\n    });\n    this.getGasPrice = this.buildRequest<never[], string>({\n      call: RPC_CALLS.eth_gasPrice,\n    });\n    this.getEstimateGas = (transaction: TransactionConfig): Promise<number> =>\n      this.buildRequest<[TransactionConfig], number>({\n        call: RPC_CALLS.eth_estimateGas,\n      })([transaction]);\n    this.setSafeSettings = this.buildRequest<[SafeSettings], SafeSettings>({\n      call: RPC_CALLS.safe_setSettings,\n    });\n  }\n\n  private buildRequest<P = never[], R = unknown>(args: BuildRequestArgs) {\n    const { call, formatters } = args;\n\n    return async (params?: P): Promise<R> => {\n      if (formatters && Array.isArray(params)) {\n        formatters.forEach((formatter: ((...args: unknown[]) => unknown) | null, i) => {\n          if (formatter) {\n            params[i] = formatter(params[i]);\n          }\n        });\n      }\n\n      const payload: RPCPayload<P> = {\n        call,\n        params: params || [],\n      };\n\n      const response = await this.communicator.send<Methods.rpcCall, RPCPayload<P>, R>(Methods.rpcCall, payload);\n\n      return response.data;\n    };\n  }\n}\n\nexport { Eth };\n", "const MAGIC_VALUE = '0x1626ba7e';\nconst MAGIC_VALUE_BYTES = '0x20c13b0b';\n\nexport { MAGIC_VALUE, MAGIC_VALUE_BYTES };\n", "export type Permission = {\n  parentCapability: string;\n  invoker: string;\n  date?: number;\n  caveats?: PermissionCaveat[];\n};\n\nexport type PermissionRequest = {\n  [method: string]: Record<string, unknown>;\n};\n\nexport type PermissionCaveat = {\n  type: string;\n  value?: unknown;\n  name?: string;\n};\n\nexport const PERMISSIONS_REQUEST_REJECTED = 4001;\n\nexport class PermissionsError extends Error {\n  public code: number;\n  public data?: unknown;\n\n  constructor(message: string, code: number, data?: unknown) {\n    super(message);\n\n    this.code = code;\n    this.data = data;\n\n    // Should adjust prototype manually because how TS handles the type extension compilation\n    // https://github.com/Microsoft/TypeScript/wiki/Breaking-Changes#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, PermissionsError.prototype);\n  }\n}\n", "import { Methods, RestrictedMethods } from '../communication/methods.js';\nimport { Communicator } from '../types/index.js';\nimport { PermissionRequest, Permission, PermissionsError, PERMISSIONS_REQUEST_REJECTED } from '../types/permissions.js';\n\nclass Wallet {\n  private readonly communicator: Communicator;\n\n  constructor(communicator: Communicator) {\n    this.communicator = communicator;\n  }\n\n  async getPermissions(): Promise<Permission[]> {\n    const response = await this.communicator.send<Methods.wallet_getPermissions, undefined, Permission[]>(\n      Methods.wallet_getPermissions,\n      undefined,\n    );\n\n    return response.data;\n  }\n\n  async requestPermissions(permissions: PermissionRequest[]): Promise<Permission[]> {\n    if (!this.isPermissionRequestValid(permissions)) {\n      throw new PermissionsError('Permissions request is invalid', PERMISSIONS_REQUEST_REJECTED);\n    }\n\n    try {\n      const response = await this.communicator.send<\n        Methods.wallet_requestPermissions,\n        PermissionRequest[],\n        Permission[]\n      >(Methods.wallet_requestPermissions, permissions);\n\n      return response.data;\n    } catch {\n      throw new PermissionsError('Permissions rejected', PERMISSIONS_REQUEST_REJECTED);\n    }\n  }\n\n  isPermissionRequestValid(permissions: PermissionRequest[]): boolean {\n    return permissions.every((pr: PermissionRequest) => {\n      if (typeof pr === 'object') {\n        return Object.keys(pr).every((method) => {\n          if (Object.values(RestrictedMethods).includes(method as RestrictedMethods)) {\n            return true;\n          }\n\n          return false;\n        });\n      }\n\n      return false;\n    });\n  }\n}\n\nexport { Wallet };\n", "import { Methods } from '../communication/index.js';\nimport { Safe } from '../safe/index.js';\nimport { Wallet } from '../wallet/index.js';\n\nimport { Permission, PermissionsError, PERMISSIONS_REQUEST_REJECTED } from '../types/permissions.js';\n\nconst hasPermission = (required: Methods, permissions: Permission[]): boolean =>\n  permissions.some((permission) => permission.parentCapability === required);\n\nconst requirePermission = () => (_: unknown, propertyKey: string, descriptor: PropertyDescriptor) => {\n  const originalMethod = descriptor.value;\n\n  descriptor.value = async function () {\n    // @ts-expect-error accessing private property from decorator. 'this' context is the class instance\n    const wallet = new Wallet((this as Safe).communicator);\n\n    let currentPermissions = await wallet.getPermissions();\n\n    if (!hasPermission(propertyKey as Methods, currentPermissions)) {\n      currentPermissions = await wallet.requestPermissions([{ [propertyKey as Methods]: {} }]);\n    }\n\n    if (!hasPermission(propertyKey as Methods, currentPermissions)) {\n      throw new PermissionsError('Permissions rejected', PERMISSIONS_REQUEST_REJECTED);\n    }\n\n    return originalMethod.apply(this);\n  };\n\n  return descriptor;\n};\n\nexport default requirePermission;\n", "import { encodeFunctionD<PERSON>, Address, hashMessage, hashTypedData } from 'viem';\nimport { MAGIC_VALUE_BYTES, MAGIC_VALUE } from './signatures.js';\nimport { Methods } from '../communication/methods.js';\nimport { RPC_CALLS } from '../eth/constants.js';\nimport {\n  Communicator,\n  ChainInfo,\n  SafeBalances,\n  GetBalanceParams,\n  RPCPayload,\n  TransactionConfig,\n  EnvironmentInfo,\n  AddressBookItem,\n  isObjectEIP712TypedData,\n  EIP712TypedData,\n  SafeInfoExtended,\n} from '../types/index.js';\nimport requirePermission from '../decorators/requirePermissions.js';\n\nclass Safe {\n  private readonly communicator: Communicator;\n\n  constructor(communicator: Communicator) {\n    this.communicator = communicator;\n  }\n\n  async getChainInfo(): Promise<ChainInfo> {\n    const response = await this.communicator.send<Methods.getChainInfo, undefined, ChainInfo>(\n      Methods.getChainInfo,\n      undefined,\n    );\n\n    return response.data;\n  }\n\n  async getInfo(): Promise<SafeInfoExtended> {\n    const response = await this.communicator.send<Methods.getSafeInfo, undefined, SafeInfoExtended>(\n      Methods.getSafeInfo,\n      undefined,\n    );\n\n    return response.data;\n  }\n\n  // There is a possibility that this method will change because we may add pagination to the endpoint\n  async experimental_getBalances({ currency = 'usd' }: GetBalanceParams = {}): Promise<SafeBalances> {\n    const response = await this.communicator.send<Methods.getSafeBalances, { currency: string }, SafeBalances>(\n      Methods.getSafeBalances,\n      {\n        currency,\n      },\n    );\n\n    return response.data;\n  }\n\n  private async check1271Signature(messageHash: string, signature = '0x'): Promise<boolean> {\n    const safeInfo = await this.getInfo();\n\n    const encodedIsValidSignatureCall = encodeFunctionData({\n      abi: [\n        {\n          constant: false,\n          inputs: [\n            {\n              name: '_dataHash',\n              type: 'bytes32',\n            },\n            {\n              name: '_signature',\n              type: 'bytes',\n            },\n          ],\n          name: 'isValidSignature',\n          outputs: [\n            {\n              name: '',\n              type: 'bytes4',\n            },\n          ],\n          payable: false,\n          stateMutability: 'nonpayable',\n          type: 'function',\n        },\n      ] as const,\n      functionName: 'isValidSignature',\n      args: [messageHash as Address, signature as Address],\n    });\n\n    const payload = {\n      call: RPC_CALLS.eth_call,\n      params: [\n        {\n          to: safeInfo.safeAddress,\n          data: encodedIsValidSignatureCall,\n        },\n        'latest',\n      ],\n    };\n    try {\n      const response = await this.communicator.send<Methods.rpcCall, RPCPayload<[TransactionConfig, string]>, string>(\n        Methods.rpcCall,\n        payload,\n      );\n\n      return response.data.slice(0, 10).toLowerCase() === MAGIC_VALUE;\n    } catch (err) {\n      return false;\n    }\n  }\n\n  private async check1271SignatureBytes(messageHash: string, signature = '0x'): Promise<boolean> {\n    const safeInfo = await this.getInfo();\n\n    const encodedIsValidSignatureCall = encodeFunctionData({\n      abi: [\n        {\n          constant: false,\n          inputs: [\n            {\n              name: '_data',\n              type: 'bytes',\n            },\n            {\n              name: '_signature',\n              type: 'bytes',\n            },\n          ],\n          name: 'isValidSignature',\n          outputs: [\n            {\n              name: '',\n              type: 'bytes4',\n            },\n          ],\n          payable: false,\n          stateMutability: 'nonpayable',\n          type: 'function',\n        },\n      ] as const,\n      functionName: 'isValidSignature',\n      args: [messageHash as Address, signature as Address],\n    });\n\n    const payload = {\n      call: RPC_CALLS.eth_call,\n      params: [\n        {\n          to: safeInfo.safeAddress,\n          data: encodedIsValidSignatureCall,\n        },\n        'latest',\n      ],\n    };\n\n    try {\n      const response = await this.communicator.send<Methods.rpcCall, RPCPayload<[TransactionConfig, string]>, string>(\n        Methods.rpcCall,\n        payload,\n      );\n\n      return response.data.slice(0, 10).toLowerCase() === MAGIC_VALUE_BYTES;\n    } catch (err) {\n      return false;\n    }\n  }\n\n  calculateMessageHash(message: string): string {\n    return hashMessage(message);\n  }\n\n  calculateTypedMessageHash(typedMessage: EIP712TypedData): string {\n    const chainId =\n      typeof typedMessage.domain.chainId === 'object'\n        ? typedMessage.domain.chainId.toNumber()\n        : Number(typedMessage.domain.chainId);\n\n    let primaryType = typedMessage.primaryType;\n    if (!primaryType) {\n      const fields = Object.values(typedMessage.types);\n      // We try to infer primaryType (simplified ether's version)\n      const primaryTypes = Object.keys(typedMessage.types).filter((typeName) =>\n        fields.every((dataTypes) => dataTypes.every(({ type }) => type.replace('[', '').replace(']', '') !== typeName)),\n      );\n      if (primaryTypes.length === 0 || primaryTypes.length > 1) throw new Error('Please specify primaryType');\n      primaryType = primaryTypes[0];\n    }\n\n    return hashTypedData({\n      message: typedMessage.message,\n      domain: {\n        ...typedMessage.domain,\n        chainId,\n        verifyingContract: typedMessage.domain.verifyingContract as Address,\n        salt: typedMessage.domain.salt as Address,\n      },\n      types: typedMessage.types,\n      primaryType,\n    });\n  }\n\n  async getOffChainSignature(messageHash: string): Promise<string> {\n    const response = await this.communicator.send<Methods.getOffChainSignature, string, string>(\n      Methods.getOffChainSignature,\n      messageHash,\n    );\n\n    return response.data;\n  }\n\n  async isMessageSigned(message: string | EIP712TypedData, signature = '0x'): Promise<boolean> {\n    let check: (() => Promise<boolean>) | undefined;\n    if (typeof message === 'string') {\n      check = async (): Promise<boolean> => {\n        const messageHash = this.calculateMessageHash(message);\n        const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n        return messageHashSigned;\n      };\n    }\n\n    if (isObjectEIP712TypedData(message)) {\n      check = async (): Promise<boolean> => {\n        const messageHash = this.calculateTypedMessageHash(message);\n        const messageHashSigned = await this.isMessageHashSigned(messageHash, signature);\n        return messageHashSigned;\n      };\n    }\n    if (check) {\n      const isValid = await check();\n\n      return isValid;\n    }\n\n    throw new Error('Invalid message type');\n  }\n\n  async isMessageHashSigned(messageHash: string, signature = '0x'): Promise<boolean> {\n    const checks = [this.check1271Signature.bind(this), this.check1271SignatureBytes.bind(this)];\n\n    for (const check of checks) {\n      const isValid = await check(messageHash, signature);\n      if (isValid) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  async getEnvironmentInfo(): Promise<EnvironmentInfo> {\n    const response = await this.communicator.send<Methods.getEnvironmentInfo, undefined, EnvironmentInfo>(\n      Methods.getEnvironmentInfo,\n      undefined,\n    );\n\n    return response.data;\n  }\n\n  @requirePermission()\n  async requestAddressBook(): Promise<AddressBookItem[]> {\n    const response = await this.communicator.send<Methods.requestAddressBook, undefined, AddressBookItem[]>(\n      Methods.requestAddressBook,\n      undefined,\n    );\n\n    return response.data;\n  }\n}\n\nexport { Safe };\n", "import { Communicator } from './types/index.js';\nimport InterfaceCommunicator from './communication/index.js';\nimport { TXs } from './txs/index.js';\nimport { Eth } from './eth/index.js';\nimport { Safe } from './safe/index.js';\nimport { Wallet } from './wallet/index.js';\n\nexport type Opts = {\n  allowedDomains?: RegExp[];\n  debug?: boolean;\n};\n\nclass SafeAppsSDK {\n  private readonly communicator: Communicator;\n  public readonly eth: Eth;\n  public readonly txs: TXs;\n  public readonly safe: Safe;\n  public readonly wallet: Wallet;\n\n  constructor(opts: Opts = {}) {\n    const { allowedDomains = null, debug = false } = opts;\n\n    this.communicator = new InterfaceCommunicator(allowedDomains, debug);\n    this.eth = new Eth(this.communicator);\n    this.txs = new TXs(this.communicator);\n    this.safe = new Safe(this.communicator);\n    this.wallet = new Wallet(this.communicator);\n  }\n}\n\nexport default SafeAppsSDK;\n", "import SDK from './sdk.js';\n\nexport default SDK;\nexport * from './sdk.js';\nexport * from './types/index.js';\nexport * from './communication/methods.js';\nexport * from './communication/messageFormatter.js';\nexport { getSDKVersion } from './version.js';\nexport * from './eth/constants.js';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAO,IAAM,gBAAgB,MAAM;;;ACCnC,IAAM,UAAU,CAAC,QAAwB,IAAI,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AAEzE,IAAM,aAAa,CAAC,QAAuB;AACzC,QAAM,MAAM,IAAI,YAAY,OAAO,MAAM,CAAC;AAC1C,SAAO,OAAO,gBAAgB,GAAG;AACjC,SAAO,MAAM,KAAK,KAAK,OAAO,EAAE,KAAK,EAAE;AACzC;AAEA,IAAM,oBAAoB,MAAa;AACrC,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO,WAAW,EAAE;;AAGtB,UAAO,oBAAI,KAAI,GAAG,QAAO,EAAG,SAAS,EAAE;AACzC;;;ACVA,IAAM,mBAAN,MAAsB;;AACb,iBAAA,cAAc,CAA2C,QAAW,WAAmC;AAC5G,QAAM,KAAK,kBAAiB;AAE5B,SAAO;IACL;IACA;IACA;IACA,KAAK;MACH,YAAY,cAAa;;;AAG/B;AAEO,iBAAA,eAAe,CAAC,IAAe,MAAiC,aAAsC;EAC3G;EACA,SAAS;EACT;EACA;;AAGK,iBAAA,oBAAoB,CAAC,IAAe,OAAe,aAAoC;EAC5F;EACA,SAAS;EACT;EACA;;;;AC9BJ,IAAY;CAAZ,SAAYA,UAAO;AACjB,EAAAA,SAAA,kBAAA,IAAA;AACA,EAAAA,SAAA,SAAA,IAAA;AACA,EAAAA,SAAA,cAAA,IAAA;AACA,EAAAA,SAAA,aAAA,IAAA;AACA,EAAAA,SAAA,mBAAA,IAAA;AACA,EAAAA,SAAA,iBAAA,IAAA;AACA,EAAAA,SAAA,aAAA,IAAA;AACA,EAAAA,SAAA,kBAAA,IAAA;AACA,EAAAA,SAAA,oBAAA,IAAA;AACA,EAAAA,SAAA,sBAAA,IAAA;AACA,EAAAA,SAAA,oBAAA,IAAA;AACA,EAAAA,SAAA,uBAAA,IAAA;AACA,EAAAA,SAAA,2BAAA,IAAA;AACF,GAdY,YAAA,UAAO,CAAA,EAAA;AAgBnB,IAAY;CAAZ,SAAYC,oBAAiB;AAC3B,EAAAA,mBAAA,oBAAA,IAAA;AACF,GAFY,sBAAA,oBAAiB,CAAA,EAAA;;;ACT7B,IAAM,0BAAN,MAA6B;EAM3B,YAAY,iBAAkC,MAAM,YAAY,OAAK;AALpD,SAAA,iBAAkC;AAC3C,SAAA,YAAY,oBAAI,IAAG;AACnB,SAAA,YAAY;AACZ,SAAA,WAAW,OAAO,WAAW;AAW7B,SAAA,iBAAiB,CAAC,EAAE,QAAQ,MAAM,OAAM,MAAsC;AACpF,YAAM,mBAAmB,CAAC;AAC1B,YAAM,mBAAmB,CAAC,KAAK,YAAY,WAAW,OAAO;AAC7D,YAAM,qBAAqB,OAAO,KAAK,YAAY,eAAe,SAAS,KAAK,QAAQ,MAAM,GAAG,EAAE,CAAC,CAAC;AACrG,YAAM,oBAAoB,OAAO,uBAAuB,YAAY,sBAAsB;AAC1F,UAAI,cAAc;AAClB,UAAI,MAAM,QAAQ,KAAK,cAAc,GAAG;AACtC,sBAAc,KAAK,eAAe,KAAK,CAAC,WAAW,OAAO,KAAK,MAAM,CAAC,MAAM;;AAG9E,aAAO,CAAC,oBAAoB,oBAAoB,qBAAqB;IACvE;AAEQ,SAAA,qBAAqB,CAAC,QAAoC;AAChE,cAAQ,KAAK,wDAAwD,IAAI,MAAM,MAAM,IAAI,IAAI;IAC/F;AAEQ,SAAA,kBAAkB,CAAC,QAAoC;AAC7D,UAAI,KAAK,eAAe,GAAG,GAAG;AAC5B,aAAK,aAAa,KAAK,mBAAmB,GAAG;AAC7C,aAAK,sBAAsB,IAAI,IAAI;;IAEvC;AAEQ,SAAA,wBAAwB,CAAC,YAAgD;AAC/E,YAAM,EAAE,GAAE,IAAK;AAEf,YAAM,KAAK,KAAK,UAAU,IAAI,EAAE;AAChC,UAAI,IAAI;AACN,WAAG,OAAO;AAEV,aAAK,UAAU,OAAO,EAAE;;IAE5B;AAEO,SAAA,OAAO,CAA0B,QAAW,WAA0C;AAC3F,YAAM,UAAU,iBAAiB,YAAY,QAAQ,MAAM;AAE3D,UAAI,KAAK,UAAU;AACjB,cAAM,IAAI,MAAM,sBAAsB;;AAGxC,aAAO,OAAO,YAAY,SAAS,GAAG;AACtC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,aAAK,UAAU,IAAI,QAAQ,IAAI,CAAC,aAAyB;AACvD,cAAI,CAAC,SAAS,SAAS;AACrB,mBAAO,IAAI,MAAM,SAAS,KAAK,CAAC;AAChC;;AAGF,kBAAQ,QAAQ;QAClB,CAAC;MACH,CAAC;IACH;AA7DE,SAAK,iBAAiB;AACtB,SAAK,YAAY;AAEjB,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO,iBAAiB,WAAW,KAAK,eAAe;;EAE3D;;AA0DF,IAAA,wBAAe;;;ACuBR,IAAM,0BAA0B,CAAC,QAAyC;AAC/E,SAAO,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO,WAAW,OAAO,aAAa;AACrG;;;ACrGA,yCAkCO;;;ACtBP,IAAM,MAAN,MAAS;EAGP,YAAY,cAA0B;AACpC,SAAK,eAAe;EACtB;EAEA,MAAM,gBAAgB,YAAkB;AACtC,QAAI,CAAC,YAAY;AACf,YAAM,IAAI,MAAM,oBAAoB;;AAGtC,UAAM,WAAW,MAAM,KAAK,aAAa,KAIvC,QAAQ,mBAAmB,EAAE,WAAU,CAAE;AAE3C,WAAO,SAAS;EAClB;EAEA,MAAM,YAAY,SAAe;AAC/B,UAAM,iBAAiB;MACrB;;AAGF,UAAM,WAAW,MAAM,KAAK,aAAa,KACvC,QAAQ,aACR,cAAc;AAGhB,WAAO,SAAS;EAClB;EAEA,MAAM,iBAAiB,WAA0B;AAC/C,QAAI,CAAC,wBAAwB,SAAS,GAAG;AACvC,YAAM,IAAI,MAAM,oBAAoB;;AAGtC,UAAM,WAAW,MAAM,KAAK,aAAa,KAIvC,QAAQ,kBAAkB,EAAE,UAAS,CAAE;AAEzC,WAAO,SAAS;EAClB;EAEA,MAAM,KAAK,EAAE,KAAK,OAAM,GAA0B;AAChD,QAAI,CAAC,OAAO,CAAC,IAAI,QAAQ;AACvB,YAAM,IAAI,MAAM,6BAA6B;;AAG/C,UAAM,iBAAiB;MACrB;MACA;;AAGF,UAAM,WAAW,MAAM,KAAK,aAAa,KAIvC,QAAQ,kBAAkB,cAAc;AAE1C,WAAO,SAAS;EAClB;;;;AC/EK,IAAM,YAAY;EACvB,UAAU;EACV,cAAc;EACd,aAAa;EACb,gBAAgB;EAChB,aAAa;EACb,oBAAoB;EACpB,sBAAsB;EACtB,kBAAkB;EAClB,0BAA0B;EAC1B,2BAA2B;EAC3B,yBAAyB;EACzB,iBAAiB;EACjB,kBAAkB;;;;ACOpB,IAAM,kBAA6C;EACjD,mBAAmB,CAAC,MAAM,aAAa;EACvC,yBAAyB,CAAC,MAAM,UAAmB;EACnD,kBAAkB,CAAC,QACjB,OAAO,UAAU,GAAG,IAAI,KAAK,IAAI,SAAS,EAAE,CAAC,KAAM;;AAQvD,IAAM,MAAN,MAAS;EAiBP,YAAY,cAA0B;AACpC,SAAK,eAAe;AACpB,SAAK,OAAO,KAAK,aAAmD;MAClE,MAAM,UAAU;MAChB,YAAY,CAAC,MAAM,gBAAgB,iBAAiB;KACrD;AACD,SAAK,aAAa,KAAK,aAAwC;MAC7D,MAAM,UAAU;MAChB,YAAY,CAAC,MAAM,gBAAgB,iBAAiB;KACrD;AACD,SAAK,UAAU,KAAK,aAAwC;MAC1D,MAAM,UAAU;MAChB,YAAY,CAAC,MAAM,gBAAgB,iBAAiB;KACrD;AACD,SAAK,eAAe,KAAK,aAAgD;MACvE,MAAM,UAAU;MAChB,YAAY,CAAC,MAAM,gBAAgB,kBAAkB,gBAAgB,iBAAiB;KACvF;AACD,SAAK,cAAc,KAAK,aAAuC;MAC7D,MAAM,UAAU;KACjB;AACD,SAAK,iBAAiB,KAAK,aAAkF;MAC3G,MAAM,UAAU;MAChB,YAAY,CAAC,MAAM,gBAAgB,uBAAuB;KAC3D;AACD,SAAK,mBAAmB,KAAK,aAG3B;MACA,MAAM,UAAU;MAChB,YAAY,CAAC,gBAAgB,kBAAkB,gBAAgB,uBAAuB;KACvF;AACD,SAAK,uBAAuB,KAAK,aAA8C;MAC7E,MAAM,UAAU;KACjB;AACD,SAAK,wBAAwB,KAAK,aAAqD;MACrF,MAAM,UAAU;KACjB;AACD,SAAK,sBAAsB,KAAK,aAAwC;MACtE,MAAM,UAAU;MAChB,YAAY,CAAC,MAAM,gBAAgB,iBAAiB;KACrD;AACD,SAAK,cAAc,KAAK,aAA8B;MACpD,MAAM,UAAU;KACjB;AACD,SAAK,iBAAiB,CAAC,gBACrB,KAAK,aAA0C;MAC7C,MAAM,UAAU;KACjB,EAAE,CAAC,WAAW,CAAC;AAClB,SAAK,kBAAkB,KAAK,aAA2C;MACrE,MAAM,UAAU;KACjB;EACH;EAEQ,aAAuC,MAAsB;AACnE,UAAM,EAAE,MAAM,WAAU,IAAK;AAE7B,WAAO,OAAO,WAA0B;AACtC,UAAI,cAAc,MAAM,QAAQ,MAAM,GAAG;AACvC,mBAAW,QAAQ,CAAC,WAAqD,MAAK;AAC5E,cAAI,WAAW;AACb,mBAAO,CAAC,IAAI,UAAU,OAAO,CAAC,CAAC;;QAEnC,CAAC;;AAGH,YAAM,UAAyB;QAC7B;QACA,QAAQ,UAAU,CAAA;;AAGpB,YAAM,WAAW,MAAM,KAAK,aAAa,KAAwC,QAAQ,SAAS,OAAO;AAEzG,aAAO,SAAS;IAClB;EACF;;;;AC5HF,IAAM,cAAc;AACpB,IAAM,oBAAoB;;;ACgBnB,IAAM,+BAA+B;AAEtC,IAAO,mBAAP,MAAO,0BAAyB,MAAK;EAIzC,YAAY,SAAiB,MAAc,MAAc;AACvD,UAAM,OAAO;AAEb,SAAK,OAAO;AACZ,SAAK,OAAO;AAIZ,WAAO,eAAe,MAAM,kBAAiB,SAAS;EACxD;;;;AC5BF,IAAM,SAAN,MAAY;EAGV,YAAY,cAA0B;AACpC,SAAK,eAAe;EACtB;EAEA,MAAM,iBAAc;AAClB,UAAM,WAAW,MAAM,KAAK,aAAa,KACvC,QAAQ,uBACR,MAAS;AAGX,WAAO,SAAS;EAClB;EAEA,MAAM,mBAAmB,aAAgC;AACvD,QAAI,CAAC,KAAK,yBAAyB,WAAW,GAAG;AAC/C,YAAM,IAAI,iBAAiB,kCAAkC,4BAA4B;;AAG3F,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,aAAa,KAIvC,QAAQ,2BAA2B,WAAW;AAEhD,aAAO,SAAS;YACV;AACN,YAAM,IAAI,iBAAiB,wBAAwB,4BAA4B;;EAEnF;EAEA,yBAAyB,aAAgC;AACvD,WAAO,YAAY,MAAM,CAAC,OAAyB;AACjD,UAAI,OAAO,OAAO,UAAU;AAC1B,eAAO,OAAO,KAAK,EAAE,EAAE,MAAM,CAAC,WAAU;AACtC,cAAI,OAAO,OAAO,iBAAiB,EAAE,SAAS,MAA2B,GAAG;AAC1E,mBAAO;;AAGT,iBAAO;QACT,CAAC;;AAGH,aAAO;IACT,CAAC;EACH;;;;AC9CF,IAAM,gBAAgB,CAAC,UAAmB,gBACxC,YAAY,KAAK,CAAC,eAAe,WAAW,qBAAqB,QAAQ;AAE3E,IAAM,oBAAoB,MAAM,CAAC,GAAY,aAAqB,eAAkC;AAClG,QAAM,iBAAiB,WAAW;AAElC,aAAW,QAAQ,iBAAK;AAEtB,UAAM,SAAS,IAAI,OAAQ,KAAc,YAAY;AAErD,QAAI,qBAAqB,MAAM,OAAO,eAAc;AAEpD,QAAI,CAAC,cAAc,aAAwB,kBAAkB,GAAG;AAC9D,2BAAqB,MAAM,OAAO,mBAAmB,CAAC,EAAE,CAAC,WAAsB,GAAG,CAAA,EAAE,CAAE,CAAC;;AAGzF,QAAI,CAAC,cAAc,aAAwB,kBAAkB,GAAG;AAC9D,YAAM,IAAI,iBAAiB,wBAAwB,4BAA4B;;AAGjF,WAAO,eAAe,MAAM,IAAI;EAClC;AAEA,SAAO;AACT;AAEA,IAAA,6BAAe;;;;;;;;;ACbf,IAAM,OAAN,MAAU;EAGR,YAAY,cAA0B;AACpC,SAAK,eAAe;EACtB;EAEA,MAAM,eAAY;AAChB,UAAM,WAAW,MAAM,KAAK,aAAa,KACvC,QAAQ,cACR,MAAS;AAGX,WAAO,SAAS;EAClB;EAEA,MAAM,UAAO;AACX,UAAM,WAAW,MAAM,KAAK,aAAa,KACvC,QAAQ,aACR,MAAS;AAGX,WAAO,SAAS;EAClB;;EAGA,MAAM,yBAAyB,EAAE,WAAW,MAAK,IAAuB,CAAA,GAAE;AACxE,UAAM,WAAW,MAAM,KAAK,aAAa,KACvC,QAAQ,iBACR;MACE;KACD;AAGH,WAAO,SAAS;EAClB;EAEQ,MAAM,mBAAmB,aAAqB,YAAY,MAAI;AACpE,UAAM,WAAW,MAAM,KAAK,QAAO;AAEnC,UAAM,8BAA8B,mBAAmB;MACrD,KAAK;QACH;UACE,UAAU;UACV,QAAQ;YACN;cACE,MAAM;cACN,MAAM;;YAER;cACE,MAAM;cACN,MAAM;;;UAGV,MAAM;UACN,SAAS;YACP;cACE,MAAM;cACN,MAAM;;;UAGV,SAAS;UACT,iBAAiB;UACjB,MAAM;;;MAGV,cAAc;MACd,MAAM,CAAC,aAAwB,SAAoB;KACpD;AAED,UAAM,UAAU;MACd,MAAM,UAAU;MAChB,QAAQ;QACN;UACE,IAAI,SAAS;UACb,MAAM;;QAER;;;AAGJ,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,aAAa,KACvC,QAAQ,SACR,OAAO;AAGT,aAAO,SAAS,KAAK,MAAM,GAAG,EAAE,EAAE,YAAW,MAAO;aAC7C,KAAK;AACZ,aAAO;;EAEX;EAEQ,MAAM,wBAAwB,aAAqB,YAAY,MAAI;AACzE,UAAM,WAAW,MAAM,KAAK,QAAO;AAEnC,UAAM,8BAA8B,mBAAmB;MACrD,KAAK;QACH;UACE,UAAU;UACV,QAAQ;YACN;cACE,MAAM;cACN,MAAM;;YAER;cACE,MAAM;cACN,MAAM;;;UAGV,MAAM;UACN,SAAS;YACP;cACE,MAAM;cACN,MAAM;;;UAGV,SAAS;UACT,iBAAiB;UACjB,MAAM;;;MAGV,cAAc;MACd,MAAM,CAAC,aAAwB,SAAoB;KACpD;AAED,UAAM,UAAU;MACd,MAAM,UAAU;MAChB,QAAQ;QACN;UACE,IAAI,SAAS;UACb,MAAM;;QAER;;;AAIJ,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,aAAa,KACvC,QAAQ,SACR,OAAO;AAGT,aAAO,SAAS,KAAK,MAAM,GAAG,EAAE,EAAE,YAAW,MAAO;aAC7C,KAAK;AACZ,aAAO;;EAEX;EAEA,qBAAqB,SAAe;AAClC,WAAO,YAAY,OAAO;EAC5B;EAEA,0BAA0B,cAA6B;AACrD,UAAM,UACJ,OAAO,aAAa,OAAO,YAAY,WACnC,aAAa,OAAO,QAAQ,SAAQ,IACpC,OAAO,aAAa,OAAO,OAAO;AAExC,QAAI,cAAc,aAAa;AAC/B,QAAI,CAAC,aAAa;AAChB,YAAM,SAAS,OAAO,OAAO,aAAa,KAAK;AAE/C,YAAM,eAAe,OAAO,KAAK,aAAa,KAAK,EAAE,OAAO,CAAC,aAC3D,OAAO,MAAM,CAAC,cAAc,UAAU,MAAM,CAAC,EAAE,KAAI,MAAO,KAAK,QAAQ,KAAK,EAAE,EAAE,QAAQ,KAAK,EAAE,MAAM,QAAQ,CAAC,CAAC;AAEjH,UAAI,aAAa,WAAW,KAAK,aAAa,SAAS;AAAG,cAAM,IAAI,MAAM,4BAA4B;AACtG,oBAAc,aAAa,CAAC;;AAG9B,WAAO,cAAc;MACnB,SAAS,aAAa;MACtB,QAAQ;QACN,GAAG,aAAa;QAChB;QACA,mBAAmB,aAAa,OAAO;QACvC,MAAM,aAAa,OAAO;;MAE5B,OAAO,aAAa;MACpB;KACD;EACH;EAEA,MAAM,qBAAqB,aAAmB;AAC5C,UAAM,WAAW,MAAM,KAAK,aAAa,KACvC,QAAQ,sBACR,WAAW;AAGb,WAAO,SAAS;EAClB;EAEA,MAAM,gBAAgB,SAAmC,YAAY,MAAI;AACvE,QAAI;AACJ,QAAI,OAAO,YAAY,UAAU;AAC/B,cAAQ,YAA6B;AACnC,cAAM,cAAc,KAAK,qBAAqB,OAAO;AACrD,cAAM,oBAAoB,MAAM,KAAK,oBAAoB,aAAa,SAAS;AAC/E,eAAO;MACT;;AAGF,QAAI,wBAAwB,OAAO,GAAG;AACpC,cAAQ,YAA6B;AACnC,cAAM,cAAc,KAAK,0BAA0B,OAAO;AAC1D,cAAM,oBAAoB,MAAM,KAAK,oBAAoB,aAAa,SAAS;AAC/E,eAAO;MACT;;AAEF,QAAI,OAAO;AACT,YAAM,UAAU,MAAM,MAAK;AAE3B,aAAO;;AAGT,UAAM,IAAI,MAAM,sBAAsB;EACxC;EAEA,MAAM,oBAAoB,aAAqB,YAAY,MAAI;AAC7D,UAAM,SAAS,CAAC,KAAK,mBAAmB,KAAK,IAAI,GAAG,KAAK,wBAAwB,KAAK,IAAI,CAAC;AAE3F,eAAW,SAAS,QAAQ;AAC1B,YAAM,UAAU,MAAM,MAAM,aAAa,SAAS;AAClD,UAAI,SAAS;AACX,eAAO;;;AAIX,WAAO;EACT;EAEA,MAAM,qBAAkB;AACtB,UAAM,WAAW,MAAM,KAAK,aAAa,KACvC,QAAQ,oBACR,MAAS;AAGX,WAAO,SAAS;EAClB;EAGA,MAAM,qBAAkB;AACtB,UAAM,WAAW,MAAM,KAAK,aAAa,KACvC,QAAQ,oBACR,MAAS;AAGX,WAAO,SAAS;EAClB;;AAPM,WAAA;EADL,2BAAiB;;;;ACtPpB,IAAM,cAAN,MAAiB;EAOf,YAAY,OAAa,CAAA,GAAE;AACzB,UAAM,EAAE,iBAAiB,MAAM,QAAQ,MAAK,IAAK;AAEjD,SAAK,eAAe,IAAI,sBAAsB,gBAAgB,KAAK;AACnE,SAAK,MAAM,IAAI,IAAI,KAAK,YAAY;AACpC,SAAK,MAAM,IAAI,IAAI,KAAK,YAAY;AACpC,SAAK,OAAO,IAAI,KAAK,KAAK,YAAY;AACtC,SAAK,SAAS,IAAI,OAAO,KAAK,YAAY;EAC5C;;AAGF,IAAA,cAAe;;;AC5Bf,IAAA,cAAe;", "names": ["Methods", "RestrictedMethods"]}