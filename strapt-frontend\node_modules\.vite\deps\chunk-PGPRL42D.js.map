{"version": 3, "sources": ["../../use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../use-sync-external-store/shim/index.js", "../../use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js", "../../use-sync-external-store/shim/with-selector.js", "../../wagmi/src/context.ts", "../../@wagmi/core/src/connectors/createConnector.ts", "../../@wagmi/core/src/connectors/injected.ts", "../../@wagmi/core/src/connectors/mock.ts", "../../mipd/src/utils.ts", "../../mipd/src/store.ts", "../../@wagmi/core/node_modules/zustand/esm/middleware.mjs", "../../@wagmi/core/node_modules/zustand/esm/vanilla.mjs", "../../@wagmi/core/src/createEmitter.ts", "../../@wagmi/core/src/utils/deserialize.ts", "../../@wagmi/core/src/utils/serialize.ts", "../../@wagmi/core/src/createStorage.ts", "../../@wagmi/core/src/utils/uid.ts", "../../@wagmi/core/src/createConfig.ts", "../../@wagmi/core/src/hydrate.ts", "../../@wagmi/core/src/transports/connector.ts", "../../@wagmi/core/src/transports/fallback.ts", "../../@wagmi/core/src/utils/cookie.ts", "../../@wagmi/core/src/utils/extractRpcUrls.ts", "../../@wagmi/core/src/utils/normalizeChainId.ts", "../../wagmi/src/hydrate.ts", "../../wagmi/src/version.ts", "../../wagmi/src/utils/getVersion.ts", "../../wagmi/src/errors/base.ts", "../../wagmi/src/errors/context.ts", "../../wagmi/src/hooks/useConfig.ts", "../../@wagmi/core/src/actions/watchChains.ts", "../../wagmi/src/hooks/useSyncExternalStoreWithTracked.ts", "../../wagmi/src/hooks/useAccount.ts", "../../wagmi/src/hooks/useAccountEffect.ts", "../../@wagmi/core/src/query/utils.ts", "../../@wagmi/core/src/query/call.ts", "../../@wagmi/core/src/query/connect.ts", "../../@wagmi/core/src/query/deployContract.ts", "../../@wagmi/core/src/query/disconnect.ts", "../../@wagmi/core/src/query/estimateFeesPerGas.ts", "../../@wagmi/core/src/query/estimateGas.ts", "../../@wagmi/core/src/query/estimateMaxPriorityFeePerGas.ts", "../../@wagmi/core/src/query/getBalance.ts", "../../@wagmi/core/src/query/getBlock.ts", "../../@wagmi/core/src/query/getBlockNumber.ts", "../../@wagmi/core/src/query/getBlockTransactionCount.ts", "../../@wagmi/core/src/query/getBytecode.ts", "../../@wagmi/core/src/query/getCallsStatus.ts", "../../@wagmi/core/src/query/getCapabilities.ts", "../../@wagmi/core/src/query/getConnectorClient.ts", "../../@wagmi/core/src/query/getEnsAddress.ts", "../../@wagmi/core/src/query/getEnsAvatar.ts", "../../@wagmi/core/src/query/getEnsName.ts", "../../@wagmi/core/src/query/getEnsResolver.ts", "../../@wagmi/core/src/query/getEnsText.ts", "../../@wagmi/core/src/query/getFeeHistory.ts", "../../@wagmi/core/src/query/getGasPrice.ts", "../../@wagmi/core/src/query/getProof.ts", "../../@wagmi/core/src/query/getStorageAt.ts", "../../@wagmi/core/src/query/getToken.ts", "../../@wagmi/core/src/query/getTransaction.ts", "../../@wagmi/core/src/query/getTransactionConfirmations.ts", "../../@wagmi/core/src/query/getTransactionCount.ts", "../../@wagmi/core/src/query/getTransactionReceipt.ts", "../../@wagmi/core/src/query/getWalletClient.ts", "../../@wagmi/core/src/query/infiniteReadContracts.ts", "../../@wagmi/core/src/query/prepareTransactionRequest.ts", "../../@wagmi/core/src/query/readContract.ts", "../../@wagmi/core/src/query/readContracts.ts", "../../@wagmi/core/src/query/reconnect.ts", "../../@wagmi/core/src/query/sendCalls.ts", "../../@wagmi/core/src/query/showCallsStatus.ts", "../../@wagmi/core/src/query/sendTransaction.ts", "../../@wagmi/core/src/query/signMessage.ts", "../../@wagmi/core/src/query/signTypedData.ts", "../../@wagmi/core/src/query/switchAccount.ts", "../../@wagmi/core/src/query/simulateContract.ts", "../../@wagmi/core/src/query/switchChain.ts", "../../@wagmi/core/src/query/verifyMessage.ts", "../../@wagmi/core/src/query/verifyTypedData.ts", "../../@wagmi/core/src/query/waitForCallsStatus.ts", "../../@wagmi/core/src/query/waitForTransactionReceipt.ts", "../../@wagmi/core/src/query/watchAsset.ts", "../../@wagmi/core/src/query/writeContract.ts", "../../wagmi/src/utils/query.ts", "../../wagmi/src/hooks/useChainId.ts", "../../wagmi/src/hooks/useBalance.ts", "../../wagmi/src/hooks/useWatchBlocks.ts", "../../wagmi/src/hooks/useBlock.ts", "../../wagmi/src/hooks/useWatchBlockNumber.ts", "../../wagmi/src/hooks/useBlockNumber.ts", "../../wagmi/src/hooks/useBlockTransactionCount.ts", "../../wagmi/src/hooks/useBytecode.ts", "../../wagmi/src/hooks/useCallsStatus.ts", "../../wagmi/src/hooks/useCapabilities.ts", "../../wagmi/src/hooks/useCall.ts", "../../wagmi/src/hooks/useChains.ts", "../../wagmi/src/hooks/useClient.ts", "../../wagmi/src/hooks/useConnect.ts", "../../wagmi/src/hooks/useConnectors.ts", "../../wagmi/src/hooks/useConnections.ts", "../../wagmi/src/hooks/useConnectorClient.ts", "../../wagmi/src/hooks/useDeployContract.ts", "../../wagmi/src/hooks/useDisconnect.ts", "../../wagmi/src/hooks/useEnsAddress.ts", "../../wagmi/src/hooks/useEnsAvatar.ts", "../../wagmi/src/hooks/useEnsName.ts", "../../wagmi/src/hooks/useEnsResolver.ts", "../../wagmi/src/hooks/useEnsText.ts", "../../wagmi/src/hooks/useEstimateFeesPerGas.ts", "../../wagmi/src/hooks/useEstimateGas.ts", "../../wagmi/src/hooks/useEstimateMaxPriorityFeePerGas.ts", "../../wagmi/src/hooks/useFeeHistory.ts", "../../wagmi/src/hooks/useGasPrice.ts", "../../wagmi/src/hooks/useInfiniteReadContracts.ts", "../../wagmi/src/hooks/usePrepareTransactionRequest.ts", "../../wagmi/src/hooks/useProof.ts", "../../wagmi/src/hooks/usePublicClient.ts", "../../wagmi/src/hooks/useReadContract.ts", "../../wagmi/src/hooks/useReadContracts.ts", "../../wagmi/src/hooks/useReconnect.ts", "../../wagmi/src/hooks/useSendCalls.ts", "../../wagmi/src/hooks/useSendTransaction.ts", "../../wagmi/src/hooks/useShowCallsStatus.ts", "../../wagmi/src/hooks/useSignMessage.ts", "../../wagmi/src/hooks/useSignTypedData.ts", "../../wagmi/src/hooks/useSimulateContract.ts", "../../wagmi/src/hooks/useStorageAt.ts", "../../wagmi/src/hooks/useSwitchAccount.ts", "../../wagmi/src/hooks/useSwitchChain.ts", "../../wagmi/src/hooks/useToken.ts", "../../wagmi/src/hooks/useTransaction.ts", "../../wagmi/src/hooks/useTransactionConfirmations.ts", "../../wagmi/src/hooks/useTransactionCount.ts", "../../wagmi/src/hooks/useTransactionReceipt.ts", "../../wagmi/src/hooks/useVerifyMessage.ts", "../../wagmi/src/hooks/useVerifyTypedData.ts", "../../wagmi/src/hooks/useWalletClient.ts", "../../wagmi/src/hooks/useWaitForCallsStatus.ts", "../../wagmi/src/hooks/useWaitForTransactionReceipt.ts", "../../wagmi/src/hooks/useWatchAsset.ts", "../../wagmi/src/hooks/useWatchContractEvent.ts", "../../wagmi/src/hooks/useWatchPendingTransactions.ts", "../../wagmi/src/hooks/useWriteContract.ts"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n", "'use client'\n\nimport type { ResolvedRegister, State } from '@wagmi/core'\nimport { createContext, createElement } from 'react'\nimport { Hydrate } from './hydrate.js'\n\nexport const WagmiContext = createContext<\n  ResolvedRegister['config'] | undefined\n>(undefined)\n\nexport type WagmiProviderProps = {\n  config: ResolvedRegister['config']\n  initialState?: State | undefined\n  reconnectOnMount?: boolean | undefined\n}\n\nexport function WagmiProvider(\n  parameters: React.PropsWithChildren<WagmiProviderProps>,\n) {\n  const { children, config } = parameters\n\n  const props = { value: config }\n  return createElement(\n    Hydrate,\n    parameters,\n    createElement(WagmiContext.Provider, props, children),\n  )\n}\n", "import type {\n  AddEthereumChainParameter,\n  Address,\n  Chain,\n  Client,\n  ProviderConnectInfo,\n  ProviderMessage,\n} from 'viem'\n\nimport type { Transport } from '../createConfig.js'\nimport type { Emitter } from '../createEmitter.js'\nimport type { Storage } from '../createStorage.js'\nimport type { Compute, ExactPartial, StrictOmit } from '../types/utils.js'\n\nexport type ConnectorEventMap = {\n  change: {\n    accounts?: readonly Address[] | undefined\n    chainId?: number | undefined\n  }\n  connect: { accounts: readonly Address[]; chainId: number }\n  disconnect: never\n  error: { error: Error }\n  message: { type: string; data?: unknown | undefined }\n}\n\nexport type CreateConnectorFn<\n  provider = unknown,\n  properties extends Record<string, unknown> = Record<string, unknown>,\n  storageItem extends Record<string, unknown> = Record<string, unknown>,\n> = (config: {\n  chains: readonly [Chain, ...Chain[]]\n  emitter: Emitter<ConnectorEventMap>\n  storage?: Compute<Storage<storageItem>> | null | undefined\n  transports?: Record<number, Transport> | undefined\n}) => Compute<\n  {\n    readonly icon?: string | undefined\n    readonly id: string\n    readonly name: string\n    readonly rdns?: string | readonly string[] | undefined\n    /** @deprecated */\n    readonly supportsSimulation?: boolean | undefined\n    readonly type: string\n\n    setup?(): Promise<void>\n    connect(\n      parameters?:\n        | { chainId?: number | undefined; isReconnecting?: boolean | undefined }\n        | undefined,\n    ): Promise<{\n      accounts: readonly Address[]\n      chainId: number\n    }>\n    disconnect(): Promise<void>\n    getAccounts(): Promise<readonly Address[]>\n    getChainId(): Promise<number>\n    getProvider(\n      parameters?: { chainId?: number | undefined } | undefined,\n    ): Promise<provider>\n    getClient?(\n      parameters?: { chainId?: number | undefined } | undefined,\n    ): Promise<Client>\n    isAuthorized(): Promise<boolean>\n    switchChain?(\n      parameters: Compute<{\n        addEthereumChainParameter?:\n          | ExactPartial<StrictOmit<AddEthereumChainParameter, 'chainId'>>\n          | undefined\n        chainId: number\n      }>,\n    ): Promise<Chain>\n\n    onAccountsChanged(accounts: string[]): void\n    onChainChanged(chainId: string): void\n    onConnect?(connectInfo: ProviderConnectInfo): void\n    onDisconnect(error?: Error | undefined): void\n    onMessage?(message: ProviderMessage): void\n  } & properties\n>\n\nexport function createConnector<\n  provider,\n  properties extends Record<string, unknown> = Record<string, unknown>,\n  storageItem extends Record<string, unknown> = Record<string, unknown>,\n  ///\n  createConnectorFn extends CreateConnectorFn<\n    provider,\n    properties,\n    storageItem\n  > = CreateConnectorFn<provider, properties, storageItem>,\n>(createConnectorFn: createConnectorFn) {\n  return createConnectorFn\n}\n", "import {\n  type AddEthereumChainParameter,\n  type Address,\n  type EIP1193<PERSON>rovider,\n  type ProviderConnectInfo,\n  type ProviderRpcError,\n  ResourceUnavailableRpcError,\n  type RpcError,\n  SwitchChainError,\n  UserRejectedRequestError,\n  getAddress,\n  numberToHex,\n  withRetry,\n  withTimeout,\n} from 'viem'\n\nimport type { Connector } from '../createConfig.js'\nimport { ChainNotConfiguredError } from '../errors/config.js'\nimport { ProviderNotFoundError } from '../errors/connector.js'\nimport type { Compute } from '../types/utils.js'\nimport { createConnector } from './createConnector.js'\n\nexport type InjectedParameters = {\n  /**\n   * Some injected providers do not support programmatic disconnect.\n   * This flag simulates the disconnect behavior by keeping track of connection status in storage.\n   * @default true\n   */\n  shimDisconnect?: boolean | undefined\n  /**\n   * [EIP-1193](https://eips.ethereum.org/EIPS/eip-1193) Ethereum Provider to target\n   */\n  target?: TargetId | Target | (() => Target | undefined) | undefined\n  unstable_shimAsyncInject?: boolean | number | undefined\n}\n\ninjected.type = 'injected' as const\nexport function injected(parameters: InjectedParameters = {}) {\n  const { shimDisconnect = true, unstable_shimAsyncInject } = parameters\n\n  function getTarget(): Compute<Target & { id: string }> {\n    const target = parameters.target\n    if (typeof target === 'function') {\n      const result = target()\n      if (result) return result\n    }\n\n    if (typeof target === 'object') return target\n\n    if (typeof target === 'string')\n      return {\n        ...(targetMap[target as keyof typeof targetMap] ?? {\n          id: target,\n          name: `${target[0]!.toUpperCase()}${target.slice(1)}`,\n          provider: `is${target[0]!.toUpperCase()}${target.slice(1)}`,\n        }),\n      }\n\n    return {\n      id: 'injected',\n      name: 'Injected',\n      provider(window) {\n        return window?.ethereum\n      },\n    }\n  }\n\n  type Provider = WalletProvider | undefined\n  type Properties = {\n    onConnect(connectInfo: ProviderConnectInfo): void\n  }\n  type StorageItem = {\n    [_ in 'injected.connected' | `${string}.disconnected`]: true\n  }\n\n  let accountsChanged: Connector['onAccountsChanged'] | undefined\n  let chainChanged: Connector['onChainChanged'] | undefined\n  let connect: Connector['onConnect'] | undefined\n  let disconnect: Connector['onDisconnect'] | undefined\n\n  return createConnector<Provider, Properties, StorageItem>((config) => ({\n    get icon() {\n      return getTarget().icon\n    },\n    get id() {\n      return getTarget().id\n    },\n    get name() {\n      return getTarget().name\n    },\n    /** @deprecated */\n    get supportsSimulation() {\n      return true\n    },\n    type: injected.type,\n    async setup() {\n      const provider = await this.getProvider()\n      // Only start listening for events if `target` is set, otherwise `injected()` will also receive events\n      if (provider?.on && parameters.target) {\n        if (!connect) {\n          connect = this.onConnect.bind(this)\n          provider.on('connect', connect)\n        }\n\n        // We shouldn't need to listen for `'accountsChanged'` here since the `'connect'` event should suffice (and wallet shouldn't be connected yet).\n        // Some wallets, like MetaMask, do not implement the `'connect'` event and overload `'accountsChanged'` instead.\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged)\n        }\n      }\n    },\n    async connect({ chainId, isReconnecting } = {}) {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n\n      let accounts: readonly Address[] = []\n      if (isReconnecting) accounts = await this.getAccounts().catch(() => [])\n      else if (shimDisconnect) {\n        // Attempt to show another prompt for selecting account if `shimDisconnect` flag is enabled\n        try {\n          const permissions = await provider.request({\n            method: 'wallet_requestPermissions',\n            params: [{ eth_accounts: {} }],\n          })\n          accounts = (permissions[0]?.caveats?.[0]?.value as string[])?.map(\n            (x) => getAddress(x),\n          )\n          // `'wallet_requestPermissions'` can return a different order of accounts than `'eth_accounts'`\n          // switch to `'eth_accounts'` ordering if more than one account is connected\n          // https://github.com/wevm/wagmi/issues/4140\n          if (accounts.length > 0) {\n            const sortedAccounts = await this.getAccounts()\n            accounts = sortedAccounts\n          }\n        } catch (err) {\n          const error = err as RpcError\n          // Not all injected providers support `wallet_requestPermissions` (e.g. MetaMask iOS).\n          // Only bubble up error if user rejects request\n          if (error.code === UserRejectedRequestError.code)\n            throw new UserRejectedRequestError(error)\n          // Or prompt is already open\n          if (error.code === ResourceUnavailableRpcError.code) throw error\n        }\n      }\n\n      try {\n        if (!accounts?.length && !isReconnecting) {\n          const requestedAccounts = await provider.request({\n            method: 'eth_requestAccounts',\n          })\n          accounts = requestedAccounts.map((x) => getAddress(x))\n        }\n\n        // Manage EIP-1193 event listeners\n        // https://eips.ethereum.org/EIPS/eip-1193#events\n        if (connect) {\n          provider.removeListener('connect', connect)\n          connect = undefined\n        }\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged)\n        }\n        if (!chainChanged) {\n          chainChanged = this.onChainChanged.bind(this)\n          provider.on('chainChanged', chainChanged)\n        }\n        if (!disconnect) {\n          disconnect = this.onDisconnect.bind(this)\n          provider.on('disconnect', disconnect)\n        }\n\n        // Switch to chain if provided\n        let currentChainId = await this.getChainId()\n        if (chainId && currentChainId !== chainId) {\n          const chain = await this.switchChain!({ chainId }).catch((error) => {\n            if (error.code === UserRejectedRequestError.code) throw error\n            return { id: currentChainId }\n          })\n          currentChainId = chain?.id ?? currentChainId\n        }\n\n        // Remove disconnected shim if it exists\n        if (shimDisconnect)\n          await config.storage?.removeItem(`${this.id}.disconnected`)\n\n        // Add connected shim if no target exists\n        if (!parameters.target)\n          await config.storage?.setItem('injected.connected', true)\n\n        return { accounts, chainId: currentChainId }\n      } catch (err) {\n        const error = err as RpcError\n        if (error.code === UserRejectedRequestError.code)\n          throw new UserRejectedRequestError(error)\n        if (error.code === ResourceUnavailableRpcError.code)\n          throw new ResourceUnavailableRpcError(error)\n        throw error\n      }\n    },\n    async disconnect() {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n\n      // Manage EIP-1193 event listeners\n      if (chainChanged) {\n        provider.removeListener('chainChanged', chainChanged)\n        chainChanged = undefined\n      }\n      if (disconnect) {\n        provider.removeListener('disconnect', disconnect)\n        disconnect = undefined\n      }\n      if (!connect) {\n        connect = this.onConnect.bind(this)\n        provider.on('connect', connect)\n      }\n\n      // Experimental support for MetaMask disconnect\n      // https://github.com/MetaMask/metamask-improvement-proposals/blob/main/MIPs/mip-2.md\n      try {\n        // Adding timeout as not all wallets support this method and can hang\n        // https://github.com/wevm/wagmi/issues/4064\n        await withTimeout(\n          () =>\n            // TODO: Remove explicit type for viem@3\n            provider.request<{\n              Method: 'wallet_revokePermissions'\n              Parameters: [permissions: { eth_accounts: Record<string, any> }]\n              ReturnType: null\n            }>({\n              // `'wallet_revokePermissions'` added in `viem@2.10.3`\n              method: 'wallet_revokePermissions',\n              params: [{ eth_accounts: {} }],\n            }),\n          { timeout: 100 },\n        )\n      } catch {}\n\n      // Add shim signalling connector is disconnected\n      if (shimDisconnect) {\n        await config.storage?.setItem(`${this.id}.disconnected`, true)\n      }\n\n      if (!parameters.target)\n        await config.storage?.removeItem('injected.connected')\n    },\n    async getAccounts() {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n      const accounts = await provider.request({ method: 'eth_accounts' })\n      return accounts.map((x) => getAddress(x))\n    },\n    async getChainId() {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n      const hexChainId = await provider.request({ method: 'eth_chainId' })\n      return Number(hexChainId)\n    },\n    async getProvider() {\n      if (typeof window === 'undefined') return undefined\n\n      let provider: Provider\n      const target = getTarget()\n      if (typeof target.provider === 'function')\n        provider = target.provider(window as Window | undefined)\n      else if (typeof target.provider === 'string')\n        provider = findProvider(window, target.provider)\n      else provider = target.provider\n\n      // Some wallets do not conform to EIP-1193 (e.g. Trust Wallet)\n      // https://github.com/wevm/wagmi/issues/3526#issuecomment-**********\n      if (provider && !provider.removeListener) {\n        // Try using `off` handler if it exists, otherwise noop\n        if ('off' in provider && typeof provider.off === 'function')\n          provider.removeListener =\n            provider.off as typeof provider.removeListener\n        else provider.removeListener = () => {}\n      }\n\n      return provider\n    },\n    async isAuthorized() {\n      try {\n        const isDisconnected =\n          shimDisconnect &&\n          // If shim exists in storage, connector is disconnected\n          (await config.storage?.getItem(`${this.id}.disconnected`))\n        if (isDisconnected) return false\n\n        // Don't allow injected connector to connect if no target is set and it hasn't already connected\n        // (e.g. flag in storage is not set). This prevents a targetless injected connector from connecting\n        // automatically whenever there is a targeted connector configured.\n        if (!parameters.target) {\n          const connected = await config.storage?.getItem('injected.connected')\n          if (!connected) return false\n        }\n\n        const provider = await this.getProvider()\n        if (!provider) {\n          if (\n            unstable_shimAsyncInject !== undefined &&\n            unstable_shimAsyncInject !== false\n          ) {\n            // If no provider is found, check for async injection\n            // https://github.com/wevm/references/issues/167\n            // https://github.com/MetaMask/detect-provider\n            const handleEthereum = async () => {\n              if (typeof window !== 'undefined')\n                window.removeEventListener(\n                  'ethereum#initialized',\n                  handleEthereum,\n                )\n              const provider = await this.getProvider()\n              return !!provider\n            }\n            const timeout =\n              typeof unstable_shimAsyncInject === 'number'\n                ? unstable_shimAsyncInject\n                : 1_000\n            const res = await Promise.race([\n              ...(typeof window !== 'undefined'\n                ? [\n                    new Promise<boolean>((resolve) =>\n                      window.addEventListener(\n                        'ethereum#initialized',\n                        () => resolve(handleEthereum()),\n                        { once: true },\n                      ),\n                    ),\n                  ]\n                : []),\n              new Promise<boolean>((resolve) =>\n                setTimeout(() => resolve(handleEthereum()), timeout),\n              ),\n            ])\n            if (res) return true\n          }\n\n          throw new ProviderNotFoundError()\n        }\n\n        // Use retry strategy as some injected wallets (e.g. MetaMask) fail to\n        // immediately resolve JSON-RPC requests on page load.\n        const accounts = await withRetry(() => this.getAccounts())\n        return !!accounts.length\n      } catch {\n        return false\n      }\n    },\n    async switchChain({ addEthereumChainParameter, chainId }) {\n      const provider = await this.getProvider()\n      if (!provider) throw new ProviderNotFoundError()\n\n      const chain = config.chains.find((x) => x.id === chainId)\n      if (!chain) throw new SwitchChainError(new ChainNotConfiguredError())\n\n      const promise = new Promise<void>((resolve) => {\n        const listener = ((data) => {\n          if ('chainId' in data && data.chainId === chainId) {\n            config.emitter.off('change', listener)\n            resolve()\n          }\n        }) satisfies Parameters<typeof config.emitter.on>[1]\n        config.emitter.on('change', listener)\n      })\n\n      try {\n        await Promise.all([\n          provider\n            .request({\n              method: 'wallet_switchEthereumChain',\n              params: [{ chainId: numberToHex(chainId) }],\n            })\n            // During `'wallet_switchEthereumChain'`, MetaMask makes a `'net_version'` RPC call to the target chain.\n            // If this request fails, MetaMask does not emit the `'chainChanged'` event, but will still switch the chain.\n            // To counter this behavior, we request and emit the current chain ID to confirm the chain switch either via\n            // this callback or an externally emitted `'chainChanged'` event.\n            // https://github.com/MetaMask/metamask-extension/issues/24247\n            .then(async () => {\n              const currentChainId = await this.getChainId()\n              if (currentChainId === chainId)\n                config.emitter.emit('change', { chainId })\n            }),\n          promise,\n        ])\n        return chain\n      } catch (err) {\n        const error = err as RpcError\n\n        // Indicates chain is not added to provider\n        if (\n          error.code === 4902 ||\n          // Unwrapping for MetaMask Mobile\n          // https://github.com/MetaMask/metamask-mobile/issues/2944#issuecomment-976988719\n          (error as ProviderRpcError<{ originalError?: { code: number } }>)\n            ?.data?.originalError?.code === 4902\n        ) {\n          try {\n            const { default: blockExplorer, ...blockExplorers } =\n              chain.blockExplorers ?? {}\n            let blockExplorerUrls: string[] | undefined\n            if (addEthereumChainParameter?.blockExplorerUrls)\n              blockExplorerUrls = addEthereumChainParameter.blockExplorerUrls\n            else if (blockExplorer)\n              blockExplorerUrls = [\n                blockExplorer.url,\n                ...Object.values(blockExplorers).map((x) => x.url),\n              ]\n\n            let rpcUrls: readonly string[]\n            if (addEthereumChainParameter?.rpcUrls?.length)\n              rpcUrls = addEthereumChainParameter.rpcUrls\n            else rpcUrls = [chain.rpcUrls.default?.http[0] ?? '']\n\n            const addEthereumChain = {\n              blockExplorerUrls,\n              chainId: numberToHex(chainId),\n              chainName: addEthereumChainParameter?.chainName ?? chain.name,\n              iconUrls: addEthereumChainParameter?.iconUrls,\n              nativeCurrency:\n                addEthereumChainParameter?.nativeCurrency ??\n                chain.nativeCurrency,\n              rpcUrls,\n            } satisfies AddEthereumChainParameter\n\n            await Promise.all([\n              provider\n                .request({\n                  method: 'wallet_addEthereumChain',\n                  params: [addEthereumChain],\n                })\n                .then(async () => {\n                  const currentChainId = await this.getChainId()\n                  if (currentChainId === chainId)\n                    config.emitter.emit('change', { chainId })\n                  else\n                    throw new UserRejectedRequestError(\n                      new Error('User rejected switch after adding network.'),\n                    )\n                }),\n              promise,\n            ])\n\n            return chain\n          } catch (error) {\n            throw new UserRejectedRequestError(error as Error)\n          }\n        }\n\n        if (error.code === UserRejectedRequestError.code)\n          throw new UserRejectedRequestError(error)\n        throw new SwitchChainError(error)\n      }\n    },\n    async onAccountsChanged(accounts) {\n      // Disconnect if there are no accounts\n      if (accounts.length === 0) this.onDisconnect()\n      // Connect if emitter is listening for connect event (e.g. is disconnected and connects through wallet interface)\n      else if (config.emitter.listenerCount('connect')) {\n        const chainId = (await this.getChainId()).toString()\n        this.onConnect({ chainId })\n        // Remove disconnected shim if it exists\n        if (shimDisconnect)\n          await config.storage?.removeItem(`${this.id}.disconnected`)\n      }\n      // Regular change event\n      else\n        config.emitter.emit('change', {\n          accounts: accounts.map((x) => getAddress(x)),\n        })\n    },\n    onChainChanged(chain) {\n      const chainId = Number(chain)\n      config.emitter.emit('change', { chainId })\n    },\n    async onConnect(connectInfo) {\n      const accounts = await this.getAccounts()\n      if (accounts.length === 0) return\n\n      const chainId = Number(connectInfo.chainId)\n      config.emitter.emit('connect', { accounts, chainId })\n\n      // Manage EIP-1193 event listeners\n      const provider = await this.getProvider()\n      if (provider) {\n        if (connect) {\n          provider.removeListener('connect', connect)\n          connect = undefined\n        }\n        if (!accountsChanged) {\n          accountsChanged = this.onAccountsChanged.bind(this)\n          provider.on('accountsChanged', accountsChanged)\n        }\n        if (!chainChanged) {\n          chainChanged = this.onChainChanged.bind(this)\n          provider.on('chainChanged', chainChanged)\n        }\n        if (!disconnect) {\n          disconnect = this.onDisconnect.bind(this)\n          provider.on('disconnect', disconnect)\n        }\n      }\n    },\n    async onDisconnect(error) {\n      const provider = await this.getProvider()\n\n      // If MetaMask emits a `code: 1013` error, wait for reconnection before disconnecting\n      // https://github.com/MetaMask/providers/pull/120\n      if (error && (error as RpcError<1013>).code === 1013) {\n        if (provider && !!(await this.getAccounts()).length) return\n      }\n\n      // No need to remove `${this.id}.disconnected` from storage because `onDisconnect` is typically\n      // only called when the wallet is disconnected through the wallet's interface, meaning the wallet\n      // actually disconnected and we don't need to simulate it.\n      config.emitter.emit('disconnect')\n\n      // Manage EIP-1193 event listeners\n      if (provider) {\n        if (chainChanged) {\n          provider.removeListener('chainChanged', chainChanged)\n          chainChanged = undefined\n        }\n        if (disconnect) {\n          provider.removeListener('disconnect', disconnect)\n          disconnect = undefined\n        }\n        if (!connect) {\n          connect = this.onConnect.bind(this)\n          provider.on('connect', connect)\n        }\n      }\n    },\n  }))\n}\n\nconst targetMap = {\n  coinbaseWallet: {\n    id: 'coinbaseWallet',\n    name: 'Coinbase Wallet',\n    provider(window) {\n      if (window?.coinbaseWalletExtension) return window.coinbaseWalletExtension\n      return findProvider(window, 'isCoinbaseWallet')\n    },\n  },\n  metaMask: {\n    id: 'metaMask',\n    name: 'MetaMask',\n    provider(window) {\n      return findProvider(window, (provider) => {\n        if (!provider.isMetaMask) return false\n        // Brave tries to make itself look like MetaMask\n        // Could also try RPC `web3_clientVersion` if following is unreliable\n        if (provider.isBraveWallet && !provider._events && !provider._state)\n          return false\n        // Other wallets that try to look like MetaMask\n        const flags = [\n          'isApexWallet',\n          'isAvalanche',\n          'isBitKeep',\n          'isBlockWallet',\n          'isKuCoinWallet',\n          'isMathWallet',\n          'isOkxWallet',\n          'isOKExWallet',\n          'isOneInchIOSWallet',\n          'isOneInchAndroidWallet',\n          'isOpera',\n          'isPhantom',\n          'isPortal',\n          'isRabby',\n          'isTokenPocket',\n          'isTokenary',\n          'isUniswapWallet',\n          'isZerion',\n        ] satisfies WalletProviderFlags[]\n        for (const flag of flags) if (provider[flag]) return false\n        return true\n      })\n    },\n  },\n  phantom: {\n    id: 'phantom',\n    name: 'Phantom',\n    provider(window) {\n      if (window?.phantom?.ethereum) return window.phantom?.ethereum\n      return findProvider(window, 'isPhantom')\n    },\n  },\n} as const satisfies TargetMap\n\ntype TargetMap = { [_ in TargetId]?: Target | undefined }\n\ntype Target = {\n  icon?: string | undefined\n  id: string\n  name: string\n  provider:\n    | WalletProviderFlags\n    | WalletProvider\n    | ((window?: Window | undefined) => WalletProvider | undefined)\n}\n\n/** @deprecated */\ntype TargetId = Compute<WalletProviderFlags> extends `is${infer name}`\n  ? name extends `${infer char}${infer rest}`\n    ? `${Lowercase<char>}${rest}`\n    : never\n  : never\n\n/**\n * @deprecated As of 2024/10/16, we are no longer accepting new provider flags as EIP-6963 should be used instead.\n */\ntype WalletProviderFlags =\n  | 'isApexWallet'\n  | 'isAvalanche'\n  | 'isBackpack'\n  | 'isBifrost'\n  | 'isBitKeep'\n  | 'isBitski'\n  | 'isBlockWallet'\n  | 'isBraveWallet'\n  | 'isCoinbaseWallet'\n  | 'isDawn'\n  | 'isEnkrypt'\n  | 'isExodus'\n  | 'isFrame'\n  | 'isFrontier'\n  | 'isGamestop'\n  | 'isHyperPay'\n  | 'isImToken'\n  | 'isKuCoinWallet'\n  | 'isMathWallet'\n  | 'isMetaMask'\n  | 'isOkxWallet'\n  | 'isOKExWallet'\n  | 'isOneInchAndroidWallet'\n  | 'isOneInchIOSWallet'\n  | 'isOpera'\n  | 'isPhantom'\n  | 'isPortal'\n  | 'isRabby'\n  | 'isRainbow'\n  | 'isStatus'\n  | 'isTally'\n  | 'isTokenPocket'\n  | 'isTokenary'\n  | 'isTrust'\n  | 'isTrustWallet'\n  | 'isUniswapWallet'\n  | 'isXDEFI'\n  | 'isZerion'\n\ntype WalletProvider = Compute<\n  EIP1193Provider & {\n    [key in WalletProviderFlags]?: true | undefined\n  } & {\n    providers?: WalletProvider[] | undefined\n    /** Only exists in MetaMask as of 2022/04/03 */\n    _events?: { connect?: (() => void) | undefined } | undefined\n    /** Only exists in MetaMask as of 2022/04/03 */\n    _state?:\n      | {\n          accounts?: string[]\n          initialized?: boolean\n          isConnected?: boolean\n          isPermanentlyDisconnected?: boolean\n          isUnlocked?: boolean\n        }\n      | undefined\n  }\n>\n\ntype Window = {\n  coinbaseWalletExtension?: WalletProvider | undefined\n  ethereum?: WalletProvider | undefined\n  phantom?: { ethereum: WalletProvider } | undefined\n}\n\nfunction findProvider(\n  window: globalThis.Window | Window | undefined,\n  select?: WalletProviderFlags | ((provider: WalletProvider) => boolean),\n) {\n  function isProvider(provider: WalletProvider) {\n    if (typeof select === 'function') return select(provider)\n    if (typeof select === 'string') return provider[select]\n    return true\n  }\n\n  const ethereum = (window as Window).ethereum\n  if (ethereum?.providers)\n    return ethereum.providers.find((provider) => isProvider(provider))\n  if (ethereum && isProvider(ethereum)) return ethereum\n  return undefined\n}\n", "import {\n  type Address,\n  type EIP1193RequestFn,\n  type Hex,\n  RpcRequestError,\n  SwitchChainError,\n  type Transport,\n  UserRejectedRequestError,\n  type WalletCallReceipt,\n  type WalletGetCallsStatusReturnType,\n  type WalletRpcSchema,\n  custom,\n  fromHex,\n  getAddress,\n  keccak256,\n  numberToHex,\n  stringToHex,\n} from 'viem'\nimport { rpc } from 'viem/utils'\n\nimport {\n  ChainNotConfiguredError,\n  ConnectorNotConnectedError,\n} from '../errors/config.js'\nimport { createConnector } from './createConnector.js'\n\nexport type MockParameters = {\n  accounts: readonly [Address, ...Address[]]\n  features?:\n    | {\n        defaultConnected?: boolean | undefined\n        connectError?: boolean | Error | undefined\n        switchChainError?: boolean | Error | undefined\n        signMessageError?: boolean | Error | undefined\n        signTypedDataError?: boolean | Error | undefined\n        reconnect?: boolean | undefined\n        watchAssetError?: boolean | Error | undefined\n      }\n    | undefined\n}\n\nmock.type = 'mock' as const\nexport function mock(parameters: MockParameters) {\n  const transactionCache = new Map<Hex, Hex[]>()\n  const features =\n    parameters.features ??\n    ({ defaultConnected: false } satisfies MockParameters['features'])\n\n  type Provider = ReturnType<\n    Transport<'custom', unknown, EIP1193RequestFn<WalletRpcSchema>>\n  >\n  type Properties = {\n    connect(parameters?: {\n      chainId?: number | undefined\n      isReconnecting?: boolean | undefined\n      foo?: string | undefined\n    }): Promise<{\n      accounts: readonly Address[]\n      chainId: number\n    }>\n  }\n  let connected = features.defaultConnected\n  let connectedChainId: number\n\n  return createConnector<Provider, Properties>((config) => ({\n    id: 'mock',\n    name: 'Mock Connector',\n    type: mock.type,\n    async setup() {\n      connectedChainId = config.chains[0].id\n    },\n    async connect({ chainId } = {}) {\n      if (features.connectError) {\n        if (typeof features.connectError === 'boolean')\n          throw new UserRejectedRequestError(new Error('Failed to connect.'))\n        throw features.connectError\n      }\n\n      const provider = await this.getProvider()\n      const accounts = await provider.request({\n        method: 'eth_requestAccounts',\n      })\n\n      let currentChainId = await this.getChainId()\n      if (chainId && currentChainId !== chainId) {\n        const chain = await this.switchChain!({ chainId })\n        currentChainId = chain.id\n      }\n\n      connected = true\n\n      return {\n        accounts: accounts.map((x) => getAddress(x)),\n        chainId: currentChainId,\n      }\n    },\n    async disconnect() {\n      connected = false\n    },\n    async getAccounts() {\n      if (!connected) throw new ConnectorNotConnectedError()\n      const provider = await this.getProvider()\n      const accounts = await provider.request({ method: 'eth_accounts' })\n      return accounts.map((x) => getAddress(x))\n    },\n    async getChainId() {\n      const provider = await this.getProvider()\n      const hexChainId = await provider.request({ method: 'eth_chainId' })\n      return fromHex(hexChainId, 'number')\n    },\n    async isAuthorized() {\n      if (!features.reconnect) return false\n      if (!connected) return false\n      const accounts = await this.getAccounts()\n      return !!accounts.length\n    },\n    async switchChain({ chainId }) {\n      const provider = await this.getProvider()\n      const chain = config.chains.find((x) => x.id === chainId)\n      if (!chain) throw new SwitchChainError(new ChainNotConfiguredError())\n\n      await provider.request({\n        method: 'wallet_switchEthereumChain',\n        params: [{ chainId: numberToHex(chainId) }],\n      })\n      return chain\n    },\n    onAccountsChanged(accounts) {\n      if (accounts.length === 0) this.onDisconnect()\n      else\n        config.emitter.emit('change', {\n          accounts: accounts.map((x) => getAddress(x)),\n        })\n    },\n    onChainChanged(chain) {\n      const chainId = Number(chain)\n      config.emitter.emit('change', { chainId })\n    },\n    async onDisconnect(_error) {\n      config.emitter.emit('disconnect')\n      connected = false\n    },\n    async getProvider({ chainId } = {}) {\n      const chain =\n        config.chains.find((x) => x.id === chainId) ?? config.chains[0]\n      const url = chain.rpcUrls.default.http[0]!\n\n      const request: EIP1193RequestFn = async ({ method, params }) => {\n        // eth methods\n        if (method === 'eth_chainId') return numberToHex(connectedChainId)\n        if (method === 'eth_requestAccounts') return parameters.accounts\n        if (method === 'eth_signTypedData_v4')\n          if (features.signTypedDataError) {\n            if (typeof features.signTypedDataError === 'boolean')\n              throw new UserRejectedRequestError(\n                new Error('Failed to sign typed data.'),\n              )\n            throw features.signTypedDataError\n          }\n\n        // wallet methods\n        if (method === 'wallet_switchEthereumChain') {\n          if (features.switchChainError) {\n            if (typeof features.switchChainError === 'boolean')\n              throw new UserRejectedRequestError(\n                new Error('Failed to switch chain.'),\n              )\n            throw features.switchChainError\n          }\n          type Params = [{ chainId: Hex }]\n          connectedChainId = fromHex((params as Params)[0].chainId, 'number')\n          this.onChainChanged(connectedChainId.toString())\n          return\n        }\n\n        if (method === 'wallet_watchAsset') {\n          if (features.watchAssetError) {\n            if (typeof features.watchAssetError === 'boolean')\n              throw new UserRejectedRequestError(\n                new Error('Failed to switch chain.'),\n              )\n            throw features.watchAssetError\n          }\n          return connected\n        }\n\n        if (method === 'wallet_getCapabilities')\n          return {\n            '0x2105': {\n              paymasterService: {\n                supported:\n                  (params as [Hex])[0] ===\n                  '******************************************',\n              },\n              sessionKeys: {\n                supported: true,\n              },\n            },\n            '0x14A34': {\n              paymasterService: {\n                supported:\n                  (params as [Hex])[0] ===\n                  '******************************************',\n              },\n            },\n          }\n\n        if (method === 'wallet_sendCalls') {\n          const hashes = []\n          const calls = (params as any)[0].calls\n          for (const call of calls) {\n            const { result, error } = await rpc.http(url, {\n              body: {\n                method: 'eth_sendTransaction',\n                params: [call],\n              },\n            })\n            if (error)\n              throw new RpcRequestError({\n                body: { method, params },\n                error,\n                url,\n              })\n            hashes.push(result)\n          }\n          const id = keccak256(stringToHex(JSON.stringify(calls)))\n          transactionCache.set(id, hashes)\n          return { id }\n        }\n\n        if (method === 'wallet_getCallsStatus') {\n          const hashes = transactionCache.get((params as any)[0])\n          if (!hashes)\n            return {\n              atomic: false,\n              chainId: '0x1',\n              id: (params as any)[0],\n              status: 100,\n              receipts: [],\n              version: '2.0.0',\n            } satisfies WalletGetCallsStatusReturnType\n\n          const receipts = await Promise.all(\n            hashes.map(async (hash) => {\n              const { result, error } = await rpc.http(url, {\n                body: {\n                  method: 'eth_getTransactionReceipt',\n                  params: [hash],\n                  id: 0,\n                },\n              })\n              if (error)\n                throw new RpcRequestError({\n                  body: { method, params },\n                  error,\n                  url,\n                })\n              if (!result) return null\n              return {\n                blockHash: result.blockHash,\n                blockNumber: result.blockNumber,\n                gasUsed: result.gasUsed,\n                logs: result.logs,\n                status: result.status,\n                transactionHash: result.transactionHash,\n              } satisfies WalletCallReceipt\n            }),\n          )\n          const receipts_ = receipts.filter((x) => x !== null)\n          if (receipts_.length === 0)\n            return {\n              atomic: false,\n              chainId: '0x1',\n              id: (params as any)[0],\n              status: 100,\n              receipts: [],\n              version: '2.0.0',\n            } satisfies WalletGetCallsStatusReturnType\n          return {\n            atomic: false,\n            chainId: '0x1',\n            id: (params as any)[0],\n            status: 200,\n            receipts: receipts_,\n            version: '2.0.0',\n          } satisfies WalletGetCallsStatusReturnType\n        }\n\n        if (method === 'wallet_showCallsStatus') return\n\n        // other methods\n        if (method === 'personal_sign') {\n          if (features.signMessageError) {\n            if (typeof features.signMessageError === 'boolean')\n              throw new UserRejectedRequestError(\n                new Error('Failed to sign message.'),\n              )\n            throw features.signMessageError\n          }\n          // Change `personal_sign` to `eth_sign` and swap params\n          method = 'eth_sign'\n          type Params = [data: Hex, address: Address]\n          params = [(params as Params)[1], (params as Params)[0]]\n        }\n\n        const body = { method, params }\n        const { error, result } = await rpc.http(url, { body })\n        if (error) throw new RpcRequestError({ body, error, url })\n\n        return result\n      }\n      return custom({ request })({ retryCount: 0 })\n    },\n  }))\n}\n", "import type { EIP1193Provider } from './register.js'\nimport type {\n  EIP6963AnnounceProviderEvent,\n  EIP6963ProviderDetail,\n} from './types.js'\n\n////////////////////////////////////////////////////////////////////////////\n// Announce Provider\n\nexport type AnnounceProviderParameters = EIP6963ProviderDetail<\n  EIP1193Provider,\n  string\n>\nexport type AnnounceProviderReturnType = () => void\n\n/**\n * Announces an EIP-1193 Provider.\n */\nexport function announceProvider(\n  detail: AnnounceProviderParameters,\n): AnnounceProviderReturnType {\n  const event: CustomEvent<EIP6963ProviderDetail> = new CustomEvent(\n    'eip6963:announceProvider',\n    { detail: Object.freeze(detail) },\n  )\n\n  window.dispatchEvent(event)\n\n  const handler = () => window.dispatchEvent(event)\n  window.addEventListener('eip6963:requestProvider', handler)\n  return () => window.removeEventListener('eip6963:requestProvider', handler)\n}\n\n////////////////////////////////////////////////////////////////////////////\n// Request Providers\n\nexport type RequestProvidersParameters = (\n  providerDetail: EIP6963ProviderDetail,\n) => void\nexport type RequestProvidersReturnType = (() => void) | undefined\n\n/**\n * Watches for EIP-1193 Providers to be announced.\n */\nexport function requestProviders(\n  listener: RequestProvidersParameters,\n): RequestProvidersReturnType {\n  if (typeof window === 'undefined') return\n  const handler = (event: EIP6963AnnounceProviderEvent) =>\n    listener(event.detail)\n\n  window.addEventListener('eip6963:announceProvider', handler)\n\n  window.dispatchEvent(new CustomEvent('eip6963:requestProvider'))\n\n  return () => window.removeEventListener('eip6963:announceProvider', handler)\n}\n", "import type { Rdns } from './register.js'\nimport type { EIP6963ProviderDetail } from './types.js'\nimport { requestProviders } from './utils.js'\n\nexport type Listener = (\n  providerDetails: readonly EIP6963ProviderDetail[],\n  meta?:\n    | {\n        added?: readonly EIP6963ProviderDetail[] | undefined\n        removed?: readonly EIP6963ProviderDetail[] | undefined\n      }\n    | undefined,\n) => void\n\nexport type Store = {\n  /**\n   * Clears the store, including all provider details.\n   */\n  clear(): void\n  /**\n   * Destroys the store, including all provider details and listeners.\n   */\n  destroy(): void\n  /**\n   * Finds a provider detail by its RDNS (Reverse Domain Name Identifier).\n   */\n  findProvider(args: { rdns: Rdns }): EIP6963ProviderDetail | undefined\n  /**\n   * Returns all provider details that have been emitted.\n   */\n  getProviders(): readonly EIP6963ProviderDetail[]\n  /**\n   * Resets the store, and emits an event to request provider details.\n   */\n  reset(): void\n  /**\n   * Subscribes to emitted provider details.\n   */\n  subscribe(\n    listener: Listener,\n    args?: { emitImmediately?: boolean | undefined } | undefined,\n  ): () => void\n\n  /**\n   * @internal\n   * Current state of listening listeners.\n   */\n  _listeners(): Set<Listener>\n}\n\nexport function createStore(): Store {\n  const listeners: Set<Listener> = new Set()\n  let providerDetails: readonly EIP6963ProviderDetail[] = []\n\n  const request = () =>\n    requestProviders((providerDetail) => {\n      if (\n        providerDetails.some(\n          ({ info }) => info.uuid === providerDetail.info.uuid,\n        )\n      )\n        return\n\n      providerDetails = [...providerDetails, providerDetail]\n      listeners.forEach((listener) =>\n        listener(providerDetails, { added: [providerDetail] }),\n      )\n    })\n  let unwatch = request()\n\n  return {\n    _listeners() {\n      return listeners\n    },\n    clear() {\n      listeners.forEach((listener) =>\n        listener([], { removed: [...providerDetails] }),\n      )\n      providerDetails = []\n    },\n    destroy() {\n      this.clear()\n      listeners.clear()\n      unwatch?.()\n    },\n    findProvider({ rdns }) {\n      return providerDetails.find(\n        (providerDetail) => providerDetail.info.rdns === rdns,\n      )\n    },\n    getProviders() {\n      return providerDetails\n    },\n    reset() {\n      this.clear()\n      unwatch?.()\n      unwatch = request()\n    },\n    subscribe(listener, { emitImmediately } = {}) {\n      listeners.add(listener)\n      if (emitImmediately) listener(providerDetails, { added: providerDetails })\n      return () => listeners.delete(listener)\n    },\n  }\n}\n", "const reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...a) => api.dispatch(...a), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : (import.meta.env ? import.meta.env.MODE : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extensionConnector) {\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const action = nameOrAction === void 0 ? { type: anonymousActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...a) => {\n      if ((import.meta.env ? import.meta.env.MODE : void 0) !== \"production\" && a[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...a);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, f) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) f(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nconst combine = (initialState, create) => (...a) => Object.assign({}, initialState, create(...a));\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(\n      name,\n      JSON.stringify(newValue, options == null ? void 0 : options.replacer)\n    ),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persistImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            return [\n              true,\n              options.migrate(\n                deserializedStorageValue.state,\n                deserializedStorageValue.version\n              )\n            ];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persist = persistImpl;\n\nexport { combine, createJSONStorage, devtools, persist, redux, subscribeWithSelector };\n", "const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n", "import { EventEmitter } from 'eventemitter3'\n\ntype EventMap = Record<string, object | never>\ntype EventKey<eventMap extends EventMap> = string & keyof eventMap\ntype EventFn<parameters extends unknown[] = any[]> = (\n  ...parameters: parameters\n) => void\nexport type EventData<\n  eventMap extends EventMap,\n  eventName extends keyof eventMap,\n> = (eventMap[eventName] extends [never] ? unknown : eventMap[eventName]) & {\n  uid: string\n}\n\nexport class Emitter<eventMap extends EventMap> {\n  _emitter = new EventEmitter()\n\n  constructor(public uid: string) {}\n\n  on<key extends EventKey<eventMap>>(\n    eventName: key,\n    fn: EventFn<\n      eventMap[key] extends [never]\n        ? [{ uid: string }]\n        : [data: eventMap[key] & { uid: string }]\n    >,\n  ) {\n    this._emitter.on(eventName, fn as EventFn)\n  }\n\n  once<key extends EventKey<eventMap>>(\n    eventName: key,\n    fn: EventFn<\n      eventMap[key] extends [never]\n        ? [{ uid: string }]\n        : [data: eventMap[key] & { uid: string }]\n    >,\n  ) {\n    this._emitter.once(eventName, fn as EventFn)\n  }\n\n  off<key extends EventKey<eventMap>>(\n    eventName: key,\n    fn: EventFn<\n      eventMap[key] extends [never]\n        ? [{ uid: string }]\n        : [data: eventMap[key] & { uid: string }]\n    >,\n  ) {\n    this._emitter.off(eventName, fn as EventFn)\n  }\n\n  emit<key extends EventKey<eventMap>>(\n    eventName: key,\n    ...params: eventMap[key] extends [never] ? [] : [data: eventMap[key]]\n  ) {\n    const data = params[0]\n    this._emitter.emit(eventName, { uid: this.uid, ...data })\n  }\n\n  listenerCount<key extends EventKey<eventMap>>(eventName: key) {\n    return this._emitter.listenerCount(eventName)\n  }\n}\n\nexport function createEmitter<eventMap extends EventMap>(uid: string) {\n  return new Emitter<eventMap>(uid)\n}\n", "type Reviver = (key: string, value: any) => any\n\nexport function deserialize<type>(value: string, reviver?: Reviver): type {\n  return JSON.parse(value, (key, value_) => {\n    let value = value_\n    if (value?.__type === 'bigint') value = BigInt(value.value)\n    if (value?.__type === 'Map') value = new Map(value.value)\n    return reviver?.(key, value) ?? value\n  })\n}\n", "/**\n * Get the reference key for the circular value\n *\n * @param keys the keys to build the reference key from\n * @param cutoff the maximum number of keys to include\n * @returns the reference key\n */\nfunction getReferenceKey(keys: string[], cutoff: number) {\n  return keys.slice(0, cutoff).join('.') || '.'\n}\n\n/**\n * Faster `Array.prototype.indexOf` implementation build for slicing / splicing\n *\n * @param array the array to match the value in\n * @param value the value to match\n * @returns the matching index, or -1\n */\nfunction getCutoff(array: any[], value: any) {\n  const { length } = array\n\n  for (let index = 0; index < length; ++index) {\n    if (array[index] === value) {\n      return index + 1\n    }\n  }\n\n  return 0\n}\n\ntype StandardReplacer = (key: string, value: any) => any\ntype CircularReplacer = (key: string, value: any, referenceKey: string) => any\n\n/**\n * Create a replacer method that handles circular values\n *\n * @param [replacer] a custom replacer to use for non-circular values\n * @param [circularReplacer] a custom replacer to use for circular methods\n * @returns the value to stringify\n */\nfunction createReplacer(\n  replacer?: StandardReplacer | null | undefined,\n  circularReplacer?: CircularReplacer | null | undefined,\n): StandardReplacer {\n  const hasReplacer = typeof replacer === 'function'\n  const hasCircularReplacer = typeof circularReplacer === 'function'\n\n  const cache: any[] = []\n  const keys: string[] = []\n\n  return function replace(this: any, key: string, value: any) {\n    if (typeof value === 'object') {\n      if (cache.length) {\n        const thisCutoff = getCutoff(cache, this)\n\n        if (thisCutoff === 0) {\n          cache[cache.length] = this\n        } else {\n          cache.splice(thisCutoff)\n          keys.splice(thisCutoff)\n        }\n\n        keys[keys.length] = key\n\n        const valueCutoff = getCutoff(cache, value)\n\n        if (valueCutoff !== 0) {\n          return hasCircularReplacer\n            ? circularReplacer.call(\n                this,\n                key,\n                value,\n                getReferenceKey(keys, valueCutoff),\n              )\n            : `[ref=${getReferenceKey(keys, valueCutoff)}]`\n        }\n      } else {\n        cache[0] = value\n        keys[0] = key\n      }\n    }\n\n    return hasReplacer ? replacer.call(this, key, value) : value\n  }\n}\n\n/**\n * Stringifier that handles circular values\n *\n * Forked from https://github.com/planttheidea/fast-stringify\n *\n * @param value to stringify\n * @param [replacer] a custom replacer function for handling standard values\n * @param [indent] the number of spaces to indent the output by\n * @param [circularReplacer] a custom replacer function for handling circular values\n * @returns the stringified output\n */\nexport function serialize(\n  value: any,\n  replacer?: StandardReplacer | null | undefined,\n  indent?: number | null | undefined,\n  circularReplacer?: CircularReplacer | null | undefined,\n) {\n  return JSON.stringify(\n    value,\n    createReplacer((key, value_) => {\n      let value = value_\n      if (typeof value === 'bigint')\n        value = { __type: 'bigint', value: value_.toString() }\n      if (value instanceof Map)\n        value = { __type: 'Map', value: Array.from(value_.entries()) }\n      return replacer?.(key, value) ?? value\n    }, circularReplacer),\n    indent ?? undefined,\n  )\n}\n", "import type { PartializedState } from './createConfig.js'\nimport type { Compute } from './types/utils.js'\nimport { deserialize as deserialize_ } from './utils/deserialize.js'\nimport { serialize as serialize_ } from './utils/serialize.js'\n\n// key-values for loose autocomplete and typing\nexport type StorageItemMap = {\n  recentConnectorId: string\n  state: PartializedState\n}\n\nexport type Storage<\n  itemMap extends Record<string, unknown> = Record<string, unknown>,\n  ///\n  storageItemMap extends StorageItemMap = StorageItemMap & itemMap,\n> = {\n  key: string\n  getItem<\n    key extends keyof storageItemMap,\n    value extends storageItemMap[key],\n    defaultValue extends value | null | undefined,\n  >(\n    key: key,\n    defaultValue?: defaultValue | undefined,\n  ):\n    | (defaultValue extends null ? value | null : value)\n    | Promise<defaultValue extends null ? value | null : value>\n  setItem<\n    key extends keyof storageItemMap,\n    value extends storageItemMap[key] | null,\n  >(key: key, value: value): void | Promise<void>\n  removeItem(key: keyof storageItemMap): void | Promise<void>\n}\n\nexport type BaseStorage = {\n  getItem(\n    key: string,\n  ): string | null | undefined | Promise<string | null | undefined>\n  setItem(key: string, value: string): void | Promise<void>\n  removeItem(key: string): void | Promise<void>\n}\n\nexport type CreateStorageParameters = {\n  deserialize?: (<type>(value: string) => type | unknown) | undefined\n  key?: string | undefined\n  serialize?: (<type>(value: type | any) => string) | undefined\n  storage?: Compute<BaseStorage> | undefined\n}\n\nexport function createStorage<\n  itemMap extends Record<string, unknown> = Record<string, unknown>,\n  storageItemMap extends StorageItemMap = StorageItemMap & itemMap,\n>(parameters: CreateStorageParameters): Compute<Storage<storageItemMap>> {\n  const {\n    deserialize = deserialize_,\n    key: prefix = 'wagmi',\n    serialize = serialize_,\n    storage = noopStorage,\n  } = parameters\n\n  function unwrap<type>(value: type): type | Promise<type> {\n    if (value instanceof Promise) return value.then((x) => x).catch(() => null)\n    return value\n  }\n\n  return {\n    ...storage,\n    key: prefix,\n    async getItem(key, defaultValue) {\n      const value = storage.getItem(`${prefix}.${key as string}`)\n      const unwrapped = await unwrap(value)\n      if (unwrapped) return deserialize(unwrapped) ?? null\n      return (defaultValue ?? null) as any\n    },\n    async setItem(key, value) {\n      const storageKey = `${prefix}.${key as string}`\n      if (value === null) await unwrap(storage.removeItem(storageKey))\n      else await unwrap(storage.setItem(storageKey, serialize(value)))\n    },\n    async removeItem(key) {\n      await unwrap(storage.removeItem(`${prefix}.${key as string}`))\n    },\n  }\n}\n\nexport const noopStorage = {\n  getItem: () => null,\n  setItem: () => {},\n  removeItem: () => {},\n} satisfies BaseStorage\n\nexport function getDefaultStorage() {\n  const storage = (() => {\n    if (typeof window !== 'undefined' && window.localStorage)\n      return window.localStorage\n    return noopStorage\n  })()\n  return {\n    getItem(key) {\n      return storage.getItem(key)\n    },\n    removeItem(key) {\n      storage.removeItem(key)\n    },\n    setItem(key, value) {\n      try {\n        storage.setItem(key, value)\n        // silence errors by default (QuotaExceededError, SecurityError, etc.)\n      } catch {}\n    },\n  } satisfies BaseStorage\n}\n", "const size = 256\nlet index = size\nlet buffer: string\n\nexport function uid(length = 11) {\n  if (!buffer || index + length > size * 2) {\n    buffer = ''\n    index = 0\n    for (let i = 0; i < size; i++) {\n      buffer += ((256 + Math.random() * 256) | 0).toString(16).substring(1)\n    }\n  }\n  return buffer.substring(index, index++ + length)\n}\n", "import {\n  type EIP6963ProviderDetail,\n  type Store as MipdStore,\n  createStore as createMipd,\n} from 'mipd'\nimport {\n  type Address,\n  type Chain,\n  type Client,\n  type EIP1193RequestFn,\n  createClient,\n  type ClientConfig as viem_ClientConfig,\n  type Transport as viem_Transport,\n} from 'viem'\nimport { persist, subscribeWithSelector } from 'zustand/middleware'\nimport { type Mutate, type StoreApi, createStore } from 'zustand/vanilla'\n\nimport type {\n  ConnectorEventMap,\n  CreateConnectorFn,\n} from './connectors/createConnector.js'\nimport { injected } from './connectors/injected.js'\nimport { type Emitter, type EventData, createEmitter } from './createEmitter.js'\nimport {\n  type Storage,\n  createStorage,\n  getDefaultStorage,\n} from './createStorage.js'\nimport { ChainNotConfiguredError } from './errors/config.js'\nimport type {\n  Compute,\n  ExactPartial,\n  LooseOmit,\n  OneOf,\n  RemoveUndefined,\n} from './types/utils.js'\nimport { uid } from './utils/uid.js'\nimport { version } from './version.js'\n\nexport function createConfig<\n  const chains extends readonly [Chain, ...Chain[]],\n  transports extends Record<chains[number]['id'], Transport>,\n  const connectorFns extends readonly CreateConnectorFn[],\n>(\n  parameters: CreateConfigParameters<chains, transports, connectorFns>,\n): Config<chains, transports, connectorFns> {\n  const {\n    multiInjectedProviderDiscovery = true,\n    storage = createStorage({\n      storage: getDefaultStorage(),\n    }),\n    syncConnectedChain = true,\n    ssr = false,\n    ...rest\n  } = parameters\n\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n  // Set up connectors, clients, etc.\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n\n  const mipd =\n    typeof window !== 'undefined' && multiInjectedProviderDiscovery\n      ? createMipd()\n      : undefined\n\n  const chains = createStore(() => rest.chains)\n  const connectors = createStore(() => {\n    const collection = []\n    const rdnsSet = new Set<string>()\n    for (const connectorFns of rest.connectors ?? []) {\n      const connector = setup(connectorFns)\n      collection.push(connector)\n      if (!ssr && connector.rdns) {\n        const rdnsValues =\n          typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns\n        for (const rdns of rdnsValues) {\n          rdnsSet.add(rdns)\n        }\n      }\n    }\n    if (!ssr && mipd) {\n      const providers = mipd.getProviders()\n      for (const provider of providers) {\n        if (rdnsSet.has(provider.info.rdns)) continue\n        collection.push(setup(providerDetailToConnector(provider)))\n      }\n    }\n    return collection\n  })\n  function setup(connectorFn: CreateConnectorFn): Connector {\n    // Set up emitter with uid and add to connector so they are \"linked\" together.\n    const emitter = createEmitter<ConnectorEventMap>(uid())\n    const connector = {\n      ...connectorFn({\n        emitter,\n        chains: chains.getState(),\n        storage,\n        transports: rest.transports,\n      }),\n      emitter,\n      uid: emitter.uid,\n    }\n\n    // Start listening for `connect` events on connector setup\n    // This allows connectors to \"connect\" themselves without user interaction (e.g. MetaMask's \"Manually connect to current site\")\n    emitter.on('connect', connect)\n    connector.setup?.()\n\n    return connector\n  }\n  function providerDetailToConnector(providerDetail: EIP6963ProviderDetail) {\n    const { info } = providerDetail\n    const provider = providerDetail.provider as any\n    return injected({ target: { ...info, id: info.rdns, provider } })\n  }\n\n  const clients = new Map<number, Client<Transport, chains[number]>>()\n  function getClient<chainId extends chains[number]['id']>(\n    config: { chainId?: chainId | chains[number]['id'] | undefined } = {},\n  ): Client<Transport, Extract<chains[number], { id: chainId }>> {\n    const chainId = config.chainId ?? store.getState().chainId\n    const chain = chains.getState().find((x) => x.id === chainId)\n\n    // chainId specified and not configured\n    if (config.chainId && !chain) throw new ChainNotConfiguredError()\n\n    // If the target chain is not configured, use the client of the current chain.\n    type Return = Client<Transport, Extract<chains[number], { id: chainId }>>\n    {\n      const client = clients.get(store.getState().chainId)\n      if (client && !chain) return client as Return\n      if (!chain) throw new ChainNotConfiguredError()\n    }\n\n    // If a memoized client exists for a chain id, use that.\n    {\n      const client = clients.get(chainId)\n      if (client) return client as Return\n    }\n\n    let client: Client<Transport, chains[number]>\n    if (rest.client) client = rest.client({ chain })\n    else {\n      const chainId = chain.id as chains[number]['id']\n      const chainIds = chains.getState().map((x) => x.id)\n      // Grab all properties off `rest` and resolve for use in `createClient`\n      const properties: Partial<viem_ClientConfig> = {}\n      const entries = Object.entries(rest) as [keyof typeof rest, any][]\n\n      for (const [key, value] of entries) {\n        if (\n          key === 'chains' ||\n          key === 'client' ||\n          key === 'connectors' ||\n          key === 'transports'\n        )\n          continue\n\n        if (typeof value === 'object') {\n          // check if value is chainId-specific since some values can be objects\n          // e.g. { batch: { multicall: { batchSize: 1024 } } }\n          if (chainId in value) properties[key] = value[chainId]\n          else {\n            // check if value is chainId-specific, but does not have value for current chainId\n            const hasChainSpecificValue = chainIds.some((x) => x in value)\n            if (hasChainSpecificValue) continue\n            properties[key] = value\n          }\n        } else properties[key] = value\n      }\n\n      client = createClient({\n        ...properties,\n        chain,\n        batch: properties.batch ?? { multicall: true },\n        transport: (parameters) =>\n          rest.transports[chainId]({ ...parameters, connectors }),\n      })\n    }\n\n    clients.set(chainId, client)\n    return client as Return\n  }\n\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n  // Create store\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n\n  function getInitialState(): State {\n    return {\n      chainId: chains.getState()[0].id,\n      connections: new Map<string, Connection>(),\n      current: null,\n      status: 'disconnected',\n    }\n  }\n\n  let currentVersion: number\n  const prefix = '0.0.0-canary-'\n  if (version.startsWith(prefix))\n    currentVersion = Number.parseInt(version.replace(prefix, ''))\n  // use package major version to version store\n  else currentVersion = Number.parseInt(version.split('.')[0] ?? '0')\n\n  const store = createStore(\n    subscribeWithSelector(\n      // only use persist middleware if storage exists\n      storage\n        ? persist(getInitialState, {\n            migrate(persistedState, version) {\n              if (version === currentVersion) return persistedState as State\n\n              const initialState = getInitialState()\n              const chainId = validatePersistedChainId(\n                persistedState,\n                initialState.chainId,\n              )\n              return { ...initialState, chainId }\n            },\n            name: 'store',\n            partialize(state) {\n              // Only persist \"critical\" store properties to preserve storage size.\n              return {\n                connections: {\n                  __type: 'Map',\n                  value: Array.from(state.connections.entries()).map(\n                    ([key, connection]) => {\n                      const { id, name, type, uid } = connection.connector\n                      const connector = { id, name, type, uid }\n                      return [key, { ...connection, connector }]\n                    },\n                  ),\n                } as unknown as PartializedState['connections'],\n                chainId: state.chainId,\n                current: state.current,\n              } satisfies PartializedState\n            },\n            merge(persistedState, currentState) {\n              // `status` should not be persisted as it messes with reconnection\n              if (\n                typeof persistedState === 'object' &&\n                persistedState &&\n                'status' in persistedState\n              )\n                delete persistedState.status\n              // Make sure persisted `chainId` is valid\n              const chainId = validatePersistedChainId(\n                persistedState,\n                currentState.chainId,\n              )\n              return {\n                ...currentState,\n                ...(persistedState as object),\n                chainId,\n              }\n            },\n            skipHydration: ssr,\n            storage: storage as Storage<Record<string, unknown>>,\n            version: currentVersion,\n          })\n        : getInitialState,\n    ),\n  )\n  store.setState(getInitialState())\n\n  function validatePersistedChainId(\n    persistedState: unknown,\n    defaultChainId: number,\n  ) {\n    return persistedState &&\n      typeof persistedState === 'object' &&\n      'chainId' in persistedState &&\n      typeof persistedState.chainId === 'number' &&\n      chains.getState().some((x) => x.id === persistedState.chainId)\n      ? persistedState.chainId\n      : defaultChainId\n  }\n\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n  // Subscribe to changes\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n\n  // Update default chain when connector chain changes\n  if (syncConnectedChain)\n    store.subscribe(\n      ({ connections, current }) =>\n        current ? connections.get(current)?.chainId : undefined,\n      (chainId) => {\n        // If chain is not configured, then don't switch over to it.\n        const isChainConfigured = chains\n          .getState()\n          .some((x) => x.id === chainId)\n        if (!isChainConfigured) return\n\n        return store.setState((x) => ({\n          ...x,\n          chainId: chainId ?? x.chainId,\n        }))\n      },\n    )\n\n  // EIP-6963 subscribe for new wallet providers\n  mipd?.subscribe((providerDetails) => {\n    const connectorIdSet = new Set<string>()\n    const connectorRdnsSet = new Set<string>()\n    for (const connector of connectors.getState()) {\n      connectorIdSet.add(connector.id)\n      if (connector.rdns) {\n        const rdnsValues =\n          typeof connector.rdns === 'string' ? [connector.rdns] : connector.rdns\n        for (const rdns of rdnsValues) {\n          connectorRdnsSet.add(rdns)\n        }\n      }\n    }\n\n    const newConnectors: Connector[] = []\n    for (const providerDetail of providerDetails) {\n      if (connectorRdnsSet.has(providerDetail.info.rdns)) continue\n      const connector = setup(providerDetailToConnector(providerDetail))\n      if (connectorIdSet.has(connector.id)) continue\n      newConnectors.push(connector)\n    }\n\n    if (storage && !store.persist.hasHydrated()) return\n    connectors.setState((x) => [...x, ...newConnectors], true)\n  })\n\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n  // Emitter listeners\n  /////////////////////////////////////////////////////////////////////////////////////////////////\n\n  function change(data: EventData<ConnectorEventMap, 'change'>) {\n    store.setState((x) => {\n      const connection = x.connections.get(data.uid)\n      if (!connection) return x\n      return {\n        ...x,\n        connections: new Map(x.connections).set(data.uid, {\n          accounts:\n            (data.accounts as readonly [Address, ...Address[]]) ??\n            connection.accounts,\n          chainId: data.chainId ?? connection.chainId,\n          connector: connection.connector,\n        }),\n      }\n    })\n  }\n  function connect(data: EventData<ConnectorEventMap, 'connect'>) {\n    // Disable handling if reconnecting/connecting\n    if (\n      store.getState().status === 'connecting' ||\n      store.getState().status === 'reconnecting'\n    )\n      return\n\n    store.setState((x) => {\n      const connector = connectors.getState().find((x) => x.uid === data.uid)\n      if (!connector) return x\n\n      if (connector.emitter.listenerCount('connect'))\n        connector.emitter.off('connect', change)\n      if (!connector.emitter.listenerCount('change'))\n        connector.emitter.on('change', change)\n      if (!connector.emitter.listenerCount('disconnect'))\n        connector.emitter.on('disconnect', disconnect)\n\n      return {\n        ...x,\n        connections: new Map(x.connections).set(data.uid, {\n          accounts: data.accounts as readonly [Address, ...Address[]],\n          chainId: data.chainId,\n          connector: connector,\n        }),\n        current: data.uid,\n        status: 'connected',\n      }\n    })\n  }\n  function disconnect(data: EventData<ConnectorEventMap, 'disconnect'>) {\n    store.setState((x) => {\n      const connection = x.connections.get(data.uid)\n      if (connection) {\n        const connector = connection.connector\n        if (connector.emitter.listenerCount('change'))\n          connection.connector.emitter.off('change', change)\n        if (connector.emitter.listenerCount('disconnect'))\n          connection.connector.emitter.off('disconnect', disconnect)\n        if (!connector.emitter.listenerCount('connect'))\n          connection.connector.emitter.on('connect', connect)\n      }\n\n      x.connections.delete(data.uid)\n\n      if (x.connections.size === 0)\n        return {\n          ...x,\n          connections: new Map(),\n          current: null,\n          status: 'disconnected',\n        }\n\n      const nextConnection = x.connections.values().next().value as Connection\n      return {\n        ...x,\n        connections: new Map(x.connections),\n        current: nextConnection.connector.uid,\n      }\n    })\n  }\n\n  return {\n    get chains() {\n      return chains.getState() as chains\n    },\n    get connectors() {\n      return connectors.getState() as Connector<connectorFns[number]>[]\n    },\n    storage,\n\n    getClient,\n    get state() {\n      return store.getState() as unknown as State<chains>\n    },\n    setState(value) {\n      let newState: State\n      if (typeof value === 'function') newState = value(store.getState() as any)\n      else newState = value\n\n      // Reset state if it got set to something not matching the base state\n      const initialState = getInitialState()\n      if (typeof newState !== 'object') newState = initialState\n      const isCorrupt = Object.keys(initialState).some((x) => !(x in newState))\n      if (isCorrupt) newState = initialState\n\n      store.setState(newState, true)\n    },\n    subscribe(selector, listener, options) {\n      return store.subscribe(\n        selector as unknown as (state: State) => any,\n        listener,\n        options\n          ? ({\n              ...options,\n              fireImmediately: options.emitImmediately,\n              // Workaround cast since Zustand does not support `'exactOptionalPropertyTypes'`\n            } as RemoveUndefined<typeof options>)\n          : undefined,\n      )\n    },\n\n    _internal: {\n      mipd,\n      store,\n      ssr: Boolean(ssr),\n      syncConnectedChain,\n      transports: rest.transports as transports,\n      chains: {\n        setState(value) {\n          const nextChains = (\n            typeof value === 'function' ? value(chains.getState()) : value\n          ) as chains\n          if (nextChains.length === 0) return\n          return chains.setState(nextChains, true)\n        },\n        subscribe(listener) {\n          return chains.subscribe(listener)\n        },\n      },\n      connectors: {\n        providerDetailToConnector,\n        setup: setup as <connectorFn extends CreateConnectorFn>(\n          connectorFn: connectorFn,\n        ) => Connector<connectorFn>,\n        setState(value) {\n          return connectors.setState(\n            typeof value === 'function' ? value(connectors.getState()) : value,\n            true,\n          )\n        },\n        subscribe(listener) {\n          return connectors.subscribe(listener)\n        },\n      },\n      events: { change, connect, disconnect },\n    },\n  }\n}\n\n/////////////////////////////////////////////////////////////////////////////////////////////////\n// Types\n/////////////////////////////////////////////////////////////////////////////////////////////////\n\nexport type CreateConfigParameters<\n  chains extends readonly [Chain, ...Chain[]] = readonly [Chain, ...Chain[]],\n  transports extends Record<chains[number]['id'], Transport> = Record<\n    chains[number]['id'],\n    Transport\n  >,\n  connectorFns extends\n    readonly CreateConnectorFn[] = readonly CreateConnectorFn[],\n> = Compute<\n  {\n    chains: chains\n    connectors?: connectorFns | undefined\n    multiInjectedProviderDiscovery?: boolean | undefined\n    storage?: Storage | null | undefined\n    ssr?: boolean | undefined\n    syncConnectedChain?: boolean | undefined\n  } & OneOf<\n    | ({ transports: transports } & {\n        [key in keyof ClientConfig]?:\n          | ClientConfig[key]\n          | { [_ in chains[number]['id']]?: ClientConfig[key] | undefined }\n          | undefined\n      })\n    | {\n        client(parameters: { chain: chains[number] }): Client<\n          transports[chains[number]['id']],\n          chains[number]\n        >\n      }\n  >\n>\n\nexport type Config<\n  chains extends readonly [Chain, ...Chain[]] = readonly [Chain, ...Chain[]],\n  transports extends Record<chains[number]['id'], Transport> = Record<\n    chains[number]['id'],\n    Transport\n  >,\n  connectorFns extends\n    readonly CreateConnectorFn[] = readonly CreateConnectorFn[],\n> = {\n  readonly chains: chains\n  readonly connectors: readonly Connector<connectorFns[number]>[]\n  readonly storage: Storage | null\n\n  readonly state: State<chains>\n  setState<tchains extends readonly [Chain, ...Chain[]] = chains>(\n    value: State<tchains> | ((state: State<tchains>) => State<tchains>),\n  ): void\n  subscribe<state>(\n    selector: (state: State<chains>) => state,\n    listener: (state: state, previousState: state) => void,\n    options?:\n      | {\n          emitImmediately?: boolean | undefined\n          equalityFn?: ((a: state, b: state) => boolean) | undefined\n        }\n      | undefined,\n  ): () => void\n\n  getClient<chainId extends chains[number]['id']>(parameters?: {\n    chainId?: chainId | chains[number]['id'] | undefined\n  }): Client<transports[chainId], Extract<chains[number], { id: chainId }>>\n\n  /**\n   * Not part of versioned API, proceed with caution.\n   * @internal\n   */\n  _internal: Internal<chains, transports>\n}\n\ntype Internal<\n  chains extends readonly [Chain, ...Chain[]] = readonly [Chain, ...Chain[]],\n  transports extends Record<chains[number]['id'], Transport> = Record<\n    chains[number]['id'],\n    Transport\n  >,\n> = {\n  readonly mipd: MipdStore | undefined\n  readonly store: Mutate<StoreApi<any>, [['zustand/persist', any]]>\n  readonly ssr: boolean\n  readonly syncConnectedChain: boolean\n  readonly transports: transports\n\n  chains: {\n    setState(\n      value:\n        | readonly [Chain, ...Chain[]]\n        | ((\n            state: readonly [Chain, ...Chain[]],\n          ) => readonly [Chain, ...Chain[]]),\n    ): void\n    subscribe(\n      listener: (\n        state: readonly [Chain, ...Chain[]],\n        prevState: readonly [Chain, ...Chain[]],\n      ) => void,\n    ): () => void\n  }\n  connectors: {\n    providerDetailToConnector(\n      providerDetail: EIP6963ProviderDetail,\n    ): CreateConnectorFn\n    setup<connectorFn extends CreateConnectorFn>(\n      connectorFn: connectorFn,\n    ): Connector<connectorFn>\n    setState(value: Connector[] | ((state: Connector[]) => Connector[])): void\n    subscribe(\n      listener: (state: Connector[], prevState: Connector[]) => void,\n    ): () => void\n  }\n  events: {\n    change(data: EventData<ConnectorEventMap, 'change'>): void\n    connect(data: EventData<ConnectorEventMap, 'connect'>): void\n    disconnect(data: EventData<ConnectorEventMap, 'disconnect'>): void\n  }\n}\n\nexport type State<\n  chains extends readonly [Chain, ...Chain[]] = readonly [Chain, ...Chain[]],\n> = {\n  chainId: chains[number]['id']\n  connections: Map<string, Connection>\n  current: string | null\n  status: 'connected' | 'connecting' | 'disconnected' | 'reconnecting'\n}\n\nexport type PartializedState = Compute<\n  ExactPartial<Pick<State, 'chainId' | 'connections' | 'current' | 'status'>>\n>\n\nexport type Connection = {\n  accounts: readonly [Address, ...Address[]]\n  chainId: number\n  connector: Connector\n}\n\nexport type Connector<\n  createConnectorFn extends CreateConnectorFn = CreateConnectorFn,\n> = ReturnType<createConnectorFn> & {\n  emitter: Emitter<ConnectorEventMap>\n  uid: string\n}\n\nexport type Transport<\n  type extends string = string,\n  rpcAttributes = Record<string, any>,\n  eip1193RequestFn extends EIP1193RequestFn = EIP1193RequestFn,\n> = (\n  params: Parameters<\n    viem_Transport<type, rpcAttributes, eip1193RequestFn>\n  >[0] & {\n    connectors?: StoreApi<Connector[]> | undefined\n  },\n) => ReturnType<viem_Transport<type, rpcAttributes, eip1193RequestFn>>\n\ntype ClientConfig = LooseOmit<\n  viem_ClientConfig,\n  'account' | 'chain' | 'key' | 'name' | 'transport' | 'type'\n>\n", "import { reconnect } from './actions/reconnect.js'\nimport type { Config, State } from './createConfig.js'\n\ntype HydrateParameters = {\n  initialState?: State | undefined\n  reconnectOnMount?: boolean | undefined\n}\n\nexport function hydrate(config: Config, parameters: HydrateParameters) {\n  const { initialState, reconnectOnMount } = parameters\n\n  if (initialState && !config._internal.store.persist.hasHydrated())\n    config.setState({\n      ...initialState,\n      chainId: config.chains.some((x) => x.id === initialState.chainId)\n        ? initialState.chainId\n        : config.chains[0].id,\n      connections: reconnectOnMount ? initialState.connections : new Map(),\n      status: reconnectOnMount ? 'reconnecting' : 'disconnected',\n    })\n\n  return {\n    async onMount() {\n      if (config._internal.ssr) {\n        await config._internal.store.persist.rehydrate()\n        if (config._internal.mipd) {\n          config._internal.connectors.setState((connectors) => {\n            const rdnsSet = new Set<string>()\n            for (const connector of connectors ?? []) {\n              if (connector.rdns) {\n                const rdnsValues = Array.isArray(connector.rdns)\n                  ? connector.rdns\n                  : [connector.rdns]\n                for (const rdns of rdnsValues) {\n                  rdnsSet.add(rdns)\n                }\n              }\n            }\n            const mipdConnectors = []\n            const providers = config._internal.mipd?.getProviders() ?? []\n            for (const provider of providers) {\n              if (rdnsSet.has(provider.info.rdns)) continue\n              const connectorFn =\n                config._internal.connectors.providerDetailToConnector(provider)\n              const connector = config._internal.connectors.setup(connectorFn)\n              mipdConnectors.push(connector)\n            }\n            return [...connectors, ...mipdConnectors]\n          })\n        }\n      }\n\n      if (reconnectOnMount) reconnect(config)\n      else if (config.storage)\n        // Reset connections that may have been hydrated from storage.\n        config.setState((x) => ({\n          ...x,\n          connections: new Map(),\n        }))\n    },\n  }\n}\n", "import {\n  ChainDisconnectedError,\n  type EIP1193Parameters,\n  type EIP1193<PERSON>rovider,\n  type EIP1193RequestFn,\n  ProviderDisconnectedError,\n  type TransportConfig,\n  type WalletRpcSchema,\n  createTransport,\n  hexToNumber,\n  withRetry,\n  withTimeout,\n} from 'viem'\n\nimport type { Connector, Transport } from '../createConfig.js'\n\nexport type ConnectorTransportConfig = {\n  /** The key of the transport. */\n  key?: TransportConfig['key'] | undefined\n  /** The name of the transport. */\n  name?: TransportConfig['name'] | undefined\n  /** The max number of times to retry. */\n  retryCount?: TransportConfig['retryCount'] | undefined\n  /** The base delay (in ms) between retries. */\n  retryDelay?: TransportConfig['retryDelay'] | undefined\n}\n\nexport type ConnectorTransport = Transport\n\nexport function unstable_connector(\n  connector: Pick<Connector, 'type'>,\n  config: ConnectorTransportConfig = {},\n): Transport<'connector'> {\n  const { type } = connector\n  const { key = 'connector', name = 'Connector', retryDelay } = config\n\n  return (parameters) => {\n    const { chain, connectors } = parameters\n    const retryCount = config.retryCount ?? parameters.retryCount\n\n    const request: EIP1193RequestFn = async ({ method, params }) => {\n      const connector = connectors?.getState().find((c) => c.type === type)\n      if (!connector)\n        throw new ProviderDisconnectedError(\n          new Error(\n            `Could not find connector of type \"${type}\" in \\`connectors\\` passed to \\`createConfig\\`.`,\n          ),\n        )\n\n      const provider = (await connector.getProvider({\n        chainId: chain?.id,\n      })) as EIP1193Provider | undefined\n      if (!provider)\n        throw new ProviderDisconnectedError(\n          new Error('Provider is disconnected.'),\n        )\n\n      // We are applying a retry & timeout strategy here as some injected wallets (e.g. MetaMask) fail to\n      // immediately resolve a JSON-RPC request on page load.\n      const chainId = hexToNumber(\n        await withRetry(() =>\n          withTimeout(() => provider.request({ method: 'eth_chainId' }), {\n            timeout: 100,\n          }),\n        ),\n      )\n      if (chain && chainId !== chain.id)\n        throw new ChainDisconnectedError(\n          new Error(\n            `The current chain of the connector (id: ${chainId}) does not match the target chain for the request (id: ${chain.id} – ${chain.name}).`,\n          ),\n        )\n\n      const body = { method, params } as EIP1193Parameters<WalletRpcSchema>\n      return provider.request(body)\n    }\n\n    return createTransport({\n      key,\n      name,\n      request,\n      retryCount,\n      retryDelay,\n      type: 'connector',\n    })\n  }\n}\n", "import { fallback as viem_fallback } from 'viem'\n\nimport type { Transport } from '../createConfig.js'\n\nexport function fallback(\n  transports: Transport[],\n  config?: Parameters<typeof viem_fallback>[1],\n) {\n  return viem_fallback(transports, config)\n}\n", "import type { Config, State } from '../createConfig.js'\nimport type { BaseStorage } from '../createStorage.js'\nimport { deserialize } from './deserialize.js'\n\nexport const cookieStorage = {\n  getItem(key) {\n    if (typeof window === 'undefined') return null\n    const value = parseCookie(document.cookie, key)\n    return value ?? null\n  },\n  setItem(key, value) {\n    if (typeof window === 'undefined') return\n    document.cookie = `${key}=${value};path=/;samesite=Lax`\n  },\n  removeItem(key) {\n    if (typeof window === 'undefined') return\n    document.cookie = `${key}=;max-age=-1;path=/`\n  },\n} satisfies BaseStorage\n\nexport function cookieToInitialState(config: Config, cookie?: string | null) {\n  if (!cookie) return undefined\n  const key = `${config.storage?.key}.store`\n  const parsed = parseCookie(cookie, key)\n  if (!parsed) return undefined\n  return deserialize<{ state: State }>(parsed).state\n}\n\nexport function parseCookie(cookie: string, key: string) {\n  const keyValue = cookie.split('; ').find((x) => x.startsWith(`${key}=`))\n  if (!keyValue) return undefined\n  return keyValue.substring(key.length + 1)\n}\n", "import type { Chain, Transport } from 'viem'\n\ntype ExtractRpcUrlsParameters = {\n  transports?: Record<string, Transport> | undefined\n  chain: Chain\n}\n\nexport function extractRpcUrls(parameters: ExtractRpcUrlsParameters) {\n  const { chain } = parameters\n  const fallbackUrl = chain.rpcUrls.default.http[0]\n\n  if (!parameters.transports) return [fallbackUrl]\n\n  const transport = parameters.transports?.[chain.id]?.({ chain })\n  const transports = (transport?.value?.transports as NonNullable<\n    typeof transport\n  >[]) || [transport]\n  return transports.map(({ value }) => value?.url || fallbackUrl)\n}\n", "/** @deprecated use `Number` instead */\nexport function normalizeChainId(chainId: bigint | number | string | unknown) {\n  if (typeof chainId === 'string')\n    return Number.parseInt(\n      chainId,\n      chainId.trim().substring(0, 2) === '0x' ? 16 : 10,\n    )\n  if (typeof chainId === 'bigint') return Number(chainId)\n  if (typeof chainId === 'number') return chainId\n  throw new Error(\n    `Cannot normalize chainId \"${chainId}\" of type \"${typeof chainId}\"`,\n  )\n}\n", "'use client'\n\nimport { type ResolvedRegister, type State, hydrate } from '@wagmi/core'\nimport { type ReactElement, useEffect, useRef } from 'react'\n\nexport type HydrateProps = {\n  config: ResolvedRegister['config']\n  initialState?: State | undefined\n  reconnectOnMount?: boolean | undefined\n}\n\nexport function Hydrate(parameters: React.PropsWithChildren<HydrateProps>) {\n  const { children, config, initialState, reconnectOnMount = true } = parameters\n\n  const { onMount } = hydrate(config, {\n    initialState,\n    reconnectOnMount,\n  })\n\n  // Hydrate for non-SSR\n  if (!config._internal.ssr) onMount()\n\n  // Hydrate for SSR\n  const active = useRef(true)\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `queryKey` not required\n  useEffect(() => {\n    if (!active.current) return\n    if (!config._internal.ssr) return\n    onMount()\n    return () => {\n      active.current = false\n    }\n  }, [])\n\n  return children as ReactElement\n}\n", "export const version = '2.15.2'\n", "import { version } from '../version.js'\n\nexport const getVersion = () => `wagmi@${version}`\n", "import { BaseError as CoreError } from '@wagmi/core'\n\nimport { getVersion } from '../utils/getVersion.js'\n\nexport type BaseErrorType = BaseError & { name: 'WagmiError' }\nexport class BaseError extends CoreError {\n  override name = 'WagmiError'\n  override get docsBaseUrl() {\n    return 'https://wagmi.sh/react'\n  }\n  override get version() {\n    return getVersion()\n  }\n}\n", "import { BaseError } from './base.js'\n\nexport type WagmiProviderNotFoundErrorType = WagmiProviderNotFoundError & {\n  name: 'WagmiProviderNotFoundError'\n}\nexport class WagmiProviderNotFoundError extends BaseError {\n  override name = 'WagmiProviderNotFoundError'\n  constructor() {\n    super('`useConfig` must be used within `WagmiProvider`.', {\n      docsPath: '/api/WagmiProvider',\n    })\n  }\n}\n", "'use client'\n\nimport type { Config, ResolvedRegister } from '@wagmi/core'\nimport { useContext } from 'react'\n\nimport { WagmiContext } from '../context.js'\nimport { WagmiProviderNotFoundError } from '../errors/context.js'\nimport type { ConfigParameter } from '../types/properties.js'\n\nexport type UseConfigParameters<config extends Config = Config> =\n  ConfigParameter<config>\n\nexport type UseConfigReturnType<config extends Config = Config> = config\n\n/** https://wagmi.sh/react/api/hooks/useConfig */\nexport function useConfig<config extends Config = ResolvedRegister['config']>(\n  parameters: UseConfigParameters<config> = {},\n): UseConfigReturnType<config> {\n  const config = parameters.config ?? useContext(WagmiContext)\n  if (!config) throw new WagmiProviderNotFoundError()\n  return config as UseConfigReturnType<config>\n}\n", "import type { Config } from '../createConfig.js'\nimport type { GetChainsReturnType } from './getChains.js'\n\nexport type WatchChainsParameters<config extends Config = Config> = {\n  onChange(\n    chains: GetChainsReturnType<config>,\n    prevChains: GetChainsReturnType<config>,\n  ): void\n}\n\nexport type WatchChainsReturnType = () => void\n\n/**\n * @internal\n * We don't expose this because as far as consumers know, you can't chainge (lol) `config.chains` at runtime.\n * Setting `config.chains` via `config._internal.chains.setState(...)` is an extremely advanced use case that's not worth documenting or supporting in the public API at this time.\n */\nexport function watchChains<config extends Config>(\n  config: config,\n  parameters: WatchChainsParameters<config>,\n): WatchChainsReturnType {\n  const { onChange } = parameters\n  return config._internal.chains.subscribe((chains, prevChains) => {\n    onChange(\n      chains as unknown as GetChainsReturnType<config>,\n      prevChains as unknown as GetChainsReturnType<config>,\n    )\n  })\n}\n", "'use client'\n\nimport { deepEqual } from '@wagmi/core/internal'\nimport { useMemo, useRef } from 'react'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector.js'\n\nconst isPlainObject = (obj: unknown) =>\n  typeof obj === 'object' && !Array.isArray(obj)\n\nexport function useSyncExternalStoreWithTracked<\n  snapshot extends selection,\n  selection = snapshot,\n>(\n  subscribe: (onStoreChange: () => void) => () => void,\n  getSnapshot: () => snapshot,\n  getServerSnapshot: undefined | null | (() => snapshot) = getSnapshot,\n  isEqual: (a: selection, b: selection) => boolean = deepEqual,\n) {\n  const trackedKeys = useRef<string[]>([])\n  const result = useSyncExternalStoreWithSelector(\n    subscribe,\n    getSnapshot,\n    getServerSnapshot,\n    (x) => x,\n    (a, b) => {\n      if (isPlainObject(a) && isPlainObject(b) && trackedKeys.current.length) {\n        for (const key of trackedKeys.current) {\n          const equal = isEqual(\n            (a as { [_a: string]: any })[key],\n            (b as { [_b: string]: any })[key],\n          )\n          if (!equal) return false\n        }\n        return true\n      }\n      return isEqual(a, b)\n    },\n  )\n\n  return useMemo(() => {\n    if (isPlainObject(result)) {\n      const trackedResult = { ...result }\n      let properties = {}\n      for (const [key, value] of Object.entries(\n        trackedResult as { [key: string]: any },\n      )) {\n        properties = {\n          ...properties,\n          [key]: {\n            configurable: false,\n            enumerable: true,\n            get: () => {\n              if (!trackedKeys.current.includes(key)) {\n                trackedKeys.current.push(key)\n              }\n              return value\n            },\n          },\n        }\n      }\n      Object.defineProperties(trackedResult, properties)\n      return trackedResult\n    }\n\n    return result\n  }, [result])\n}\n", "'use client'\n\nimport {\n  type Config,\n  type GetAccountReturnType,\n  type ResolvedRegister,\n  getAccount,\n  watchAccount,\n} from '@wagmi/core'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\nimport { useSyncExternalStoreWithTracked } from './useSyncExternalStoreWithTracked.js'\n\nexport type UseAccountParameters<config extends Config = Config> =\n  ConfigParameter<config>\n\nexport type UseAccountReturnType<config extends Config = Config> =\n  GetAccountReturnType<config>\n\n/** https://wagmi.sh/react/api/hooks/useAccount */\nexport function useAccount<config extends Config = ResolvedRegister['config']>(\n  parameters: UseAccountParameters<config> = {},\n): UseAccountReturnType<config> {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStoreWithTracked(\n    (onChange) => watchAccount(config, { onChange }),\n    () => getAccount(config),\n  )\n}\n", "'use client'\n\nimport { type GetAccountReturnType, watchAccount } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport { useEffect } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseAccountEffectParameters = Compute<\n  {\n    onConnect?(\n      data: Compute<\n        Pick<\n          Extract<GetAccountReturnType, { status: 'connected' }>,\n          'address' | 'addresses' | 'chain' | 'chainId' | 'connector'\n        > & {\n          isReconnected: boolean\n        }\n      >,\n    ): void\n    onDisconnect?(): void\n  } & ConfigParameter\n>\n\n/** https://wagmi.sh/react/api/hooks/useAccountEffect */\nexport function useAccountEffect(parameters: UseAccountEffectParameters = {}) {\n  const { onConnect, onDisconnect } = parameters\n\n  const config = useConfig(parameters)\n\n  useEffect(() => {\n    return watchAccount(config, {\n      onChange(data, prevData) {\n        if (\n          (prevData.status === 'reconnecting' ||\n            (prevData.status === 'connecting' &&\n              prevData.address === undefined)) &&\n          data.status === 'connected'\n        ) {\n          const { address, addresses, chain, chainId, connector } = data\n          const isReconnected =\n            prevData.status === 'reconnecting' ||\n            // if `previousAccount.status` is `undefined`, the connector connected immediately.\n            prevData.status === undefined\n          onConnect?.({\n            address,\n            addresses,\n            chain,\n            chainId,\n            connector,\n            isReconnected,\n          })\n        } else if (\n          prevData.status === 'connected' &&\n          data.status === 'disconnected'\n        )\n          onDisconnect?.()\n      },\n    })\n  }, [config, onConnect, onDisconnect])\n}\n", "import { type QueryKey, replaceEqualDeep } from '@tanstack/query-core'\n\nexport function structuralSharing<data>(\n  oldData: data | undefined,\n  newData: data,\n): data {\n  return replaceEqualDeep(oldData, newData)\n}\n\nexport function hashFn(queryKey: QueryKey): string {\n  return JSON.stringify(queryKey, (_, value) => {\n    if (isPlainObject(value))\n      return Object.keys(value)\n        .sort()\n        .reduce((result, key) => {\n          result[key] = value[key]\n          return result\n        }, {} as any)\n    if (typeof value === 'bigint') return value.toString()\n    return value\n  })\n}\n\n// biome-ignore lint/complexity/noBannedTypes:\nfunction isPlainObject(value: any): value is Object {\n  if (!hasObjectPrototype(value)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = value.constructor\n  if (typeof ctor === 'undefined') return true\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) return false\n\n  // If constructor does not have an Object-specific method\n  // biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>\n  if (!prot.hasOwnProperty('isPrototypeOf')) return false\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any): boolean {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function filterQueryOptions<type extends Record<string, unknown>>(\n  options: type,\n): type {\n  // destructuring is super fast\n  // biome-ignore format: no formatting\n  const {\n    // import('@tanstack/query-core').QueryOptions\n    _defaulted, behavior, gcTime, initialData, initialDataUpdatedAt, maxPages, meta, networkMode, queryFn, queryHash, queryKey, queryKeyHashFn, retry, retryDelay, structuralSharing,\n\n    // import('@tanstack/query-core').InfiniteQueryObserverOptions\n    getPreviousPageParam, getNextPageParam, initialPageParam,\n\n    // import('@tanstack/react-query').UseQueryOptions\n    _optimisticResults, enabled, notifyOnChangeProps, placeholderData, refetchInterval, refetchIntervalInBackground, refetchOnMount, refetchOnReconnect, refetchOnWindowFocus, retryOnMount, select, staleTime, suspense, throwOnError,\n\n    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n    // wagmi\n    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////\n    config, connector, query,\n    ...rest\n  } = options\n\n  return rest as type\n}\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type CallErrorType,\n  type CallParameters,\n  type CallReturnType,\n  call,\n} from '../actions/call.js'\nimport type { Config } from '../createConfig.js'\nimport type { Scope<PERSON>eyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type CallOptions<config extends Config> = Compute<\n  ExactPartial<CallParameters<config>> & ScopeKeyParameter\n>\n\nexport function callQueryOptions<config extends Config>(\n  config: config,\n  options: CallOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const data = await call(config, {\n        ...parameters,\n      } as CallParameters)\n      return data ?? null\n    },\n    queryKey: callQueryKey(options),\n  } as const satisfies QueryOptions<\n    CallQueryFnData,\n    CallErrorType,\n    CallData,\n    CallQueryKey<config>\n  >\n}\n\nexport type CallQueryFnData = CallReturnType\n\nexport type CallData = CallQueryFnData\n\nexport function callQueryKey<config extends Config>(\n  options: CallOptions<config>,\n) {\n  return ['call', filterQueryOptions(options)] as const\n}\n\nexport type CallQueryKey<config extends Config> = ReturnType<\n  typeof callQueryKey<config>\n>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type ConnectErrorType,\n  type ConnectParameters,\n  type ConnectReturnType,\n  connect,\n} from '../actions/connect.js'\nimport type { Config, Connector } from '../createConfig.js'\n\nimport type { CreateConnectorFn } from '../connectors/createConnector.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function connectMutationOptions<config extends Config>(config: config) {\n  return {\n    mutationFn(variables) {\n      return connect(config, variables)\n    },\n    mutationKey: ['connect'],\n  } as const satisfies MutationOptions<\n    ConnectData<config>,\n    ConnectErrorType,\n    ConnectVariables<config, Connector | CreateConnectorFn>\n  >\n}\n\nexport type ConnectData<config extends Config> = ConnectReturnType<config>\n\nexport type ConnectVariables<\n  config extends Config,\n  connector extends Connector | CreateConnectorFn,\n> = ConnectParameters<config, connector>\n\nexport type ConnectMutate<config extends Config, context = unknown> = <\n  connector extends\n    | config['connectors'][number]\n    | Connector\n    | CreateConnectorFn,\n>(\n  variables: ConnectVariables<config, connector>,\n  options?:\n    | Compute<\n        MutateOptions<\n          ConnectData<config>,\n          ConnectErrorType,\n          Compute<ConnectVariables<config, connector>>,\n          context\n        >\n      >\n    | undefined,\n) => void\n\nexport type ConnectMutateAsync<config extends Config, context = unknown> = <\n  connector extends\n    | config['connectors'][number]\n    | Connector\n    | CreateConnectorFn,\n>(\n  variables: ConnectVariables<config, connector>,\n  options?:\n    | Compute<\n        MutateOptions<\n          ConnectData<config>,\n          ConnectErrorType,\n          Compute<ConnectVariables<config, connector>>,\n          context\n        >\n      >\n    | undefined,\n) => Promise<ConnectData<config>>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\nimport type { Abi, ContractConstructorArgs } from 'viem'\n\nimport {\n  type DeployContractErrorType,\n  type DeployContractParameters,\n  type DeployContractReturnType,\n  deployContract,\n} from '../actions/deployContract.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function deployContractMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return deployContract(config, variables)\n    },\n    mutationKey: ['deployContract'],\n  } as const satisfies MutationOptions<\n    DeployContractData,\n    DeployContractErrorType,\n    DeployContractVariables<Abi, config, config['chains'][number]['id']>\n  >\n}\n\nexport type DeployContractData = Compute<DeployContractReturnType>\n\nexport type DeployContractVariables<\n  abi extends Abi | readonly unknown[],\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n  ///\n  allArgs = ContractConstructorArgs<abi>,\n> = DeployContractParameters<abi, config, chainId, allArgs>\n\nexport type DeployContractMutate<config extends Config, context = unknown> = <\n  abi extends Abi | readonly unknown[],\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: DeployContractVariables<abi, config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          DeployContractData,\n          DeployContractErrorType,\n          Compute<DeployContractVariables<abi, config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => void\n\nexport type DeployContractMutateAsync<\n  config extends Config,\n  context = unknown,\n> = <\n  abi extends Abi | readonly unknown[],\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: DeployContractVariables<abi, config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          DeployContractData,\n          DeployContractErrorType,\n          Compute<DeployContractVariables<abi, config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => Promise<DeployContractData>\n", "import type { MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type DisconnectErrorType,\n  type DisconnectParameters,\n  type DisconnectReturnType,\n  disconnect,\n} from '../actions/disconnect.js'\nimport type { Config } from '../createConfig.js'\nimport type { Mutate, MutateAsync } from './types.js'\n\nexport function disconnectMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return disconnect(config, variables)\n    },\n    mutationKey: ['disconnect'],\n  } as const satisfies MutationOptions<\n    DisconnectData,\n    DisconnectErrorType,\n    DisconnectVariables\n  >\n}\n\nexport type DisconnectData = DisconnectReturnType\n\nexport type DisconnectVariables = DisconnectParameters | undefined\n\nexport type DisconnectMutate<context = unknown> = Mutate<\n  DisconnectData,\n  DisconnectErrorType,\n  DisconnectVariables,\n  context\n>\n\nexport type DisconnectMutateAsync<context = unknown> = MutateAsync<\n  DisconnectData,\n  DisconnectErrorType,\n  DisconnectVariables,\n  context\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\nimport type { FeeValuesType } from 'viem'\n\nimport {\n  type EstimateFeesPerGasErrorType,\n  type EstimateFeesPerGasParameters,\n  type EstimateFeesPerGasReturnType,\n  estimateFeesPerGas,\n} from '../actions/estimateFeesPerGas.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type EstimateFeesPerGasOptions<\n  type extends FeeValuesType,\n  config extends Config,\n> = Compute<\n  ExactPartial<EstimateFeesPerGasParameters<type, config>> & ScopeKeyParameter\n>\n\nexport function estimateFeesPerGasQueryOptions<\n  config extends Config,\n  type extends FeeValuesType = 'eip1559',\n>(config: config, options: EstimateFeesPerGasOptions<type, config> = {}) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      return estimateFeesPerGas(config, parameters)\n    },\n    queryKey: estimateFeesPerGasQueryKey(options),\n  } as const satisfies QueryOptions<\n    EstimateFeesPerGasQueryFnData<type>,\n    EstimateFeesPerGasErrorType,\n    EstimateFeesPerGasData<type>,\n    EstimateFeesPerGasQueryKey<config, type>\n  >\n}\n\nexport type EstimateFeesPerGasQueryFnData<type extends FeeValuesType> =\n  EstimateFeesPerGasReturnType<type>\n\nexport type EstimateFeesPerGasData<type extends FeeValuesType> =\n  EstimateFeesPerGasQueryFnData<type>\n\nexport function estimateFeesPerGasQueryKey<\n  config extends Config,\n  type extends FeeValuesType = 'eip1559',\n>(options: EstimateFeesPerGasOptions<type, config> = {}) {\n  return ['estimateFeesPerGas', filterQueryOptions(options)] as const\n}\n\nexport type EstimateFeesPerGasQueryKey<\n  config extends Config,\n  type extends FeeValuesType,\n> = ReturnType<typeof estimateFeesPerGasQueryKey<config, type>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type EstimateGasErrorType,\n  type EstimateGasParameters,\n  type EstimateGasReturnType,\n  estimateGas,\n} from '../actions/estimateGas.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { UnionExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type EstimateGasOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n> = UnionExactPartial<EstimateGasParameters<config, chainId>> &\n  ScopeKeyParameter\n\nexport function estimateGasQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: EstimateGasOptions<config, chainId> = {} as any) {\n  return {\n    async queryFn({ queryKey }) {\n      const { connector } = options\n      const { account, scopeKey: _, ...parameters } = queryKey[1]\n      if (!account && !connector)\n        throw new Error('account or connector is required')\n      return estimateGas(config, { account, connector, ...(parameters as any) })\n    },\n    queryKey: estimateGasQueryKey(options),\n  } as const satisfies QueryOptions<\n    EstimateGasQueryFnData,\n    EstimateGasErrorType,\n    EstimateGasData,\n    EstimateGasQueryKey<config, chainId>\n  >\n}\n\nexport type EstimateGasQueryFnData = EstimateGasReturnType\n\nexport type EstimateGasData = EstimateGasQueryFnData\n\nexport function estimateGasQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n>(options: EstimateGasOptions<config, chainId> = {} as any) {\n  const { connector: _, ...rest } = options\n  return ['estimateGas', filterQueryOptions(rest)] as const\n}\n\nexport type EstimateGasQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n> = ReturnType<typeof estimateGasQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type EstimateMaxPriorityFeePerGasErrorType,\n  type EstimateMaxPriorityFeePerGasParameters,\n  type EstimateMaxPriorityFeePerGasReturnType,\n  estimateMaxPriorityFeePerGas,\n} from '../actions/estimateMaxPriorityFeePerGas.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type EstimateMaxPriorityFeePerGasOptions<config extends Config> =\n  Compute<\n    ExactPartial<EstimateMaxPriorityFeePerGasParameters<config>> &\n      ScopeKeyParameter\n  >\n\nexport function estimateMaxPriorityFeePerGasQueryOptions<config extends Config>(\n  config: config,\n  options: EstimateMaxPriorityFeePerGasOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      return estimateMaxPriorityFeePerGas(config, parameters)\n    },\n    queryKey: estimateMaxPriorityFeePerGasQueryKey(options),\n  } as const satisfies QueryOptions<\n    EstimateMaxPriorityFeePerGasQueryFnData,\n    EstimateMaxPriorityFeePerGasErrorType,\n    EstimateMaxPriorityFeePerGasData,\n    EstimateMaxPriorityFeePerGasQueryKey<config>\n  >\n}\n\nexport type EstimateMaxPriorityFeePerGasQueryFnData =\n  EstimateMaxPriorityFeePerGasReturnType\n\nexport type EstimateMaxPriorityFeePerGasData =\n  EstimateMaxPriorityFeePerGasQueryFnData\n\nexport function estimateMaxPriorityFeePerGasQueryKey<config extends Config>(\n  options: EstimateMaxPriorityFeePerGasOptions<config> = {},\n) {\n  return ['estimateMaxPriorityFeePerGas', filterQueryOptions(options)] as const\n}\n\nexport type EstimateMaxPriorityFeePerGasQueryKey<config extends Config> =\n  ReturnType<typeof estimateMaxPriorityFeePerGasQueryKey<config>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetBalanceErrorType,\n  type GetBalanceParameters,\n  type GetBalanceReturnType,\n  getBalance,\n} from '../actions/getBalance.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, PartialBy } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetBalanceOptions<config extends Config> = Compute<\n  PartialBy<GetBalanceParameters<config>, 'address'> & ScopeKeyParameter\n>\n\nexport function getBalanceQueryOptions<config extends Config>(\n  config: config,\n  options: GetBalanceOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, scopeKey: _, ...parameters } = queryKey[1]\n      if (!address) throw new Error('address is required')\n      const balance = await getBalance(config, {\n        ...(parameters as GetBalanceParameters),\n        address,\n      })\n      return balance ?? null\n    },\n    queryKey: getBalanceQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetBalanceQueryFnData,\n    GetBalanceErrorType,\n    GetBalanceData,\n    GetBalanceQueryKey<config>\n  >\n}\n\nexport type GetBalanceQueryFnData = Compute<GetBalanceReturnType>\n\nexport type GetBalanceData = GetBalanceQueryFnData\n\nexport function getBalanceQueryKey<config extends Config>(\n  options: GetBalanceOptions<config> = {},\n) {\n  return ['balance', filterQueryOptions(options)] as const\n}\n\nexport type GetBalanceQueryKey<config extends Config> = ReturnType<\n  typeof getBalanceQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\nimport type { BlockTag } from 'viem'\n\nimport {\n  type GetBlockErrorType,\n  type GetBlockParameters,\n  type GetBlockReturnType,\n  getBlock,\n} from '../actions/getBlock.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetBlockOptions<\n  includeTransactions extends boolean,\n  blockTag extends BlockTag,\n  config extends Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<\n    GetBlockParameters<includeTransactions, blockTag, config, chainId>\n  > &\n    ScopeKeyParameter\n>\n\nexport function getBlockQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n  includeTransactions extends boolean,\n  blockTag extends BlockTag,\n>(\n  config: config,\n  options: GetBlockOptions<includeTransactions, blockTag, config, chainId> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const block = await getBlock(config, parameters)\n      return (block ?? null) as any\n    },\n    queryKey: getBlockQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetBlockQueryFnData<includeTransactions, blockTag, config, chainId>,\n    GetBlockErrorType,\n    GetBlockData<includeTransactions, blockTag, config, chainId>,\n    GetBlockQueryKey<includeTransactions, blockTag, config, chainId>\n  >\n}\n\nexport type GetBlockQueryFnData<\n  includeTransactions extends boolean,\n  blockTag extends BlockTag,\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetBlockReturnType<includeTransactions, blockTag, config, chainId>\n\nexport type GetBlockData<\n  includeTransactions extends boolean,\n  blockTag extends BlockTag,\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetBlockQueryFnData<includeTransactions, blockTag, config, chainId>\n\nexport function getBlockQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n>(\n  options: GetBlockOptions<includeTransactions, blockTag, config, chainId> = {},\n) {\n  return ['block', filterQueryOptions(options)] as const\n}\n\nexport type GetBlockQueryKey<\n  includeTransactions extends boolean,\n  blockTag extends BlockTag,\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<\n  typeof getBlockQueryKey<config, chainId, includeTransactions, blockTag>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetBlockNumberErrorType,\n  type GetBlockNumberParameters,\n  type GetBlockNumberReturnType,\n  getBlockNumber,\n} from '../actions/getBlockNumber.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetBlockNumberOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<GetBlockNumberParameters<config, chainId>> & ScopeKeyParameter\n>\n\nexport function getBlockNumberQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetBlockNumberOptions<config, chainId> = {}) {\n  return {\n    gcTime: 0,\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const blockNumber = await getBlockNumber(config, parameters)\n      return blockNumber ?? null\n    },\n    queryKey: getBlockNumberQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetBlockNumberQueryFnData,\n    GetBlockNumberErrorType,\n    GetBlockNumberData,\n    GetBlockNumberQueryKey<config, chainId>\n  >\n}\n\nexport type GetBlockNumberQueryFnData = GetBlockNumberReturnType\n\nexport type GetBlockNumberData = GetBlockNumberQueryFnData\n\nexport function getBlockNumberQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetBlockNumberOptions<config, chainId> = {}) {\n  return ['blockNumber', filterQueryOptions(options)] as const\n}\n\nexport type GetBlockNumberQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getBlockNumberQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetBlockTransactionCountErrorType,\n  type GetBlockTransactionCountParameters,\n  type GetBlockTransactionCountReturnType,\n  getBlockTransactionCount,\n} from '../actions/getBlockTransactionCount.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { ExactPartial, UnionCompute } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetBlockTransactionCountOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = UnionCompute<\n  ExactPartial<GetBlockTransactionCountParameters<config, chainId>> &\n    ScopeKeyParameter\n>\n\nexport function getBlockTransactionCountQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  options: GetBlockTransactionCountOptions<config, chainId> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const blockTransactionCount = await getBlockTransactionCount(\n        config,\n        parameters,\n      )\n      return blockTransactionCount ?? null\n    },\n    queryKey: getBlockTransactionCountQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetBlockTransactionCountQueryFnData,\n    GetBlockTransactionCountErrorType,\n    GetBlockTransactionCountData,\n    GetBlockTransactionCountQueryKey<config, chainId>\n  >\n}\n\nexport type GetBlockTransactionCountQueryFnData =\n  GetBlockTransactionCountReturnType\n\nexport type GetBlockTransactionCountData = GetBlockTransactionCountQueryFnData\n\nexport function getBlockTransactionCountQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetBlockTransactionCountOptions<config, chainId> = {}) {\n  return ['blockTransactionCount', filterQueryOptions(options)] as const\n}\n\nexport type GetBlockTransactionCountQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getBlockTransactionCountQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetBytecodeErrorType,\n  type GetBytecodeParameters,\n  type GetBytecodeReturnType,\n  getBytecode,\n} from '../actions/getBytecode.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetBytecodeOptions<config extends Config> = Compute<\n  ExactPartial<GetBytecodeParameters<config>> & ScopeKeyParameter\n>\n\nexport function getBytecodeQueryOptions<config extends Config>(\n  config: config,\n  options: GetBytecodeOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, scopeKey: _, ...parameters } = queryKey[1]\n      if (!address) throw new Error('address is required')\n      const bytecode = await getBytecode(config, { ...parameters, address })\n      return (bytecode ?? null) as any\n    },\n    queryKey: getBytecodeQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetBytecodeQueryFnData,\n    GetBytecodeErrorType,\n    GetBytecodeData,\n    GetBytecodeQueryKey<config>\n  >\n}\nexport type GetBytecodeQueryFnData = GetBytecodeReturnType\n\nexport type GetBytecodeData = GetBytecodeQueryFnData\n\nexport function getBytecodeQueryKey<config extends Config>(\n  options: GetBytecodeOptions<config>,\n) {\n  return ['getBytecode', filterQueryOptions(options)] as const\n}\n\nexport type GetBytecodeQueryKey<config extends Config> = ReturnType<\n  typeof getBytecodeQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetCallsStatusErrorType,\n  type GetCallsStatusParameters,\n  type GetCallsStatusReturnType,\n  getCallsStatus,\n} from '../actions/getCallsStatus.js'\nimport type { Config } from '../createConfig.js'\nimport { ConnectorNotConnectedError } from '../errors/config.js'\nimport { filterQueryOptions } from '../query/utils.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute } from '../types/utils.js'\n\nexport type GetCallsStatusOptions = Compute<\n  GetCallsStatusParameters & ScopeKeyParameter\n>\n\nexport function getCallsStatusQueryOptions<config extends Config>(\n  config: config,\n  options: GetCallsStatusOptions,\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const status = await getCallsStatus(config, parameters)\n      return status\n    },\n    queryKey: getCallsStatusQueryKey(options),\n    retry(failureCount, error) {\n      if (error instanceof ConnectorNotConnectedError) return false\n      return failureCount < 3\n    },\n  } as const satisfies QueryOptions<\n    GetCallsStatusQueryFnData,\n    GetCallsStatusErrorType,\n    GetCallsStatusData,\n    GetCallsStatusQueryKey\n  >\n}\n\nexport type GetCallsStatusQueryFnData = GetCallsStatusReturnType\n\nexport type GetCallsStatusData = GetCallsStatusQueryFnData\n\nexport function getCallsStatusQueryKey(options: GetCallsStatusOptions) {\n  return ['callsStatus', filterQueryOptions(options)] as const\n}\n\nexport type GetCallsStatusQueryKey = ReturnType<typeof getCallsStatusQueryKey>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetCapabilitiesErrorType,\n  type GetCapabilitiesParameters,\n  type GetCapabilitiesReturnType,\n  getCapabilities,\n} from '../actions/getCapabilities.js'\nimport type { Config } from '../createConfig.js'\nimport { ConnectorNotConnectedError } from '../errors/config.js'\nimport { filterQueryOptions } from '../query/utils.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\n\nexport type GetCapabilitiesOptions<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n> = Compute<\n  ExactPartial<GetCapabilitiesParameters<config, chainId>> & ScopeKeyParameter\n>\n\nexport function getCapabilitiesQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n>(config: config, options: GetCapabilitiesOptions<config, chainId> = {}) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const capabilities = await getCapabilities(config, parameters)\n      return capabilities\n    },\n    queryKey: getCapabilitiesQueryKey(options),\n    retry(failureCount, error) {\n      if (error instanceof ConnectorNotConnectedError) return false\n      return failureCount < 3\n    },\n  } as const satisfies QueryOptions<\n    GetCapabilitiesQueryFnData<config, chainId>,\n    GetCapabilitiesErrorType,\n    GetCapabilitiesData<config, chainId>,\n    GetCapabilitiesQueryKey<config, chainId>\n  >\n}\n\nexport type GetCapabilitiesQueryFnData<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n> = GetCapabilitiesReturnType<config, chainId>\n\nexport type GetCapabilitiesData<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n> = GetCapabilitiesQueryFnData<config, chainId>\n\nexport function getCapabilitiesQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n>(options: GetCapabilitiesOptions<config, chainId> = {}) {\n  return ['capabilities', filterQueryOptions(options)] as const\n}\n\nexport type GetCapabilitiesQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n> = ReturnType<typeof getCapabilitiesQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetConnectorClientErrorType,\n  type GetConnectorClientParameters,\n  type GetConnectorClientReturnType,\n  getConnectorClient,\n} from '../actions/getConnectorClient.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetConnectorClientOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<GetConnectorClientParameters<config, chainId>> &\n    ScopeKeyParameter\n>\n\nexport function getConnectorClientQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetConnectorClientOptions<config, chainId> = {}) {\n  return {\n    gcTime: 0,\n    async queryFn({ queryKey }) {\n      const { connector } = options\n      const { connectorUid: _, scopeKey: _s, ...parameters } = queryKey[1]\n      return getConnectorClient(config, {\n        ...parameters,\n        connector,\n      }) as unknown as Promise<GetConnectorClientReturnType<config, chainId>>\n    },\n    queryKey: getConnectorClientQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetConnectorClientQueryFnData<config, chainId>,\n    GetConnectorClientErrorType,\n    GetConnectorClientData<config, chainId>,\n    GetConnectorClientQueryKey<config, chainId>\n  >\n}\n\nexport type GetConnectorClientQueryFnData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetConnectorClientReturnType<config, chainId>\n\nexport type GetConnectorClientData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetConnectorClientQueryFnData<config, chainId>\n\nexport function getConnectorClientQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetConnectorClientOptions<config, chainId> = {}) {\n  const { connector, ...parameters } = options\n  return [\n    'connectorClient',\n    { ...filterQueryOptions(parameters), connectorUid: connector?.uid },\n  ] as const\n}\n\nexport type GetConnectorClientQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getConnectorClientQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetEnsAddressErrorType,\n  type GetEnsAddressParameters,\n  type GetEnsAddressReturnType,\n  getEnsAddress,\n} from '../actions/getEnsAddress.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetEnsAddressOptions<config extends Config> = Compute<\n  ExactPartial<GetEnsAddressParameters<config>> & ScopeKeyParameter\n>\n\nexport function getEnsAddressQueryOptions<config extends Config>(\n  config: config,\n  options: GetEnsAddressOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { name, scopeKey: _, ...parameters } = queryKey[1]\n      if (!name) throw new Error('name is required')\n      return getEnsAddress(config, { ...parameters, name })\n    },\n    queryKey: getEnsAddressQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetEnsAddressQueryFnData,\n    GetEnsAddressErrorType,\n    GetEnsAddressData,\n    GetEnsAddressQueryKey<config>\n  >\n}\n\nexport type GetEnsAddressQueryFnData = GetEnsAddressReturnType\n\nexport type GetEnsAddressData = GetEnsAddressQueryFnData\n\nexport function getEnsAddressQueryKey<config extends Config>(\n  options: GetEnsAddressOptions<config> = {},\n) {\n  return ['ensAddress', filterQueryOptions(options)] as const\n}\n\nexport type GetEnsAddressQueryKey<config extends Config> = ReturnType<\n  typeof getEnsAddressQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetEnsAvatarErrorType,\n  type GetEnsAvatarParameters,\n  type GetEnsAvatarReturnType,\n  getEnsAvatar,\n} from '../actions/getEnsAvatar.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetEnsAvatarOptions<config extends Config> = Compute<\n  ExactPartial<GetEnsAvatarParameters<config>> & ScopeKeyParameter\n>\n\nexport function getEnsAvatarQueryOptions<config extends Config>(\n  config: config,\n  options: GetEnsAvatarOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { name, scopeKey: _, ...parameters } = queryKey[1]\n      if (!name) throw new Error('name is required')\n      return getEnsAvatar(config, { ...parameters, name })\n    },\n    queryKey: getEnsAvatarQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetEnsAvatarQueryFnData,\n    GetEnsAvatarErrorType,\n    GetEnsAvatarData,\n    GetEnsAvatarQueryKey<config>\n  >\n}\n\nexport type GetEnsAvatarQueryFnData = GetEnsAvatarReturnType\n\nexport type GetEnsAvatarData = GetEnsAvatarQueryFnData\n\nexport function getEnsAvatarQueryKey<config extends Config>(\n  options: GetEnsAvatarOptions<config> = {},\n) {\n  return ['ensAvatar', filterQueryOptions(options)] as const\n}\n\nexport type GetEnsAvatarQueryKey<config extends Config> = ReturnType<\n  typeof getEnsAvatarQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetEnsNameErrorType,\n  type GetEnsNameParameters,\n  type GetEnsNameReturnType,\n  getEnsName,\n} from '../actions/getEnsName.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetEnsNameOptions<config extends Config> = Compute<\n  ExactPartial<GetEnsNameParameters<config>> & ScopeKeyParameter\n>\n\nexport function getEnsNameQueryOptions<config extends Config>(\n  config: config,\n  options: GetEnsNameOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, scopeKey: _, ...parameters } = queryKey[1]\n      if (!address) throw new Error('address is required')\n      return getEnsName(config, { ...parameters, address })\n    },\n    queryKey: getEnsNameQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetEnsNameQueryFnData,\n    GetEnsNameErrorType,\n    GetEnsNameData,\n    GetEnsNameQueryKey<config>\n  >\n}\n\nexport type GetEnsNameQueryFnData = GetEnsNameReturnType\n\nexport type GetEnsNameData = GetEnsNameQueryFnData\n\nexport function getEnsNameQueryKey<config extends Config>(\n  options: GetEnsNameOptions<config> = {},\n) {\n  return ['ensName', filterQueryOptions(options)] as const\n}\n\nexport type GetEnsNameQueryKey<config extends Config> = ReturnType<\n  typeof getEnsNameQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetEnsResolverErrorType,\n  type GetEnsResolverParameters,\n  type GetEnsResolverReturnType,\n  getEnsResolver,\n} from '../actions/getEnsResolver.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetEnsResolverOptions<config extends Config> = Compute<\n  ExactPartial<GetEnsResolverParameters<config>> & ScopeKeyParameter\n>\n\nexport function getEnsResolverQueryOptions<config extends Config>(\n  config: config,\n  options: GetEnsResolverOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { name, scopeKey: _, ...parameters } = queryKey[1]\n      if (!name) throw new Error('name is required')\n      return getEnsResolver(config, { ...parameters, name })\n    },\n    queryKey: getEnsResolverQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetEnsResolverQueryFnData,\n    GetEnsResolverErrorType,\n    GetEnsResolverData,\n    GetEnsResolverQueryKey<config>\n  >\n}\n\nexport type GetEnsResolverQueryFnData = GetEnsResolverReturnType\n\nexport type GetEnsResolverData = GetEnsResolverQueryFnData\n\nexport function getEnsResolverQueryKey<config extends Config>(\n  options: GetEnsResolverOptions<config> = {},\n) {\n  return ['ensResolver', filterQueryOptions(options)] as const\n}\n\nexport type GetEnsResolverQueryKey<config extends Config> = ReturnType<\n  typeof getEnsResolverQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetEnsTextErrorType,\n  type GetEnsTextParameters,\n  type GetEnsTextReturnType,\n  getEnsText,\n} from '../actions/getEnsText.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetEnsTextOptions<config extends Config> = Compute<\n  ExactPartial<GetEnsTextParameters<config>> & ScopeKeyParameter\n>\n\nexport function getEnsTextQueryOptions<config extends Config>(\n  config: config,\n  options: GetEnsTextOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { key, name, scopeKey: _, ...parameters } = queryKey[1]\n      if (!key || !name) throw new Error('key and name are required')\n      return getEnsText(config, { ...parameters, key, name })\n    },\n    queryKey: getEnsTextQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetEnsTextQueryFnData,\n    GetEnsTextErrorType,\n    GetEnsTextData,\n    GetEnsTextQueryKey<config>\n  >\n}\n\nexport type GetEnsTextQueryFnData = GetEnsTextReturnType\n\nexport type GetEnsTextData = GetEnsTextQueryFnData\n\nexport function getEnsTextQueryKey<config extends Config>(\n  options: GetEnsTextOptions<config> = {},\n) {\n  return ['ensText', filterQueryOptions(options)] as const\n}\n\nexport type GetEnsTextQueryKey<config extends Config> = ReturnType<\n  typeof getEnsTextQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetFeeHistoryErrorType,\n  type GetFeeHistoryParameters,\n  type GetFeeHistoryReturnType,\n  getFeeHistory,\n} from '../actions/getFeeHistory.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, PartialBy } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetFeeHistoryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  PartialBy<\n    GetFeeHistoryParameters<config, chainId>,\n    'blockCount' | 'rewardPercentiles'\n  > &\n    ScopeKeyParameter\n>\n\nexport function getFeeHistoryQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetFeeHistoryOptions<config, chainId> = {}) {\n  return {\n    async queryFn({ queryKey }) {\n      const {\n        blockCount,\n        rewardPercentiles,\n        scopeKey: _,\n        ...parameters\n      } = queryKey[1]\n      if (!blockCount) throw new Error('blockCount is required')\n      if (!rewardPercentiles) throw new Error('rewardPercentiles is required')\n      const feeHistory = await getFeeHistory(config, {\n        ...(parameters as GetFeeHistoryParameters),\n        blockCount,\n        rewardPercentiles,\n      })\n      return feeHistory ?? null\n    },\n    queryKey: getFeeHistoryQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetFeeHistoryQueryFnData,\n    GetFeeHistoryErrorType,\n    GetFeeHistoryData,\n    GetFeeHistoryQueryKey<config, chainId>\n  >\n}\n\nexport type GetFeeHistoryQueryFnData = GetFeeHistoryReturnType\n\nexport type GetFeeHistoryData = GetFeeHistoryQueryFnData\n\nexport function getFeeHistoryQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetFeeHistoryOptions<config, chainId> = {}) {\n  return ['feeHistory', filterQueryOptions(options)] as const\n}\n\nexport type GetFeeHistoryQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getFeeHistoryQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetGasPriceErrorType,\n  type GetGasPriceParameters,\n  type GetGasPriceReturnType,\n  getGasPrice,\n} from '../actions/getGasPrice.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetGasPriceOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<GetGasPriceParameters<config, chainId>> & ScopeKeyParameter\n>\n\nexport function getGasPriceQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetGasPriceOptions<config, chainId> = {}) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const gasPrice = await getGasPrice(config, parameters)\n      return gasPrice ?? null\n    },\n    queryKey: getGasPriceQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetGasPriceQueryFnData,\n    GetGasPriceErrorType,\n    GetGasPriceData,\n    GetGasPriceQueryKey<config, chainId>\n  >\n}\n\nexport type GetGasPriceQueryFnData = GetGasPriceReturnType\n\nexport type GetGasPriceData = GetGasPriceQueryFnData\n\nexport function getGasPriceQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetGasPriceOptions<config, chainId> = {}) {\n  return ['gasPrice', filterQueryOptions(options)] as const\n}\n\nexport type GetGasPriceQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getGasPriceQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetProofErrorType,\n  type GetProofParameters,\n  type GetProofReturnType,\n  getProof,\n} from '../actions/getProof.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetProofOptions<config extends Config> = Compute<\n  ExactPartial<GetProofParameters<config>> & ScopeKeyParameter\n>\n\nexport function getProofQueryOptions<config extends Config>(\n  config: config,\n  options: GetProofOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, scopeKey: _, storageKeys, ...parameters } = queryKey[1]\n      if (!address || !storageKeys)\n        throw new Error('address and storageKeys are required')\n      return getProof(config, { ...parameters, address, storageKeys })\n    },\n    queryKey: getProofQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetProofQueryFnData,\n    GetProofErrorType,\n    GetProofData,\n    GetProofQueryKey<config>\n  >\n}\n\nexport type GetProofQueryFnData = GetProofReturnType\n\nexport type GetProofData = GetProofQueryFnData\n\nexport function getProofQueryKey<config extends Config>(\n  options: GetProofOptions<config>,\n) {\n  return ['getProof', filterQueryOptions(options)] as const\n}\n\nexport type GetProofQueryKey<config extends Config> = ReturnType<\n  typeof getProofQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetStorageAtErrorType,\n  type GetStorageAtParameters,\n  type GetStorageAtReturnType,\n  getStorageAt,\n} from '../actions/getStorageAt.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetStorageAtOptions<config extends Config> = Compute<\n  ExactPartial<GetStorageAtParameters<config>> & ScopeKeyParameter\n>\n\nexport function getStorageAtQueryOptions<config extends Config>(\n  config: config,\n  options: GetStorageAtOptions<config> = {},\n) {\n  return {\n    queryFn({ queryKey }) {\n      const { address, slot, scopeKey: _, ...parameters } = queryKey[1]\n      if (!address || !slot) throw new Error('address and slot are required')\n      return getStorageAt(config, { ...parameters, address, slot })\n    },\n    queryKey: getStorageAtQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetStorageAtQueryFnData,\n    GetStorageAtErrorType,\n    GetStorageAtData,\n    GetStorageAtQueryKey<config>\n  >\n}\n\nexport type GetStorageAtQueryFnData = GetStorageAtReturnType\n\nexport type GetStorageAtData = GetStorageAtQueryFnData\n\nexport function getStorageAtQueryKey<config extends Config>(\n  options: GetStorageAtOptions<config>,\n) {\n  return ['getStorageAt', filterQueryOptions(options)] as const\n}\n\nexport type GetStorageAtQueryKey<config extends Config> = ReturnType<\n  typeof getStorageAtQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetTokenErrorType,\n  type GetTokenParameters,\n  type GetTokenReturnType,\n  getToken,\n} from '../actions/getToken.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetTokenOptions<config extends Config> = Compute<\n  ExactPartial<GetTokenParameters<config>> & ScopeKeyParameter\n>\n\nexport function getTokenQueryOptions<config extends Config>(\n  config: config,\n  options: GetTokenOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, scopeKey: _, ...parameters } = queryKey[1]\n      if (!address) throw new Error('address is required')\n      return getToken(config, { ...parameters, address })\n    },\n    queryKey: getTokenQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetTokenQueryFnData,\n    GetTokenErrorType,\n    GetTokenData,\n    GetTokenQueryKey<config>\n  >\n}\n\nexport type GetTokenQueryFnData = GetTokenReturnType\n\nexport type GetTokenData = GetTokenQueryFnData\n\nexport function getTokenQueryKey<config extends Config>(\n  options: GetTokenOptions<config> = {},\n) {\n  return ['token', filterQueryOptions(options)] as const\n}\n\nexport type GetTokenQueryKey<config extends Config> = ReturnType<\n  typeof getTokenQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetTransactionErrorType,\n  type GetTransactionParameters,\n  type GetTransactionReturnType,\n  getTransaction,\n} from '../actions/getTransaction.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetTransactionOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<GetTransactionParameters<config, chainId>> & ScopeKeyParameter\n>\n\nexport function getTransactionQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetTransactionOptions<config, chainId> = {}) {\n  return {\n    async queryFn({ queryKey }) {\n      const { blockHash, blockNumber, blockTag, hash, index } = queryKey[1]\n      if (!blockHash && !blockNumber && !blockTag && !hash)\n        throw new Error('blockHash, blockNumber, blockTag, or hash is required')\n      if (!hash && !index)\n        throw new Error(\n          'index is required for blockHash, blockNumber, or blockTag',\n        )\n      const { scopeKey: _, ...rest } = queryKey[1]\n      return getTransaction(\n        config,\n        rest as GetTransactionParameters,\n      ) as unknown as Promise<GetTransactionQueryFnData<config, chainId>>\n    },\n    queryKey: getTransactionQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetTransactionQueryFnData<config, chainId>,\n    GetTransactionErrorType,\n    GetTransactionData<config, chainId>,\n    GetTransactionQueryKey<config, chainId>\n  >\n}\n\nexport type GetTransactionQueryFnData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetTransactionReturnType<config, chainId>\n\nexport type GetTransactionData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetTransactionQueryFnData<config, chainId>\n\nexport function getTransactionQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetTransactionOptions<config, chainId> = {}) {\n  return ['transaction', filterQueryOptions(options)] as const\n}\n\nexport type GetTransactionQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getTransactionQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetTransactionConfirmationsErrorType,\n  type GetTransactionConfirmationsParameters,\n  type GetTransactionConfirmationsReturnType,\n  getTransactionConfirmations,\n} from '../actions/getTransactionConfirmations.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { UnionExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetTransactionConfirmationsOptions<\n  config extends Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n> = UnionExactPartial<GetTransactionConfirmationsParameters<config, chainId>> &\n  ScopeKeyParameter\n\nexport function getTransactionConfirmationsQueryOptions<\n  config extends Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n>(\n  config: config,\n  options: GetTransactionConfirmationsOptions<config, chainId> = {} as any,\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const {\n        hash,\n        transactionReceipt,\n        scopeKey: _,\n        ...parameters\n      } = queryKey[1]\n      if (!hash && !transactionReceipt)\n        throw new Error('hash or transactionReceipt is required')\n\n      const confirmations = await getTransactionConfirmations(config, {\n        hash,\n        transactionReceipt,\n        ...(parameters as any),\n      })\n      return confirmations ?? null\n    },\n    queryKey: getTransactionConfirmationsQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetTransactionConfirmationsQueryFnData,\n    GetTransactionConfirmationsErrorType,\n    GetTransactionConfirmationsData,\n    GetTransactionConfirmationsQueryKey<config, chainId>\n  >\n}\n\nexport type GetTransactionConfirmationsQueryFnData =\n  GetTransactionConfirmationsReturnType\n\nexport type GetTransactionConfirmationsData =\n  GetTransactionConfirmationsQueryFnData\n\nexport function getTransactionConfirmationsQueryKey<\n  config extends Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n>(options: GetTransactionConfirmationsOptions<config, chainId> = {} as any) {\n  return ['transactionConfirmations', filterQueryOptions(options)] as const\n}\n\nexport type GetTransactionConfirmationsQueryKey<\n  config extends Config,\n  chainId extends\n    | config['chains'][number]['id']\n    | undefined = config['chains'][number]['id'],\n> = ReturnType<typeof getTransactionConfirmationsQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetTransactionCountErrorType,\n  type GetTransactionCountParameters,\n  type GetTransactionCountReturnType,\n  getTransactionCount,\n} from '../actions/getTransactionCount.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, PartialBy } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetTransactionCountOptions<config extends Config> = Compute<\n  PartialBy<GetTransactionCountParameters<config>, 'address'> &\n    ScopeKeyParameter\n>\n\nexport function getTransactionCountQueryOptions<config extends Config>(\n  config: config,\n  options: GetTransactionCountOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, scopeKey: _, ...parameters } = queryKey[1]\n      if (!address) throw new Error('address is required')\n      const transactionCount = await getTransactionCount(config, {\n        ...(parameters as GetTransactionCountParameters),\n        address,\n      })\n      return transactionCount ?? null\n    },\n    queryKey: getTransactionCountQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetTransactionCountQueryFnData,\n    GetTransactionCountErrorType,\n    GetTransactionCountData,\n    GetTransactionCountQueryKey<config>\n  >\n}\n\nexport type GetTransactionCountQueryFnData =\n  Compute<GetTransactionCountReturnType>\n\nexport type GetTransactionCountData = GetTransactionCountQueryFnData\n\nexport function getTransactionCountQueryKey<config extends Config>(\n  options: GetTransactionCountOptions<config> = {},\n) {\n  return ['transactionCount', filterQueryOptions(options)] as const\n}\n\nexport type GetTransactionCountQueryKey<config extends Config> = ReturnType<\n  typeof getTransactionCountQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetTransactionReceiptErrorType,\n  type GetTransactionReceiptParameters,\n  getTransactionReceipt,\n} from '../actions/getTransactionReceipt.js'\nimport type { GetTransactionReceiptReturnType } from '../actions/getTransactionReceipt.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetTransactionReceiptOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<GetTransactionReceiptParameters<config, chainId>> &\n    ScopeKeyParameter\n>\n\nexport function getTransactionReceiptQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetTransactionReceiptOptions<config, chainId> = {}) {\n  return {\n    queryFn({ queryKey }) {\n      const { hash, scopeKey: _, ...parameters } = queryKey[1]\n      if (!hash) throw new Error('hash is required')\n      return getTransactionReceipt(config, { ...parameters, hash })\n    },\n    queryKey: getTransactionReceiptQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetTransactionReceiptQueryFnData<config, chainId>,\n    GetTransactionReceiptErrorType,\n    GetTransactionReceiptData<config, chainId>,\n    GetTransactionReceiptQueryKey<config, chainId>\n  >\n}\nexport type GetTransactionReceiptQueryFnData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetTransactionReceiptReturnType<config, chainId>\n\nexport type GetTransactionReceiptData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetTransactionReceiptQueryFnData<config, chainId>\n\nexport function getTransactionReceiptQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetTransactionReceiptOptions<config, chainId>) {\n  return ['getTransactionReceipt', filterQueryOptions(options)] as const\n}\n\nexport type GetTransactionReceiptQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getTransactionReceiptQueryKey<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type GetWalletClientErrorType,\n  type GetWalletClientParameters,\n  type GetWalletClientReturnType,\n  getWalletClient,\n} from '../actions/getWalletClient.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type GetWalletClientOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<GetWalletClientParameters<config, chainId>> & ScopeKeyParameter\n>\n\nexport function getWalletClientQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(config: config, options: GetWalletClientOptions<config, chainId> = {}) {\n  return {\n    gcTime: 0,\n    async queryFn({ queryKey }) {\n      const { connector } = options\n      const { connectorUid: _, scopeKey: _s, ...parameters } = queryKey[1]\n      return getWalletClient(config, { ...parameters, connector })\n    },\n    queryKey: getWalletClientQueryKey(options),\n  } as const satisfies QueryOptions<\n    GetWalletClientQueryFnData<config, chainId>,\n    GetWalletClientErrorType,\n    GetWalletClientData<config, chainId>,\n    GetWalletClientQueryKey<config, chainId>\n  >\n}\n\nexport type GetWalletClientQueryFnData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetWalletClientReturnType<config, chainId>\n\nexport type GetWalletClientData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = GetWalletClientQueryFnData<config, chainId>\n\nexport function getWalletClientQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: GetWalletClientOptions<config, chainId> = {}) {\n  const { connector, ...parameters } = options\n  return [\n    'walletClient',\n    { ...filterQueryOptions(parameters), connectorUid: connector?.uid },\n  ] as const\n}\n\nexport type GetWalletClientQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof getWalletClientQueryKey<config, chainId>>\n", "import type { ContractFunctionParameters } from 'viem'\nimport {\n  type ReadContractsErrorType,\n  type ReadContractsParameters,\n  type ReadContractsReturnType,\n  readContracts,\n} from '../actions/readContracts.js'\nimport type { Config } from '../createConfig.js'\nimport type {\n  ChainIdParameter,\n  ScopeKeyParameter,\n} from '../types/properties.js'\nimport type { StrictOmit } from '../types/utils.js'\nimport type { InfiniteQueryOptions } from './types.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type InfiniteReadContractsOptions<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n  pageParam,\n  config extends Config,\n> = {\n  cacheKey: string\n  contracts(\n    pageParam: pageParam,\n  ): ReadContractsParameters<contracts, allowFailure, config>['contracts']\n} & StrictOmit<\n  ReadContractsParameters<contracts, allowFailure, config>,\n  'contracts'\n> &\n  ScopeKeyParameter\n\nexport function infiniteReadContractsQueryOptions<\n  config extends Config,\n  const contracts extends readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  pageParam = unknown,\n>(\n  config: config,\n  options: InfiniteReadContractsOptions<\n    contracts,\n    allowFailure,\n    pageParam,\n    config\n  > &\n    ChainIdParameter<config> &\n    RequiredPageParamsParameters<contracts, allowFailure, pageParam>,\n) {\n  return {\n    ...options.query,\n    async queryFn({ pageParam, queryKey }) {\n      const { contracts } = options\n      const { cacheKey: _, scopeKey: _s, ...parameters } = queryKey[1]\n      return (await readContracts(config, {\n        ...parameters,\n        contracts: contracts(pageParam as any),\n      })) as ReadContractsReturnType<contracts, allowFailure>\n    },\n    queryKey: infiniteReadContractsQueryKey(options),\n  } as const satisfies InfiniteQueryOptions<\n    InfiniteReadContractsQueryFnData<contracts, allowFailure>,\n    ReadContractsErrorType,\n    InfiniteReadContractsData<contracts, allowFailure>,\n    InfiniteReadContractsData<contracts, allowFailure>,\n    InfiniteReadContractsQueryKey<contracts, allowFailure, pageParam, config>,\n    pageParam\n  >\n}\n\ntype RequiredPageParamsParameters<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n  pageParam,\n> = {\n  query: {\n    initialPageParam: pageParam\n    getNextPageParam(\n      lastPage: InfiniteReadContractsQueryFnData<contracts, allowFailure>,\n      allPages: InfiniteReadContractsQueryFnData<contracts, allowFailure>[],\n      lastPageParam: pageParam,\n      allPageParams: pageParam[],\n    ): pageParam | undefined | null\n  }\n}\n\nexport type InfiniteReadContractsQueryFnData<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n> = ReadContractsReturnType<contracts, allowFailure>\n\nexport type InfiniteReadContractsData<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n> = InfiniteReadContractsQueryFnData<contracts, allowFailure>\n\nexport function infiniteReadContractsQueryKey<\n  config extends Config,\n  const contracts extends readonly unknown[],\n  allowFailure extends boolean,\n  pageParam,\n>(\n  options: InfiniteReadContractsOptions<\n    contracts,\n    allowFailure,\n    pageParam,\n    config\n  > &\n    ChainIdParameter<config> &\n    RequiredPageParamsParameters<contracts, allowFailure, pageParam>,\n) {\n  const { contracts: _, query: _q, ...parameters } = options\n  return ['infiniteReadContracts', filterQueryOptions(parameters)] as const\n}\n\nexport type InfiniteReadContractsQueryKey<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n  pageParam,\n  config extends Config,\n> = ReturnType<\n  typeof infiniteReadContractsQueryKey<\n    config,\n    contracts,\n    allowFailure,\n    pageParam\n  >\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport type { PrepareTransactionRequestRequest as viem_PrepareTransactionRequestRequest } from 'viem'\n\nimport {\n  type PrepareTransactionRequestErrorType,\n  type PrepareTransactionRequestParameters,\n  type PrepareTransactionRequestReturnType,\n  prepareTransactionRequest,\n} from '../actions/prepareTransactionRequest.js'\nimport type { Config } from '../createConfig.js'\nimport type { SelectChains } from '../types/chain.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { UnionExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type PrepareTransactionRequestOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n> = UnionExactPartial<\n  PrepareTransactionRequestParameters<config, chainId, request>\n> &\n  ScopeKeyParameter\n\nexport function prepareTransactionRequestQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n>(\n  config: config,\n  options: PrepareTransactionRequestOptions<\n    config,\n    chainId,\n    request\n  > = {} as any,\n) {\n  return {\n    queryFn({ queryKey }) {\n      const { scopeKey: _, to, ...parameters } = queryKey[1]\n      if (!to) throw new Error('to is required')\n      return prepareTransactionRequest(config, {\n        to,\n        ...(parameters as any),\n      }) as unknown as Promise<\n        PrepareTransactionRequestQueryFnData<config, chainId, request>\n      >\n    },\n    queryKey: prepareTransactionRequestQueryKey(options),\n  } as const satisfies QueryOptions<\n    PrepareTransactionRequestQueryFnData<config, chainId, request>,\n    PrepareTransactionRequestErrorType,\n    PrepareTransactionRequestData<config, chainId, request>,\n    PrepareTransactionRequestQueryKey<config, chainId, request>\n  >\n}\nexport type PrepareTransactionRequestQueryFnData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n> = PrepareTransactionRequestReturnType<config, chainId, request>\n\nexport type PrepareTransactionRequestData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n> = PrepareTransactionRequestQueryFnData<config, chainId, request>\n\nexport function prepareTransactionRequestQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n>(options: PrepareTransactionRequestOptions<config, chainId, request>) {\n  return ['prepareTransactionRequest', filterQueryOptions(options)] as const\n}\n\nexport type PrepareTransactionRequestQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n> = ReturnType<\n  typeof prepareTransactionRequestQueryKey<config, chainId, request>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\nimport type { Abi, ContractFunctionArgs, ContractFunctionName } from 'viem'\n\nimport {\n  type ReadContractErrorType,\n  type ReadContractParameters,\n  type ReadContractReturnType,\n  readContract,\n} from '../actions/readContract.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { UnionExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type ReadContractOptions<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n  config extends Config,\n> = UnionExactPartial<ReadContractParameters<abi, functionName, args, config>> &\n  ScopeKeyParameter\n\nexport function readContractQueryOptions<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n>(\n  config: config,\n  options: ReadContractOptions<abi, functionName, args, config> = {} as any,\n) {\n  return {\n    // TODO: Support `signal` once Viem actions allow passthrough\n    // https://tkdodo.eu/blog/why-you-want-react-query#bonus-cancellation\n    async queryFn({ queryKey }) {\n      const abi = options.abi as Abi\n      if (!abi) throw new Error('abi is required')\n\n      const { functionName, scopeKey: _, ...parameters } = queryKey[1]\n      const addressOrCodeParams = (() => {\n        const params = queryKey[1] as unknown as ReadContractParameters\n        if (params.address) return { address: params.address }\n        if (params.code) return { code: params.code }\n        throw new Error('address or code is required')\n      })()\n\n      if (!functionName) throw new Error('functionName is required')\n\n      return readContract(config, {\n        abi,\n        functionName,\n        args: parameters.args as readonly unknown[],\n        ...addressOrCodeParams,\n        ...parameters,\n      }) as Promise<ReadContractData<abi, functionName, args>>\n    },\n    queryKey: readContractQueryKey(options as any) as any,\n  } as const satisfies QueryOptions<\n    ReadContractQueryFnData<abi, functionName, args>,\n    ReadContractErrorType,\n    ReadContractData<abi, functionName, args>,\n    ReadContractQueryKey<abi, functionName, args, config>\n  >\n}\n\nexport type ReadContractQueryFnData<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n> = ReadContractReturnType<abi, functionName, args>\n\nexport type ReadContractData<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n> = ReadContractQueryFnData<abi, functionName, args>\n\nexport function readContractQueryKey<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n>(options: ReadContractOptions<abi, functionName, args, config> = {} as any) {\n  const { abi: _, ...rest } = options\n  return ['readContract', filterQueryOptions(rest)] as const\n}\n\nexport type ReadContractQueryKey<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n  config extends Config,\n> = ReturnType<typeof readContractQueryKey<config, abi, functionName, args>>\n", "import type { QueryOptions } from '@tanstack/query-core'\nimport type {\n  ContractFunctionParameters,\n  MulticallParameters as viem_MulticallParameters,\n} from 'viem'\n\nimport {\n  type ReadContractsErrorType,\n  type ReadContractsReturnType,\n  readContracts,\n} from '../actions/readContracts.js'\nimport type { Config } from '../createConfig.js'\nimport type { ChainIdParameter } from '../types/properties.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type ReadContractsOptions<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n  config extends Config,\n> = ExactPartial<\n  viem_MulticallParameters<\n    contracts,\n    allowFailure,\n    { optional: true; properties: ChainIdParameter<config> }\n  >\n> &\n  ScopeKeyParameter\n\nexport function readContractsQueryOptions<\n  config extends Config,\n  const contracts extends readonly unknown[],\n  allowFailure extends boolean = true,\n>(\n  config: config,\n  options: ReadContractsOptions<contracts, allowFailure, config> &\n    ChainIdParameter<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const contracts: ContractFunctionParameters[] = []\n      const length = queryKey[1].contracts.length\n      for (let i = 0; i < length; i++) {\n        const contract = queryKey[1].contracts[i]!\n        const abi = (options.contracts?.[i] as ContractFunctionParameters).abi\n        contracts.push({ ...contract, abi })\n      }\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      return readContracts(config, {\n        ...parameters,\n        contracts,\n      }) as Promise<ReadContractsReturnType<contracts, allowFailure>>\n    },\n    queryKey: readContractsQueryKey(options),\n  } as const satisfies QueryOptions<\n    ReadContractsQueryFnData<contracts, allowFailure>,\n    ReadContractsErrorType,\n    ReadContractsData<contracts, allowFailure>,\n    ReadContractsQueryKey<contracts, allowFailure, config>\n  >\n}\n\nexport type ReadContractsQueryFnData<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n> = ReadContractsReturnType<contracts, allowFailure>\n\nexport type ReadContractsData<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n> = ReadContractsQueryFnData<contracts, allowFailure>\n\nexport function readContractsQueryKey<\n  config extends Config,\n  const contracts extends readonly unknown[],\n  allowFailure extends boolean,\n>(\n  options: ReadContractsOptions<contracts, allowFailure, config> &\n    ChainIdParameter<config> = {},\n) {\n  const contracts = []\n  for (const contract of (options.contracts ??\n    []) as (ContractFunctionParameters & { chainId: number })[]) {\n    const { abi: _, ...rest } = contract\n    contracts.push({ ...rest, chainId: rest.chainId ?? options.chainId })\n  }\n  return [\n    'readContracts',\n    filterQueryOptions({ ...options, contracts }),\n  ] as const\n}\n\nexport type ReadContractsQueryKey<\n  contracts extends readonly unknown[],\n  allowFailure extends boolean,\n  config extends Config,\n> = ReturnType<typeof readContractsQueryKey<config, contracts, allowFailure>>\n", "import type { MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type ReconnectErrorType,\n  type ReconnectParameters,\n  type ReconnectReturnType,\n  reconnect,\n} from '../actions/reconnect.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\nimport type { Mutate, MutateAsync } from './types.js'\n\nexport function reconnectMutationOptions(config: Config) {\n  return {\n    mutationFn(variables) {\n      return reconnect(config, variables)\n    },\n    mutationKey: ['reconnect'],\n  } as const satisfies MutationOptions<\n    ReconnectData,\n    ReconnectErrorType,\n    ReconnectVariables\n  >\n}\n\nexport type ReconnectData = Compute<ReconnectReturnType>\n\nexport type ReconnectVariables = ReconnectParameters | undefined\n\nexport type ReconnectMutate<context = unknown> = Mutate<\n  ReconnectData,\n  ReconnectErrorType,\n  ReconnectVariables,\n  context\n>\n\nexport type ReconnectMutateAsync<context = unknown> = MutateAsync<\n  ReconnectData,\n  ReconnectErrorType,\n  ReconnectVariables,\n  context\n>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type SendCallsErrorType,\n  type SendCallsParameters,\n  type SendCallsReturnType,\n  sendCalls,\n} from '../actions/sendCalls.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function sendCallsMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return sendCalls(config, variables)\n    },\n    mutationKey: ['sendCalls'],\n  } as const satisfies MutationOptions<\n    SendCallsData,\n    SendCallsErrorType,\n    SendCallsVariables<config, config['chains'][number]['id']>\n  >\n}\n\nexport type SendCallsData = Compute<SendCallsReturnType>\n\nexport type SendCallsVariables<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = SendCallsParameters<config, chainId>\n\nexport type SendCallsMutate<config extends Config, context = unknown> = <\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: SendCallsVariables<config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          SendCallsData,\n          SendCallsErrorType,\n          Compute<SendCallsVariables<config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => void\n\nexport type SendCallsMutateAsync<config extends Config, context = unknown> = <\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: SendCallsVariables<config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          SendCallsData,\n          SendCallsErrorType,\n          Compute<SendCallsVariables<config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => Promise<SendCallsData>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type ShowCallsStatusErrorType,\n  type ShowCallsStatusParameters,\n  type ShowCallsStatusReturnType,\n  showCallsStatus,\n} from '../actions/showCallsStatus.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function showCallsStatusMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return showCallsStatus(config, variables)\n    },\n    mutationKey: ['showCallsStatus'],\n  } as const satisfies MutationOptions<\n    ShowCallsStatusData,\n    ShowCallsStatusErrorType,\n    ShowCallsStatusVariables\n  >\n}\n\nexport type ShowCallsStatusData = Compute<ShowCallsStatusReturnType>\n\nexport type ShowCallsStatusVariables = ShowCallsStatusParameters\n\nexport type ShowCallsStatusMutate<context = unknown> = (\n  variables: ShowCallsStatusVariables,\n  options?:\n    | Compute<\n        MutateOptions<\n          ShowCallsStatusData,\n          ShowCallsStatusErrorType,\n          Compute<ShowCallsStatusVariables>,\n          context\n        >\n      >\n    | undefined,\n) => void\n\nexport type ShowCallsStatusMutateAsync<context = unknown> = (\n  variables: ShowCallsStatusVariables,\n  options?:\n    | Compute<\n        MutateOptions<\n          ShowCallsStatusData,\n          ShowCallsStatusErrorType,\n          Compute<ShowCallsStatusVariables>,\n          context\n        >\n      >\n    | undefined,\n) => Promise<ShowCallsStatusData>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type SendTransactionErrorType,\n  type SendTransactionParameters,\n  type SendTransactionReturnType,\n  sendTransaction,\n} from '../actions/sendTransaction.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function sendTransactionMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return sendTransaction(config, variables)\n    },\n    mutationKey: ['sendTransaction'],\n  } as const satisfies MutationOptions<\n    SendTransactionData,\n    SendTransactionErrorType,\n    SendTransactionVariables<config, config['chains'][number]['id']>\n  >\n}\n\nexport type SendTransactionData = Compute<SendTransactionReturnType>\n\nexport type SendTransactionVariables<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = SendTransactionParameters<config, chainId>\n\nexport type SendTransactionMutate<config extends Config, context = unknown> = <\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: SendTransactionVariables<config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          SendTransactionData,\n          SendTransactionErrorType,\n          Compute<SendTransactionVariables<config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => void\n\nexport type SendTransactionMutateAsync<\n  config extends Config,\n  context = unknown,\n> = <chainId extends config['chains'][number]['id']>(\n  variables: SendTransactionVariables<config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          SendTransactionData,\n          SendTransactionErrorType,\n          Compute<SendTransactionVariables<config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => Promise<SendTransactionData>\n", "import type { MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type SignMessageErrorType,\n  type SignMessageParameters,\n  type SignMessageReturnType,\n  signMessage,\n} from '../actions/signMessage.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\nimport type { Mutate, MutateAsync } from './types.js'\n\nexport function signMessageMutationOptions(config: Config) {\n  return {\n    mutationFn(variables) {\n      return signMessage(config, variables)\n    },\n    mutationKey: ['signMessage'],\n  } as const satisfies MutationOptions<\n    SignMessageData,\n    SignMessageErrorType,\n    SignMessageVariables\n  >\n}\n\nexport type SignMessageData = SignMessageReturnType\n\nexport type SignMessageVariables = Compute<SignMessageParameters>\n\nexport type SignMessageMutate<context = unknown> = Mutate<\n  SignMessageData,\n  SignMessageErrorType,\n  SignMessageVariables,\n  context\n>\n\nexport type SignMessageMutateAsync<context = unknown> = MutateAsync<\n  SignMessageData,\n  SignMessageErrorType,\n  SignMessageVariables,\n  context\n>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\n\nimport type { TypedData } from 'viem'\nimport {\n  type SignTypedDataErrorType,\n  type SignTypedDataParameters,\n  type SignTypedDataReturnType,\n  signTypedData,\n} from '../actions/signTypedData.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function signTypedDataMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return signTypedData(config, variables)\n    },\n    mutationKey: ['signTypedData'],\n  } as const satisfies MutationOptions<\n    SignTypedDataData,\n    SignTypedDataErrorType,\n    SignTypedDataVariables\n  >\n}\n\nexport type SignTypedDataData = Compute<SignTypedDataReturnType>\n\nexport type SignTypedDataVariables<\n  typedData extends TypedData | Record<string, unknown> = TypedData,\n  primaryType extends keyof typedData | 'EIP712Domain' = keyof typedData,\n  ///\n  primaryTypes = typedData extends TypedData ? keyof typedData : string,\n> = SignTypedDataParameters<typedData, primaryType, primaryTypes>\n\nexport type SignTypedDataMutate<context = unknown> = <\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(\n  variables: SignTypedDataVariables<typedData, primaryType>,\n  options?:\n    | MutateOptions<\n        SignTypedDataData,\n        SignTypedDataErrorType,\n        SignTypedDataVariables<\n          typedData,\n          primaryType,\n          // use `primaryType` to make sure it's not union of all possible primary types\n          primaryType\n        >,\n        context\n      >\n    | undefined,\n) => void\n\nexport type SignTypedDataMutateAsync<context = unknown> = <\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(\n  variables: SignTypedDataVariables<typedData, primaryType>,\n  options?:\n    | MutateOptions<\n        SignTypedDataData,\n        SignTypedDataErrorType,\n        SignTypedDataVariables<\n          typedData,\n          primaryType,\n          // use `primaryType` to make sure it's not union of all possible primary types\n          primaryType\n        >,\n        context\n      >\n    | undefined,\n) => Promise<SignTypedDataData>\n", "import type { MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type SwitchAccountErrorType,\n  type SwitchAccountParameters,\n  type SwitchAccountReturnType,\n  switchAccount,\n} from '../actions/switchAccount.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\nimport type { Mutate, MutateAsync } from './types.js'\n\nexport function switchAccountMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return switchAccount(config, variables)\n    },\n    mutationKey: ['switchAccount'],\n  } as const satisfies MutationOptions<\n    SwitchAccountData<config>,\n    SwitchAccountErrorType,\n    SwitchAccountVariables\n  >\n}\n\nexport type SwitchAccountData<config extends Config> = Compute<\n  SwitchAccountReturnType<config>\n>\n\nexport type SwitchAccountVariables = Compute<SwitchAccountParameters>\n\nexport type SwitchAccountMutate<\n  config extends Config,\n  context = unknown,\n> = Mutate<\n  SwitchAccountData<config>,\n  SwitchAccountErrorType,\n  SwitchAccountVariables,\n  context\n>\n\nexport type SwitchAccountMutateAsync<\n  config extends Config,\n  context = unknown,\n> = MutateAsync<\n  SwitchAccountData<config>,\n  SwitchAccountErrorType,\n  SwitchAccountVariables,\n  context\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\nimport type { Abi, ContractFunctionArgs, ContractFunctionName } from 'viem'\n\nimport {\n  type SimulateContractErrorType,\n  type SimulateContractParameters,\n  type SimulateContractReturnType,\n  simulateContract,\n} from '../actions/simulateContract.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { UnionExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type SimulateContractOptions<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n> = UnionExactPartial<\n  SimulateContractParameters<abi, functionName, args, config, chainId>\n> &\n  ScopeKeyParameter\n\nexport function simulateContractQueryOptions<\n  config extends Config,\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  chainId extends config['chains'][number]['id'] | undefined,\n>(\n  config: config,\n  options: SimulateContractOptions<\n    abi,\n    functionName,\n    args,\n    config,\n    chainId\n  > = {} as any,\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { abi, connector } = options\n      if (!abi) throw new Error('abi is required')\n      const { scopeKey: _, ...parameters } = queryKey[1]\n      const { address, functionName } = parameters\n      if (!address) throw new Error('address is required')\n      if (!functionName) throw new Error('functionName is required')\n      return simulateContract(config, {\n        abi,\n        connector,\n        ...(parameters as any),\n      })\n    },\n    queryKey: simulateContractQueryKey(options),\n  } as const satisfies QueryOptions<\n    SimulateContractQueryFnData<abi, functionName, args, config, chainId>,\n    SimulateContractErrorType,\n    SimulateContractData<abi, functionName, args, config, chainId>,\n    SimulateContractQueryKey<abi, functionName, args, config, chainId>\n  >\n}\n\nexport type SimulateContractQueryFnData<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n> = SimulateContractReturnType<abi, functionName, args, config, chainId>\n\nexport type SimulateContractData<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n> = SimulateContractQueryFnData<abi, functionName, args, config, chainId>\n\nexport function simulateContractQueryKey<\n  config extends Config,\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  chainId extends config['chains'][number]['id'] | undefined,\n>(\n  options: SimulateContractOptions<\n    abi,\n    functionName,\n    args,\n    config,\n    chainId\n  > = {} as any,\n) {\n  const { abi: _, connector: _c, ...rest } = options\n  return ['simulateContract', filterQueryOptions(rest)] as const\n}\n\nexport type SimulateContractQueryKey<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  config extends Config,\n  chainId extends config['chains'][number]['id'] | undefined,\n> = ReturnType<\n  typeof simulateContractQueryKey<config, abi, functionName, args, chainId>\n>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type SwitchChainErrorType,\n  type SwitchChainParameters,\n  type SwitchChainReturnType,\n  switchChain,\n} from '../actions/switchChain.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function switchChainMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return switchChain(config, variables)\n    },\n    mutationKey: ['switchChain'],\n  } as const satisfies MutationOptions<\n    SwitchChainData<config, config['chains'][number]['id']>,\n    SwitchChainErrorType,\n    SwitchChainVariables<config, config['chains'][number]['id']>\n  >\n}\n\nexport type SwitchChainData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<SwitchChainReturnType<config, chainId>>\n\nexport type SwitchChainVariables<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<SwitchChainParameters<config, chainId>>\n\nexport type SwitchChainMutate<config extends Config, context = unknown> = <\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: SwitchChainVariables<config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          SwitchChainData<config, chainId>,\n          SwitchChainErrorType,\n          Compute<SwitchChainVariables<config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => void\n\nexport type SwitchChainMutateAsync<config extends Config, context = unknown> = <\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: SwitchChainVariables<config, chainId>,\n  options?:\n    | Compute<\n        MutateOptions<\n          SwitchChainData<config, chainId>,\n          SwitchChainErrorType,\n          Compute<SwitchChainVariables<config, chainId>>,\n          context\n        >\n      >\n    | undefined,\n) => Promise<SwitchChainData<config, chainId>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type VerifyMessageErrorType,\n  type VerifyMessageParameters,\n  type VerifyMessageReturnType,\n  verifyMessage,\n} from '../actions/verifyMessage.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type VerifyMessageOptions<config extends Config> = Compute<\n  ExactPartial<VerifyMessageParameters<config>> & ScopeKeyParameter\n>\n\nexport function verifyMessageQueryOptions<config extends Config>(\n  config: config,\n  options: VerifyMessageOptions<config> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { address, message, signature } = queryKey[1]\n      if (!address || !message || !signature)\n        throw new Error('address, message, and signature are required')\n\n      const { scopeKey: _, ...parameters } = queryKey[1]\n\n      const verified = await verifyMessage(\n        config,\n        parameters as VerifyMessageParameters,\n      )\n      return verified ?? null\n    },\n    queryKey: verifyMessageQueryKey(options),\n  } as const satisfies QueryOptions<\n    VerifyMessageQueryFnData,\n    VerifyMessageErrorType,\n    VerifyMessageData,\n    VerifyMessageQueryKey<config>\n  >\n}\nexport type VerifyMessageQueryFnData = VerifyMessageReturnType\n\nexport type VerifyMessageData = VerifyMessageQueryFnData\n\nexport function verifyMessageQueryKey<config extends Config>(\n  options: VerifyMessageOptions<config>,\n) {\n  return ['verifyMessage', filterQueryOptions(options)] as const\n}\n\nexport type VerifyMessageQueryKey<config extends Config> = ReturnType<\n  typeof verifyMessageQueryKey<config>\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\nimport type { TypedData } from 'viem'\n\nimport {\n  type VerifyTypedDataErrorType,\n  type VerifyTypedDataParameters,\n  type VerifyTypedDataReturnType,\n  verifyTypedData,\n} from '../actions/verifyTypedData.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type VerifyTypedDataOptions<\n  typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n  config extends Config,\n> = ExactPartial<VerifyTypedDataParameters<typedData, primaryType, config>> &\n  ScopeKeyParameter\n\nexport function verifyTypedDataQueryOptions<\n  config extends Config,\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(\n  config: config,\n  options: VerifyTypedDataOptions<typedData, primaryType, config> = {} as any,\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const {\n        address,\n        message,\n        primaryType,\n        signature,\n        types,\n        scopeKey: _,\n        ...parameters\n      } = queryKey[1]\n      if (!address) throw new Error('address is required')\n      if (!message) throw new Error('message is required')\n      if (!primaryType) throw new Error('primaryType is required')\n      if (!signature) throw new Error('signature is required')\n      if (!types) throw new Error('types is required')\n\n      const verified = await verifyTypedData(config, {\n        ...parameters,\n        address,\n        message,\n        primaryType,\n        signature,\n        types,\n      } as VerifyTypedDataParameters)\n      return verified ?? null\n    },\n    queryKey: verifyTypedDataQueryKey(options),\n  } as const satisfies QueryOptions<\n    VerifyTypedDataQueryFnData,\n    VerifyTypedDataErrorType,\n    VerifyTypedDataData,\n    VerifyTypedDataQueryKey<typedData, primaryType, config>\n  >\n}\n\nexport type VerifyTypedDataQueryFnData = VerifyTypedDataReturnType\n\nexport type VerifyTypedDataData = VerifyTypedDataQueryFnData\n\nexport function verifyTypedDataQueryKey<\n  config extends Config,\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(options: VerifyTypedDataOptions<typedData, primaryType, config>) {\n  return ['verifyTypedData', filterQueryOptions(options)] as const\n}\n\nexport type VerifyTypedDataQueryKey<\n  typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n  config extends Config,\n> = ReturnType<typeof verifyTypedDataQueryKey<config, typedData, primaryType>>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type WaitForCallsStatusErrorType,\n  type WaitForCallsStatusParameters,\n  type WaitForCallsStatusReturnType,\n  waitForCallsStatus,\n} from '../actions/waitForCallsStatus.js'\nimport type { Config } from '../createConfig.js'\nimport { ConnectorNotConnectedError } from '../errors/config.js'\nimport { filterQueryOptions } from '../query/utils.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, PartialBy } from '../types/utils.js'\n\nexport type WaitForCallsStatusOptions = Compute<\n  PartialBy<WaitForCallsStatusParameters, 'id'> & ScopeKeyParameter\n>\n\nexport function waitForCallsStatusQueryOptions<config extends Config>(\n  config: config,\n  options: WaitForCallsStatusOptions,\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { scopeKey: _, id, ...parameters } = queryKey[1]\n      if (!id) throw new Error('id is required')\n      const status = await waitForCallsStatus(config, { ...parameters, id })\n      return status\n    },\n    queryKey: waitForCallsStatusQueryKey(options),\n    retry(failureCount, error) {\n      if (error instanceof ConnectorNotConnectedError) return false\n      return failureCount < 3\n    },\n  } as const satisfies QueryOptions<\n    WaitForCallsStatusQueryFnData,\n    WaitForCallsStatusErrorType,\n    WaitForCallsStatusData,\n    WaitForCallsStatusQueryKey\n  >\n}\n\nexport type WaitForCallsStatusQueryFnData = WaitForCallsStatusReturnType\n\nexport type WaitForCallsStatusData = WaitForCallsStatusQueryFnData\n\nexport function waitForCallsStatusQueryKey(options: WaitForCallsStatusOptions) {\n  return ['callsStatus', filterQueryOptions(options)] as const\n}\n\nexport type WaitForCallsStatusQueryKey = ReturnType<\n  typeof waitForCallsStatusQueryKey\n>\n", "import type { QueryOptions } from '@tanstack/query-core'\n\nimport {\n  type WaitForTransactionReceiptErrorType,\n  type WaitForTransactionReceiptParameters,\n  type WaitForTransactionReceiptReturnType,\n  waitForTransactionReceipt,\n} from '../actions/waitForTransactionReceipt.js'\nimport type { Config } from '../createConfig.js'\nimport type { ScopeKeyParameter } from '../types/properties.js'\nimport type { Compute, ExactPartial } from '../types/utils.js'\nimport { filterQueryOptions } from './utils.js'\n\nexport type WaitForTransactionReceiptOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = Compute<\n  ExactPartial<WaitForTransactionReceiptParameters<config, chainId>> &\n    ScopeKeyParameter\n>\n\nexport function waitForTransactionReceiptQueryOptions<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(\n  config: config,\n  options: WaitForTransactionReceiptOptions<config, chainId> = {},\n) {\n  return {\n    async queryFn({ queryKey }) {\n      const { hash, ...parameters } = queryKey[1]\n      if (!hash) throw new Error('hash is required')\n      return waitForTransactionReceipt(config, {\n        ...parameters,\n        onReplaced: options.onReplaced,\n        hash,\n      }) as unknown as Promise<\n        WaitForTransactionReceiptReturnType<config, chainId>\n      >\n    },\n    queryKey: waitForTransactionReceiptQueryKey(options),\n  } as const satisfies QueryOptions<\n    WaitForTransactionReceiptQueryFnData<config, chainId>,\n    WaitForTransactionReceiptErrorType,\n    WaitForTransactionReceiptData<config, chainId>,\n    WaitForTransactionReceiptQueryKey<config, chainId>\n  >\n}\n\nexport type WaitForTransactionReceiptQueryFnData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = WaitForTransactionReceiptReturnType<config, chainId>\n\nexport type WaitForTransactionReceiptData<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = WaitForTransactionReceiptQueryFnData<config, chainId>\n\nexport function waitForTransactionReceiptQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n>(options: WaitForTransactionReceiptOptions<config, chainId> = {}) {\n  const { onReplaced: _, ...rest } = options\n  return ['waitForTransactionReceipt', filterQueryOptions(rest)] as const\n}\n\nexport type WaitForTransactionReceiptQueryKey<\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n> = ReturnType<typeof waitForTransactionReceiptQueryKey<config, chainId>>\n", "import type { MutationOptions } from '@tanstack/query-core'\n\nimport {\n  type WatchAssetErrorType,\n  type WatchAssetParameters,\n  type WatchAssetReturnType,\n  watchAsset,\n} from '../actions/watchAsset.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\nimport type { Mutate, MutateAsync } from './types.js'\n\nexport function watchAssetMutationOptions(config: Config) {\n  return {\n    mutationFn(variables) {\n      return watchAsset(config, variables)\n    },\n    mutationKey: ['watchAsset'],\n  } as const satisfies MutationOptions<\n    WatchAssetData,\n    WatchAssetErrorType,\n    WatchAssetVariables\n  >\n}\n\nexport type WatchAssetData = WatchAssetReturnType\n\nexport type WatchAssetVariables = Compute<WatchAssetParameters>\n\nexport type WatchAssetMutate<context = unknown> = Mutate<\n  WatchAssetData,\n  WatchAssetErrorType,\n  WatchAssetVariables,\n  context\n>\n\nexport type WatchAssetMutateAsync<context = unknown> = MutateAsync<\n  WatchAssetData,\n  WatchAssetErrorType,\n  WatchAssetVariables,\n  context\n>\n", "import type { MutateOptions, MutationOptions } from '@tanstack/query-core'\nimport type { Abi, ContractFunctionArgs, ContractFunctionName } from 'viem'\n\nimport {\n  type WriteContractErrorType,\n  type WriteContractParameters,\n  type WriteContractReturnType,\n  writeContract,\n} from '../actions/writeContract.js'\nimport type { Config } from '../createConfig.js'\nimport type { Compute } from '../types/utils.js'\n\nexport function writeContractMutationOptions<config extends Config>(\n  config: config,\n) {\n  return {\n    mutationFn(variables) {\n      return writeContract(config, variables)\n    },\n    mutationKey: ['writeContract'],\n  } as const satisfies MutationOptions<\n    WriteContractData,\n    WriteContractErrorType,\n    WriteContractVariables<\n      Abi,\n      string,\n      readonly unknown[],\n      config,\n      config['chains'][number]['id']\n    >\n  >\n}\n\nexport type WriteContractData = Compute<WriteContractReturnType>\n\nexport type WriteContractVariables<\n  abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  config extends Config,\n  chainId extends config['chains'][number]['id'],\n  ///\n  allFunctionNames = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n> = WriteContractParameters<\n  abi,\n  functionName,\n  args,\n  config,\n  chainId,\n  allFunctionNames\n>\n\nexport type WriteContractMutate<config extends Config, context = unknown> = <\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: WriteContractVariables<abi, functionName, args, config, chainId>,\n  options?:\n    | MutateOptions<\n        WriteContractData,\n        WriteContractErrorType,\n        WriteContractVariables<\n          abi,\n          functionName,\n          args,\n          config,\n          chainId,\n          // use `functionName` to make sure it's not union of all possible function names\n          functionName\n        >,\n        context\n      >\n    | undefined,\n) => void\n\nexport type WriteContractMutateAsync<\n  config extends Config,\n  context = unknown,\n> = <\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  chainId extends config['chains'][number]['id'],\n>(\n  variables: WriteContractVariables<abi, functionName, args, config, chainId>,\n  options?:\n    | MutateOptions<\n        WriteContractData,\n        WriteContractErrorType,\n        WriteContractVariables<\n          abi,\n          functionName,\n          args,\n          config,\n          chainId,\n          // use `functionName` to make sure it's not union of all possible function names\n          functionName\n        >,\n        context\n      >\n    | undefined,\n) => Promise<WriteContractData>\n", "import {\n  type DefaultError,\n  type QueryKey,\n  type UseInfiniteQueryOptions,\n  type UseInfiniteQueryResult,\n  type UseMutationOptions,\n  type UseMutationResult,\n  type UseQueryOptions,\n  type UseQueryResult,\n  useInfiniteQuery as tanstack_useInfiniteQuery,\n  useQuery as tanstack_useQuery,\n  useMutation,\n} from '@tanstack/react-query'\nimport type {\n  Compute,\n  ExactPartial,\n  Omit,\n  UnionStrictOmit,\n} from '@wagmi/core/internal'\nimport { hashFn } from '@wagmi/core/query'\n\nexport type UseMutationParameters<\n  data = unknown,\n  error = Error,\n  variables = void,\n  context = unknown,\n> = Compute<\n  Omit<\n    UseMutationOptions<data, error, Compute<variables>, context>,\n    'mutationFn' | 'mutationKey' | 'throwOnError'\n  >\n>\n\nexport type UseMutationReturnType<\n  data = unknown,\n  error = Error,\n  variables = void,\n  context = unknown,\n> = Compute<\n  UnionStrictOmit<\n    UseMutationResult<data, error, variables, context>,\n    'mutate' | 'mutateAsync'\n  >\n>\n\nexport { useMutation }\n\n////////////////////////////////////////////////////////////////////////////////\n\nexport type UseQueryParameters<\n  queryFnData = unknown,\n  error = DefaultError,\n  data = queryFnData,\n  queryKey extends QueryKey = QueryKey,\n> = Compute<\n  ExactPartial<\n    Omit<UseQueryOptions<queryFnData, error, data, queryKey>, 'initialData'>\n  > & {\n    // Fix `initialData` type\n    initialData?:\n      | UseQueryOptions<queryFnData, error, data, queryKey>['initialData']\n      | undefined\n  }\n>\n\nexport type UseQueryReturnType<data = unknown, error = DefaultError> = Compute<\n  UseQueryResult<data, error> & {\n    queryKey: QueryKey\n  }\n>\n\n// Adding some basic customization.\n// Ideally we don't have this function, but `import('@tanstack/react-query').useQuery` currently has some quirks where it is super hard to\n// pass down the inferred `initialData` type because of it's discriminated overload in the on `useQuery`.\nexport function useQuery<queryFnData, error, data, queryKey extends QueryKey>(\n  parameters: UseQueryParameters<queryFnData, error, data, queryKey> & {\n    queryKey: QueryKey\n  },\n): UseQueryReturnType<data, error> {\n  const result = tanstack_useQuery({\n    ...(parameters as any),\n    queryKeyHashFn: hashFn, // for bigint support\n  }) as UseQueryReturnType<data, error>\n  result.queryKey = parameters.queryKey\n  return result\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nexport type UseInfiniteQueryParameters<\n  queryFnData = unknown,\n  error = DefaultError,\n  data = queryFnData,\n  queryData = queryFnData,\n  queryKey extends QueryKey = QueryKey,\n  pageParam = unknown,\n> = Compute<\n  Omit<\n    UseInfiniteQueryOptions<\n      queryFnData,\n      error,\n      data,\n      queryData,\n      queryKey,\n      pageParam\n    >,\n    'initialData'\n  > & {\n    // Fix `initialData` type\n    initialData?:\n      | UseInfiniteQueryOptions<\n          queryFnData,\n          error,\n          data,\n          queryKey\n        >['initialData']\n      | undefined\n  }\n>\n\nexport type UseInfiniteQueryReturnType<\n  data = unknown,\n  error = DefaultError,\n> = UseInfiniteQueryResult<data, error> & {\n  queryKey: QueryKey\n}\n\n// Adding some basic customization.\nexport function useInfiniteQuery<\n  queryFnData,\n  error,\n  data,\n  queryKey extends QueryKey,\n>(\n  parameters: UseInfiniteQueryParameters<queryFnData, error, data, queryKey> & {\n    queryKey: QueryKey\n  },\n): UseInfiniteQueryReturnType<data, error> {\n  const result = tanstack_useInfiniteQuery({\n    ...(parameters as any),\n    queryKeyHashFn: hashFn, // for bigint support\n  }) as UseInfiniteQueryReturnType<data, error>\n  result.queryKey = parameters.queryKey\n  return result\n}\n", "'use client'\n\nimport {\n  type Config,\n  type GetChainIdReturnType,\n  type ResolvedRegister,\n  getChainId,\n  watchChainId,\n} from '@wagmi/core'\nimport { useSyncExternalStore } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseChainIdParameters<config extends Config = Config> =\n  ConfigParameter<config>\n\nexport type UseChainIdReturnType<config extends Config = Config> =\n  GetChainIdReturnType<config>\n\n/** https://wagmi.sh/react/api/hooks/useChainId */\nexport function useChainId<config extends Config = ResolvedRegister['config']>(\n  parameters: UseChainIdParameters<config> = {},\n): UseChainIdReturnType<config> {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStore(\n    (onChange) => watchChainId(config, { onChange }),\n    () => getChainId(config),\n    () => getChainId(config),\n  )\n}\n", "'use client'\n\nimport type { Config, GetBalanceErrorType, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetBalanceData,\n  type GetBalanceOptions,\n  type GetBalanceQueryKey,\n  getBalanceQueryOptions,\n} from '@wagmi/core/query'\nimport type { GetBalanceQueryFnData } from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseBalanceParameters<\n  config extends Config = Config,\n  selectData = GetBalanceData,\n> = Compute<\n  GetBalanceOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetBalanceQueryFnData,\n      GetBalanceErrorType,\n      selectData,\n      GetBalanceQueryKey<config>\n    >\n>\n\nexport type UseBalanceReturnType<selectData = GetBalanceData> =\n  UseQueryReturnType<selectData, GetBalanceErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useBalance */\nexport function useBalance<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetBalanceData,\n>(\n  parameters: UseBalanceParameters<config, selectData> = {},\n): UseBalanceReturnType<selectData> {\n  const { address, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getBalanceQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport {\n  type Config,\n  type ResolvedRegister,\n  type WatchBlocksParameters,\n  watchBlocks,\n} from '@wagmi/core'\nimport type { UnionCompute, UnionExactPartial } from '@wagmi/core/internal'\nimport { useEffect } from 'react'\nimport type { BlockTag } from 'viem'\n\nimport type { ConfigParameter, EnabledParameter } from '../types/properties.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWatchBlocksParameters<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = UnionCompute<\n  UnionExactPartial<\n    WatchBlocksParameters<includeTransactions, blockTag, config, chainId>\n  > &\n    ConfigParameter<config> &\n    EnabledParameter\n>\n\nexport type UseWatchBlocksReturnType = void\n\n/** https://wagmi.sh/react/hooks/useWatchBlocks */\nexport function useWatchBlocks<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n>(\n  parameters: UseWatchBlocksParameters<\n    includeTransactions,\n    blockTag,\n    config,\n    chainId\n  > = {} as any,\n): UseWatchBlocksReturnType {\n  const { enabled = true, onBlock, config: _, ...rest } = parameters\n\n  const config = useConfig(parameters)\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  // TODO(react@19): cleanup\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `rest` changes every render so only including properties in dependency array\n  useEffect(() => {\n    if (!enabled) return\n    if (!onBlock) return\n    return watchBlocks(config, {\n      ...(rest as any),\n      chainId,\n      onBlock,\n    })\n  }, [\n    chainId,\n    config,\n    enabled,\n    onBlock,\n    ///\n    rest.blockTag,\n    rest.emitMissed,\n    rest.emitOnBegin,\n    rest.includeTransactions,\n    rest.onError,\n    rest.poll,\n    rest.pollingInterval,\n    rest.syncConnectedChain,\n  ])\n}\n", "'use client'\n\nimport { useQueryClient } from '@tanstack/react-query'\nimport type { Config, GetBlockErrorType, ResolvedRegister } from '@wagmi/core'\nimport type {\n  Compute,\n  UnionCompute,\n  UnionStrictOmit,\n} from '@wagmi/core/internal'\nimport {\n  type GetBlockData,\n  type GetBlockOptions,\n  type GetBlockQueryFnData,\n  type GetBlockQueryKey,\n  getBlockQueryOptions,\n} from '@wagmi/core/query'\nimport type { BlockTag } from 'viem'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\nimport {\n  type UseWatchBlocksParameters,\n  useWatchBlocks,\n} from './useWatchBlocks.js'\n\nexport type UseBlockParameters<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockData<includeTransactions, blockTag, config, chainId>,\n> = Compute<\n  GetBlockOptions<includeTransactions, blockTag, config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetBlockQueryFnData<includeTransactions, blockTag, config, chainId>,\n      GetBlockErrorType,\n      selectData,\n      GetBlockQueryKey<includeTransactions, blockTag, config, chainId>\n    > & {\n      watch?:\n        | boolean\n        | UnionCompute<\n            UnionStrictOmit<\n              UseWatchBlocksParameters<\n                includeTransactions,\n                blockTag,\n                config,\n                chainId\n              >,\n              'chainId' | 'config' | 'onBlock' | 'onError'\n            >\n          >\n        | undefined\n    }\n>\n\nexport type UseBlockReturnType<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockData<includeTransactions, blockTag, config, chainId>,\n> = UseQueryReturnType<selectData, GetBlockErrorType>\n\n/** https://wagmi.sh/react/hooks/useBlock */\nexport function useBlock<\n  includeTransactions extends boolean = false,\n  blockTag extends BlockTag = 'latest',\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockData<includeTransactions, blockTag, config, chainId>,\n>(\n  parameters: UseBlockParameters<\n    includeTransactions,\n    blockTag,\n    config,\n    chainId,\n    selectData\n  > = {},\n): UseBlockReturnType<\n  includeTransactions,\n  blockTag,\n  config,\n  chainId,\n  selectData\n> {\n  const { query = {}, watch } = parameters\n\n  const config = useConfig(parameters)\n  const queryClient = useQueryClient()\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  const options = getBlockQueryOptions(config, {\n    ...parameters,\n    chainId,\n  })\n  const enabled = Boolean(query.enabled ?? true)\n\n  useWatchBlocks({\n    ...({\n      config: parameters.config,\n      chainId: parameters.chainId!,\n      ...(typeof watch === 'object' ? watch : {}),\n    } as UseWatchBlocksParameters),\n    enabled: Boolean(\n      enabled && (typeof watch === 'object' ? watch.enabled : watch),\n    ),\n    onBlock(block) {\n      queryClient.setQueryData(options.queryKey, block)\n    },\n  })\n\n  return useQuery({\n    ...(query as any),\n    ...options,\n    enabled,\n  }) as UseBlockReturnType<\n    includeTransactions,\n    blockTag,\n    config,\n    chainId,\n    selectData\n  >\n}\n", "'use client'\n\nimport {\n  type Config,\n  type ResolvedRegister,\n  type WatchBlockNumberParameters,\n  watchBlockNumber,\n} from '@wagmi/core'\nimport type { UnionCompute, UnionExactPartial } from '@wagmi/core/internal'\nimport { useEffect } from 'react'\n\nimport type { ConfigParameter, EnabledParameter } from '../types/properties.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWatchBlockNumberParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = UnionCompute<\n  UnionExactPartial<WatchBlockNumberParameters<config, chainId>> &\n    ConfigParameter<config> &\n    EnabledParameter\n>\n\nexport type UseWatchBlockNumberReturnType = void\n\n/** https://wagmi.sh/react/api/hooks/useWatchBlockNumber */\nexport function useWatchBlockNumber<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  parameters: UseWatchBlockNumberParameters<config, chainId> = {} as any,\n): UseWatchBlockNumberReturnType {\n  const { enabled = true, onBlockNumber, config: _, ...rest } = parameters\n\n  const config = useConfig(parameters)\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  // TODO(react@19): cleanup\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `rest` changes every render so only including properties in dependency array\n  useEffect(() => {\n    if (!enabled) return\n    if (!onBlockNumber) return\n    return watchBlockNumber(config, {\n      ...(rest as any),\n      chainId,\n      onBlockNumber,\n    })\n  }, [\n    chainId,\n    config,\n    enabled,\n    onBlockNumber,\n    ///\n    rest.onError,\n    rest.emitMissed,\n    rest.emitOnBegin,\n    rest.poll,\n    rest.pollingInterval,\n    rest.syncConnectedChain,\n  ])\n}\n", "'use client'\n\nimport { useQueryClient } from '@tanstack/react-query'\nimport type {\n  Config,\n  GetBlockNumberErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type {\n  Compute,\n  UnionCompute,\n  UnionStrictOmit,\n} from '@wagmi/core/internal'\nimport {\n  type GetBlockNumberData,\n  type GetBlockNumberOptions,\n  type GetBlockNumberQueryFnData,\n  type GetBlockNumberQueryKey,\n  getBlockNumberQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\nimport {\n  type UseWatchBlockNumberParameters,\n  useWatchBlockNumber,\n} from './useWatchBlockNumber.js'\n\nexport type UseBlockNumberParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockNumberData,\n> = Compute<\n  GetBlockNumberOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetBlockNumberQueryFnData,\n      GetBlockNumberErrorType,\n      selectData,\n      GetBlockNumberQueryKey<config, chainId>\n    > & {\n      watch?:\n        | boolean\n        | UnionCompute<\n            UnionStrictOmit<\n              UseWatchBlockNumberParameters<config, chainId>,\n              'chainId' | 'config' | 'onBlockNumber' | 'onError'\n            >\n          >\n        | undefined\n    }\n>\n\nexport type UseBlockNumberReturnType<selectData = GetBlockNumberData> =\n  UseQueryReturnType<selectData, GetBlockNumberErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useBlockNumber */\nexport function useBlockNumber<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockNumberData,\n>(\n  parameters: UseBlockNumberParameters<config, chainId, selectData> = {},\n): UseBlockNumberReturnType<selectData> {\n  const { query = {}, watch } = parameters\n\n  const config = useConfig(parameters)\n  const queryClient = useQueryClient()\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  const options = getBlockNumberQueryOptions(config, {\n    ...parameters,\n    chainId,\n  })\n\n  useWatchBlockNumber({\n    ...({\n      config: parameters.config,\n      chainId: parameters.chainId,\n      ...(typeof watch === 'object' ? watch : {}),\n    } as UseWatchBlockNumberParameters),\n    enabled: Boolean(\n      (query.enabled ?? true) &&\n        (typeof watch === 'object' ? watch.enabled : watch),\n    ),\n    onBlockNumber(blockNumber) {\n      queryClient.setQueryData(options.queryKey, blockNumber)\n    },\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetBlockTransactionCountErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { UnionCompute } from '@wagmi/core/internal'\nimport {\n  type GetBlockTransactionCountData,\n  type GetBlockTransactionCountOptions,\n  type GetBlockTransactionCountQueryFnData,\n  type GetBlockTransactionCountQueryKey,\n  getBlockTransactionCountQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseBlockTransactionCountParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockTransactionCountData,\n> = UnionCompute<\n  GetBlockTransactionCountOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetBlockTransactionCountQueryFnData,\n      GetBlockTransactionCountErrorType,\n      selectData,\n      GetBlockTransactionCountQueryKey<config, chainId>\n    >\n>\n\nexport type UseBlockTransactionCountReturnType<\n  selectData = GetBlockTransactionCountData,\n> = UseQueryReturnType<selectData, GetBlockTransactionCountErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useBlockTransactionCount */\nexport function useBlockTransactionCount<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetBlockTransactionCountData,\n>(\n  parameters: UseBlockTransactionCountParameters<\n    config,\n    chainId,\n    selectData\n  > = {},\n): UseBlockTransactionCountReturnType<selectData> {\n  const { query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  const options = getBlockTransactionCountQueryOptions(config, {\n    ...parameters,\n    chainId,\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetBytecodeErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetBytecodeData,\n  type GetBytecodeOptions,\n  type GetBytecodeQueryKey,\n  getBytecodeQueryOptions,\n} from '@wagmi/core/query'\nimport type { GetBytecodeQueryFnData } from '@wagmi/core/query'\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseBytecodeParameters<\n  config extends Config = Config,\n  selectData = GetBytecodeData,\n> = Compute<\n  GetBytecodeOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetBytecodeQueryFnData,\n      GetBytecodeErrorType,\n      selectData,\n      GetBytecodeQueryKey<config>\n    >\n>\n\nexport type UseBytecodeReturnType<selectData = GetBytecodeData> =\n  UseQueryReturnType<selectData, GetBytecodeErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useBytecode */\nexport function useBytecode<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetBytecodeData,\n>(\n  parameters: UseBytecodeParameters<config, selectData> = {},\n): UseBytecodeReturnType<selectData> {\n  const { address, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getBytecodeQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetCallsStatusErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetCallsStatusData,\n  type GetCallsStatusOptions,\n  type GetCallsStatusQueryFnData,\n  type GetCallsStatusQueryKey,\n  getCallsStatusQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseCallsStatusParameters<\n  config extends Config = Config,\n  selectData = GetCallsStatusData,\n> = Compute<\n  GetCallsStatusOptions &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetCallsStatusQueryFnData,\n      GetCallsStatusErrorType,\n      selectData,\n      GetCallsStatusQueryKey\n    >\n>\n\nexport type UseCallsStatusReturnType<selectData = GetCallsStatusData> =\n  UseQueryReturnType<selectData, GetCallsStatusErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useCallsStatus */\nexport function useCallsStatus<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetCallsStatusData,\n>(\n  parameters: UseCallsStatusParameters<config, selectData>,\n): UseCallsStatusReturnType<selectData> {\n  const { query = {} } = parameters\n\n  const config = useConfig(parameters)\n\n  const options = getCallsStatusQueryOptions(config, parameters)\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetCapabilitiesErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetCapabilitiesData,\n  type GetCapabilitiesOptions,\n  type GetCapabilitiesQueryFnData,\n  type GetCapabilitiesQueryKey,\n  getCapabilitiesQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useAccount } from './useAccount.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseCapabilitiesParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = GetCapabilitiesData<config, chainId>,\n> = Compute<\n  GetCapabilitiesOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetCapabilitiesQueryFnData<config, chainId>,\n      GetCapabilitiesErrorType,\n      selectData,\n      GetCapabilitiesQueryKey<config, chainId>\n    >\n>\n\nexport type UseCapabilitiesReturnType<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = GetCapabilitiesData<config, chainId>,\n> = UseQueryReturnType<selectData, GetCapabilitiesErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useCapabilities */\nexport function useCapabilities<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = GetCapabilitiesData<config, chainId>,\n>(\n  parameters: UseCapabilitiesParameters<config, chainId, selectData> = {},\n): UseCapabilitiesReturnType<config, chainId, selectData> {\n  const { account, query = {} } = parameters\n\n  const { address } = useAccount()\n  const config = useConfig(parameters)\n\n  const options = getCapabilitiesQueryOptions(config, {\n    ...parameters,\n    account: account ?? address,\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type { CallErrorType, Config, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type CallData,\n  type CallOptions,\n  type CallQueryKey,\n  callQueryOptions,\n} from '@wagmi/core/query'\nimport type { CallQueryFnData } from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseCallParameters<\n  config extends Config = Config,\n  selectData = CallData,\n> = Compute<\n  CallOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      CallQueryFnData,\n      CallErrorType,\n      selectData,\n      CallQueryKey<config>\n    >\n>\n\nexport type UseCallReturnType<selectData = CallData> = UseQueryReturnType<\n  selectData,\n  CallErrorType\n>\n\n/** https://wagmi.sh/react/api/hooks/useCall */\nexport function useCall<\n  config extends Config = ResolvedRegister['config'],\n  selectData = CallData,\n>(\n  parameters: UseCallParameters<config, selectData> = {},\n): UseCallReturnType<selectData> {\n  const { query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = callQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport {\n  type Config,\n  type GetChainsReturnType,\n  type ResolvedRegister,\n  getChains,\n} from '@wagmi/core'\nimport { watchChains } from '@wagmi/core/internal'\nimport { useSyncExternalStore } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseChainsParameters<config extends Config = Config> =\n  ConfigParameter<config>\n\nexport type UseChainsReturnType<config extends Config = Config> =\n  GetChainsReturnType<config>\n\n/** https://wagmi.sh/react/api/hooks/useChains */\nexport function useChains<config extends Config = ResolvedRegister['config']>(\n  parameters: UseChainsParameters<config> = {},\n): UseChainsReturnType<config> {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStore(\n    (onChange) => watchChains(config, { onChange }),\n    () => getChains(config),\n    () => getChains(config),\n  )\n}\n", "'use client'\n\nimport {\n  type Config,\n  type GetClientParameters,\n  type GetClientReturnType,\n  type ResolvedRegister,\n  getClient,\n  watchClient,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector.js'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseClientParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | number | undefined =\n    | config['chains'][number]['id']\n    | undefined,\n> = Compute<GetClientParameters<config, chainId> & ConfigParameter<config>>\n\nexport type UseClientReturnType<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | number | undefined =\n    | config['chains'][number]['id']\n    | undefined,\n> = GetClientReturnType<config, chainId>\n\n/** https://wagmi.sh/react/api/hooks/useClient */\nexport function useClient<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | number | undefined =\n    | config['chains'][number]['id']\n    | undefined,\n>(\n  parameters: UseClientParameters<config, chainId> = {},\n): UseClientReturnType<config, chainId> {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStoreWithSelector(\n    (onChange) => watchClient(config, { onChange }),\n    () => getClient(config, parameters),\n    () => getClient(config, parameters),\n    (x) => x,\n    (a, b) => a?.uid === b?.uid,\n  ) as any\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { Config, ConnectErrorType, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type ConnectData,\n  type ConnectMutate,\n  type ConnectMutateAsync,\n  type ConnectVariables,\n  connectMutationOptions,\n} from '@wagmi/core/query'\nimport { useEffect } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\nimport { type UseConnectorsReturnType, useConnectors } from './useConnectors.js'\n\nexport type UseConnectParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          ConnectData<config>,\n          ConnectErrorType,\n          ConnectVariables<config, config['connectors'][number]>,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseConnectReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  UseMutationReturnType<\n    ConnectData<config>,\n    ConnectErrorType,\n    ConnectVariables<config, config['connectors'][number]>,\n    context\n  > & {\n    connect: ConnectMutate<config, context>\n    connectAsync: ConnectMutateAsync<config, context>\n    connectors: Compute<UseConnectorsReturnType> | config['connectors']\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useConnect */\nexport function useConnect<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseConnectParameters<config, context> = {},\n): UseConnectReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = connectMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  // Reset mutation back to an idle state when the connector disconnects.\n  useEffect(() => {\n    return config.subscribe(\n      ({ status }) => status,\n      (status, previousStatus) => {\n        if (previousStatus === 'connected' && status === 'disconnected')\n          result.reset()\n      },\n    )\n  }, [config, result.reset])\n\n  type Return = UseConnectReturnType<config, context>\n  return {\n    ...(result as Return),\n    connect: mutate as Return['connect'],\n    connectAsync: mutateAsync as Return['connectAsync'],\n    connectors: useConnectors({ config }),\n  }\n}\n", "'use client'\n\nimport {\n  type Config,\n  type GetConnectorsReturnType,\n  type ResolvedRegister,\n  getConnectors,\n  watchConnectors,\n} from '@wagmi/core'\nimport { useSyncExternalStore } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseConnectorsParameters<config extends Config = Config> =\n  ConfigParameter<config>\n\nexport type UseConnectorsReturnType<config extends Config = Config> =\n  GetConnectorsReturnType<config>\n\n/** https://wagmi.sh/react/api/hooks/useConnectors */\nexport function useConnectors<\n  config extends Config = ResolvedRegister['config'],\n>(\n  parameters: UseConnectorsParameters<config> = {},\n): UseConnectorsReturnType<config> {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStore(\n    (onChange) => watchConnectors(config, { onChange }),\n    () => getConnectors(config),\n    () => getConnectors(config),\n  )\n}\n", "'use client'\n\nimport {\n  type GetConnectionsReturnType,\n  getConnections,\n  watchConnections,\n} from '@wagmi/core'\nimport { useSyncExternalStore } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseConnectionsParameters = ConfigParameter\n\nexport type UseConnectionsReturnType = GetConnectionsReturnType\n\n/** https://wagmi.sh/react/api/hooks/useConnections */\nexport function useConnections(\n  parameters: UseConnectionsParameters = {},\n): UseConnectionsReturnType {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStore(\n    (onChange) => watchConnections(config, { onChange }),\n    () => getConnections(config),\n    () => getConnections(config),\n  )\n}\n", "'use client'\n\nimport { useQueryClient } from '@tanstack/react-query'\nimport type {\n  Config,\n  GetConnectorClientErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute, Omit } from '@wagmi/core/internal'\nimport {\n  type GetConnectorClientData,\n  type GetConnectorClientOptions,\n  type GetConnectorClientQueryFnData,\n  type GetConnectorClientQueryKey,\n  getConnectorClientQueryOptions,\n} from '@wagmi/core/query'\nimport { useEffect, useRef } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport {\n  type UseQueryParameters,\n  type UseQueryReturnType,\n  useQuery,\n} from '../utils/query.js'\nimport { useAccount } from './useAccount.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseConnectorClientParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetConnectorClientData<config, chainId>,\n> = Compute<\n  GetConnectorClientOptions<config, chainId> &\n    ConfigParameter<config> & {\n      query?:\n        | Compute<\n            Omit<\n              UseQueryParameters<\n                GetConnectorClientQueryFnData<config, chainId>,\n                GetConnectorClientErrorType,\n                selectData,\n                GetConnectorClientQueryKey<config, chainId>\n              >,\n              'gcTime' | 'staleTime'\n            >\n          >\n        | undefined\n    }\n>\n\nexport type UseConnectorClientReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetConnectorClientData<config, chainId>,\n> = UseQueryReturnType<selectData, GetConnectorClientErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useConnectorClient */\nexport function useConnectorClient<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetConnectorClientData<config, chainId>,\n>(\n  parameters: UseConnectorClientParameters<config, chainId, selectData> = {},\n): UseConnectorClientReturnType<config, chainId, selectData> {\n  const { query = {}, ...rest } = parameters\n\n  const config = useConfig(rest)\n  const queryClient = useQueryClient()\n  const { address, connector, status } = useAccount({ config })\n  const chainId = useChainId({ config })\n  const activeConnector = parameters.connector ?? connector\n\n  const { queryKey, ...options } = getConnectorClientQueryOptions<\n    config,\n    chainId\n  >(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n    connector: activeConnector,\n  })\n  const enabled = Boolean(\n    (status === 'connected' ||\n      (status === 'reconnecting' && activeConnector?.getProvider)) &&\n      (query.enabled ?? true),\n  )\n\n  const addressRef = useRef(address)\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `queryKey` not required\n  useEffect(() => {\n    const previousAddress = addressRef.current\n    if (!address && previousAddress) {\n      // remove when account is disconnected\n      queryClient.removeQueries({ queryKey })\n      addressRef.current = undefined\n    } else if (address !== previousAddress) {\n      // invalidate when address changes\n      queryClient.invalidateQueries({ queryKey })\n      addressRef.current = address\n    }\n  }, [address, queryClient])\n\n  return useQuery({\n    ...query,\n    ...options,\n    queryKey,\n    enabled,\n    staleTime: Number.POSITIVE_INFINITY,\n  })\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type {\n  Config,\n  DeployContractErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type DeployContractData,\n  type DeployContractMutate,\n  type DeployContractMutateAsync,\n  type DeployContractVariables,\n  deployContractMutationOptions,\n} from '@wagmi/core/query'\nimport type { Abi } from 'viem'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseDeployContractParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          DeployContractData,\n          DeployContractErrorType,\n          DeployContractVariables<Abi, config, config['chains'][number]['id']>,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseDeployContractReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = UseMutationReturnType<\n  DeployContractData,\n  DeployContractErrorType,\n  DeployContractVariables<Abi, config, config['chains'][number]['id']>,\n  context\n> & {\n  deployContract: DeployContractMutate<config, context>\n  deployContractAsync: DeployContractMutateAsync<config, context>\n}\n\n/** https://wagmi.sh/react/api/hooks/useDeployContract */\nexport function useDeployContract<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseDeployContractParameters<config, context> = {},\n): UseDeployContractReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = deployContractMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseDeployContractReturnType<config, context>\n  return {\n    ...result,\n    deployContract: mutate as Return['deployContract'],\n    deployContractAsync: mutateAsync as Return['deployContractAsync'],\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { Connector, DisconnectErrorType } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type DisconnectData,\n  type DisconnectMutate,\n  type DisconnectMutateAsync,\n  type DisconnectVariables,\n  disconnectMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\nimport { useConnections } from './useConnections.js'\n\nexport type UseDisconnectParameters<context = unknown> = Compute<\n  ConfigParameter & {\n    mutation?:\n      | UseMutationParameters<\n          DisconnectData,\n          DisconnectErrorType,\n          DisconnectVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseDisconnectReturnType<context = unknown> = Compute<\n  UseMutationReturnType<\n    DisconnectData,\n    DisconnectErrorType,\n    DisconnectVariables,\n    context\n  > & {\n    connectors: readonly Connector[]\n    disconnect: DisconnectMutate<context>\n    disconnectAsync: DisconnectMutateAsync<context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useDisconnect */\nexport function useDisconnect<context = unknown>(\n  parameters: UseDisconnectParameters<context> = {},\n): UseDisconnectReturnType<context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = disconnectMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  return {\n    ...result,\n    connectors: useConnections({ config }).map(\n      (connection) => connection.connector,\n    ),\n    disconnect: mutate,\n    disconnectAsync: mutateAsync,\n  }\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetEnsAddressErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetEnsAddressData,\n  type GetEnsAddressOptions,\n  type GetEnsAddressQueryFnData,\n  type GetEnsAddressQueryKey,\n  getEnsAddressQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEnsAddressParameters<\n  config extends Config = Config,\n  selectData = GetEnsAddressData,\n> = Compute<\n  GetEnsAddressOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetEnsAddressQueryFnData,\n      GetEnsAddressErrorType,\n      selectData,\n      GetEnsAddressQueryKey<config>\n    >\n>\n\nexport type UseEnsAddressReturnType<selectData = GetEnsAddressData> =\n  UseQueryReturnType<selectData, GetEnsAddressErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEnsAddress */\nexport function useEnsAddress<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetEnsAddressData,\n>(\n  parameters: UseEnsAddressParameters<config, selectData> = {},\n): UseEnsAddressReturnType<selectData> {\n  const { name, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getEnsAddressQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(name && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetEnsAvatarErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetEnsAvatarData,\n  type GetEnsAvatarOptions,\n  type GetEnsAvatarQueryFnData,\n  type GetEnsAvatarQueryKey,\n  getEnsAvatarQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEnsAvatarParameters<\n  config extends Config = Config,\n  selectData = GetEnsAvatarData,\n> = Compute<\n  GetEnsAvatarOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetEnsAvatarQueryFnData,\n      GetEnsAvatarErrorType,\n      selectData,\n      GetEnsAvatarQueryKey<config>\n    >\n>\n\nexport type UseEnsAvatarReturnType<selectData = GetEnsAvatarData> =\n  UseQueryReturnType<selectData, GetEnsAvatarErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEnsAvatar */\nexport function useEnsAvatar<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetEnsAvatarData,\n>(\n  parameters: UseEnsAvatarParameters<config, selectData> = {},\n): UseEnsAvatarReturnType<selectData> {\n  const { name, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getEnsAvatarQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(name && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type { Config, GetEnsNameErrorType, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetEnsNameData,\n  type GetEnsNameOptions,\n  type GetEnsNameQueryFnData,\n  type GetEnsNameQueryKey,\n  getEnsNameQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEnsNameParameters<\n  config extends Config = Config,\n  selectData = GetEnsNameData,\n> = Compute<\n  GetEnsNameOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetEnsNameQueryFnData,\n      GetEnsNameErrorType,\n      selectData,\n      GetEnsNameQueryKey<config>\n    >\n>\n\nexport type UseEnsNameReturnType<selectData = GetEnsNameData> =\n  UseQueryReturnType<selectData, GetEnsNameErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEnsName */\nexport function useEnsName<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetEnsNameData,\n>(\n  parameters: UseEnsNameParameters<config, selectData> = {},\n): UseEnsNameReturnType<selectData> {\n  const { address, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getEnsNameQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetEnsResolverErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetEnsResolverData,\n  type GetEnsResolverOptions,\n  type GetEnsResolverQueryFnData,\n  type GetEnsResolverQueryKey,\n  getEnsResolverQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEnsResolverParameters<\n  config extends Config = Config,\n  selectData = GetEnsResolverData,\n> = Compute<\n  GetEnsResolverOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetEnsResolverQueryFnData,\n      GetEnsResolverErrorType,\n      selectData,\n      GetEnsResolverQueryKey<config>\n    >\n>\n\nexport type UseEnsResolverReturnType<selectData = GetEnsResolverData> =\n  UseQueryReturnType<selectData, GetEnsResolverErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEnsResolver */\nexport function useEnsResolver<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetEnsResolverData,\n>(\n  parameters: UseEnsResolverParameters<config, selectData> = {},\n): UseEnsResolverReturnType<selectData> {\n  const { name, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getEnsResolverQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(name && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type { Config, GetEnsTextErrorType, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetEnsTextData,\n  type GetEnsTextOptions,\n  type GetEnsTextQueryFnData,\n  type GetEnsTextQueryKey,\n  getEnsTextQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEnsTextParameters<\n  config extends Config = Config,\n  selectData = GetEnsTextData,\n> = Compute<\n  GetEnsTextOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetEnsTextQueryFnData,\n      GetEnsTextErrorType,\n      selectData,\n      GetEnsTextQueryKey<config>\n    >\n>\n\nexport type UseEnsTextReturnType<selectData = GetEnsTextData> =\n  UseQueryReturnType<selectData, GetEnsTextErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEnsText */\nexport function useEnsText<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetEnsTextData,\n>(\n  parameters: UseEnsTextParameters<config, selectData> = {},\n): UseEnsTextReturnType<selectData> {\n  const { key, name, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getEnsTextQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(key && name && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  EstimateFeesPerGasErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type EstimateFeesPerGasData,\n  type EstimateFeesPerGasOptions,\n  type EstimateFeesPerGasQueryFnData,\n  type EstimateFeesPerGasQueryKey,\n  estimateFeesPerGasQueryOptions,\n} from '@wagmi/core/query'\nimport type { FeeValuesType } from 'viem'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEstimateFeesPerGasParameters<\n  type extends FeeValuesType = FeeValuesType,\n  config extends Config = Config,\n  selectData = EstimateFeesPerGasData<type>,\n> = Compute<\n  EstimateFeesPerGasOptions<type, config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      EstimateFeesPerGasQueryFnData<type>,\n      EstimateFeesPerGasErrorType,\n      selectData,\n      EstimateFeesPerGasQueryKey<config, type>\n    >\n>\n\nexport type UseEstimateFeesPerGasReturnType<\n  type extends FeeValuesType = FeeValuesType,\n  selectData = EstimateFeesPerGasData<type>,\n> = UseQueryReturnType<selectData, EstimateFeesPerGasErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEstimateFeesPerGas */\nexport function useEstimateFeesPerGas<\n  config extends Config = ResolvedRegister['config'],\n  type extends FeeValuesType = 'eip1559',\n  selectData = EstimateFeesPerGasData<type>,\n>(\n  parameters: UseEstimateFeesPerGasParameters<type, config, selectData> = {},\n): UseEstimateFeesPerGasReturnType<type, selectData> {\n  const { query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = estimateFeesPerGasQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type {\n  Config,\n  EstimateGasErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport {\n  type EstimateGasData,\n  type EstimateGasOptions,\n  type EstimateGasQueryFnData,\n  type EstimateGasQueryKey,\n  estimateGasQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\nimport { useConnectorClient } from './useConnectorClient.js'\n\nexport type UseEstimateGasParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = EstimateGasData,\n> = EstimateGasOptions<config, chainId> &\n  ConfigParameter<config> &\n  QueryParameter<\n    EstimateGasQueryFnData,\n    EstimateGasErrorType,\n    selectData,\n    EstimateGasQueryKey<config, chainId>\n  >\n\nexport type UseEstimateGasReturnType<selectData = EstimateGasData> =\n  UseQueryReturnType<selectData, EstimateGasErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEstimateGas */\nexport function useEstimateGas<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = EstimateGasData,\n>(\n  parameters?: UseEstimateGasParameters<config, chainId, selectData>,\n): UseEstimateGasReturnType<selectData>\n\nexport function useEstimateGas(\n  parameters: UseEstimateGasParameters = {},\n): UseEstimateGasReturnType {\n  const { connector, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const { data: connectorClient } = useConnectorClient({\n    config,\n    connector,\n    query: { enabled: parameters.account === undefined },\n  })\n  const account = parameters.account ?? connectorClient?.account\n  const chainId = useChainId({ config })\n\n  const options = estimateGasQueryOptions(config, {\n    ...parameters,\n    account,\n    chainId: parameters.chainId ?? chainId,\n    connector,\n  })\n  const enabled = Boolean((account || connector) && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  EstimateMaxPriorityFeePerGasErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type EstimateMaxPriorityFeePerGasData,\n  type EstimateMaxPriorityFeePerGasOptions,\n  type EstimateMaxPriorityFeePerGasQueryFnData,\n  type EstimateMaxPriorityFeePerGasQueryKey,\n  estimateMaxPriorityFeePerGasQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseEstimateMaxPriorityFeePerGasParameters<\n  config extends Config = Config,\n  selectData = EstimateMaxPriorityFeePerGasData,\n> = Compute<\n  EstimateMaxPriorityFeePerGasOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      EstimateMaxPriorityFeePerGasQueryFnData,\n      EstimateMaxPriorityFeePerGasErrorType,\n      selectData,\n      EstimateMaxPriorityFeePerGasQueryKey<config>\n    >\n>\n\nexport type UseEstimateMaxPriorityFeePerGasReturnType<\n  selectData = EstimateMaxPriorityFeePerGasData,\n> = UseQueryReturnType<selectData, EstimateMaxPriorityFeePerGasErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useEstimateMaxPriorityFeePerGas */\nexport function useEstimateMaxPriorityFeePerGas<\n  config extends Config = ResolvedRegister['config'],\n  selectData = EstimateMaxPriorityFeePerGasData,\n>(\n  parameters: UseEstimateMaxPriorityFeePerGasParameters<\n    config,\n    selectData\n  > = {},\n): UseEstimateMaxPriorityFeePerGasReturnType<selectData> {\n  const { query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = estimateMaxPriorityFeePerGasQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetFeeHistoryErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetFeeHistoryData,\n  type GetFeeHistoryOptions,\n  type GetFeeHistoryQueryFnData,\n  type GetFeeHistoryQueryKey,\n  getFeeHistoryQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseFeeHistoryParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetFeeHistoryData,\n> = Compute<\n  GetFeeHistoryOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetFeeHistoryQueryFnData,\n      GetFeeHistoryErrorType,\n      selectData,\n      GetFeeHistoryQueryKey<config, chainId>\n    >\n>\n\nexport type UseFeeHistoryReturnType<selectData = GetFeeHistoryData> =\n  UseQueryReturnType<selectData, GetFeeHistoryErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useFeeHistory */\nexport function useFeeHistory<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetFeeHistoryData,\n>(\n  parameters: UseFeeHistoryParameters<config, chainId, selectData> = {},\n): UseFeeHistoryReturnType<selectData> {\n  const { blockCount, rewardPercentiles, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getFeeHistoryQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(\n    blockCount && rewardPercentiles && (query.enabled ?? true),\n  )\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetGasPriceErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetGasPriceData,\n  type GetGasPriceOptions,\n  type GetGasPriceQueryFnData,\n  type GetGasPriceQueryKey,\n  getGasPriceQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseGasPriceParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetGasPriceData,\n> = Compute<\n  GetGasPriceOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetGasPriceQueryFnData,\n      GetGasPriceErrorType,\n      selectData,\n      GetGasPriceQueryKey<config, chainId>\n    >\n>\n\nexport type UseGasPriceReturnType<selectData = GetGasPriceData> =\n  UseQueryReturnType<selectData, GetGasPriceErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useGasPrice */\nexport function useGasPrice<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetGasPriceData,\n>(\n  parameters: UseGasPriceParameters<config, chainId, selectData> = {},\n): UseGasPriceReturnType<selectData> {\n  const { query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  const options = getGasPriceQueryOptions(config, {\n    ...parameters,\n    chainId,\n  })\n\n  return useQuery({ ...query, ...options })\n}\n", "'use client'\n\nimport type {\n  Config,\n  ReadContractsErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport {\n  type InfiniteReadContractsQueryFnData,\n  type InfiniteReadContractsQueryKey,\n  infiniteReadContractsQueryOptions,\n  structuralSharing,\n} from '@wagmi/core/query'\nimport type { ContractFunctionParameters } from 'viem'\n\nimport type {\n  InfiniteReadContractsData,\n  InfiniteReadContractsOptions,\n} from '../exports/query.js'\nimport type {\n  ConfigParameter,\n  InfiniteQueryParameter,\n} from '../types/properties.js'\nimport {\n  type UseInfiniteQueryParameters,\n  type UseInfiniteQueryReturnType,\n  useInfiniteQuery,\n} from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseInfiniteContractReadsParameters<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  config extends Config = Config,\n  pageParam = unknown,\n  selectData = InfiniteReadContractsData<contracts, allowFailure>,\n> = InfiniteReadContractsOptions<contracts, allowFailure, pageParam, config> &\n  ConfigParameter<config> &\n  InfiniteQueryParameter<\n    InfiniteReadContractsQueryFnData<contracts, allowFailure>,\n    ReadContractsErrorType,\n    selectData,\n    InfiniteReadContractsData<contracts, allowFailure>,\n    InfiniteReadContractsQueryKey<contracts, allowFailure, pageParam, config>,\n    pageParam\n  >\n\nexport type UseInfiniteContractReadsReturnType<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  selectData = InfiniteReadContractsData<contracts, allowFailure>,\n> = UseInfiniteQueryReturnType<selectData, ReadContractsErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useInfiniteReadContracts */\nexport function useInfiniteReadContracts<\n  const contracts extends readonly unknown[],\n  allowFailure extends boolean = true,\n  config extends Config = ResolvedRegister['config'],\n  pageParam = unknown,\n  selectData = InfiniteReadContractsData<contracts, allowFailure>,\n>(\n  parameters: UseInfiniteContractReadsParameters<\n    contracts,\n    allowFailure,\n    config,\n    pageParam,\n    selectData\n  >,\n): UseInfiniteContractReadsReturnType<contracts, allowFailure, selectData> {\n  const { contracts = [], query } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = infiniteReadContractsQueryOptions(config, {\n    ...parameters,\n    chainId,\n    contracts: contracts as UseInfiniteContractReadsParameters['contracts'],\n    query: query as UseInfiniteQueryParameters,\n  })\n\n  return useInfiniteQuery({\n    ...(query as any),\n    ...options,\n    initialPageParam: options.initialPageParam,\n    structuralSharing: query.structuralSharing ?? structuralSharing,\n  })\n}\n", "'use client'\n\nimport type {\n  Config,\n  PrepareTransactionRequestErrorType,\n  ResolvedRegister,\n  SelectChains,\n} from '@wagmi/core'\nimport {\n  type PrepareTransactionRequestData,\n  type PrepareTransactionRequestOptions,\n  type PrepareTransactionRequestQueryKey,\n  prepareTransactionRequestQueryOptions,\n} from '@wagmi/core/query'\nimport type { PrepareTransactionRequestQueryFnData } from '@wagmi/core/query'\nimport type { PrepareTransactionRequestRequest as viem_PrepareTransactionRequestRequest } from 'viem'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UsePrepareTransactionRequestParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  > = viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n  selectData = PrepareTransactionRequestData<config, chainId, request>,\n> = PrepareTransactionRequestOptions<config, chainId, request> &\n  ConfigParameter<config> &\n  QueryParameter<\n    PrepareTransactionRequestQueryFnData<config, chainId, request>,\n    PrepareTransactionRequestErrorType,\n    selectData,\n    PrepareTransactionRequestQueryKey<config, chainId, request>\n  >\n\nexport type UsePrepareTransactionRequestReturnType<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  > = viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n  selectData = PrepareTransactionRequestData<config, chainId, request>,\n> = UseQueryReturnType<selectData, PrepareTransactionRequestErrorType>\n\n/** https://wagmi.sh/react/api/hooks/usePrepareTransactionRequest */\nexport function usePrepareTransactionRequest<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  request extends viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  > = viem_PrepareTransactionRequestRequest<\n    SelectChains<config, chainId>[0],\n    SelectChains<config, chainId>[0]\n  >,\n  selectData = PrepareTransactionRequestData<config, chainId, request>,\n>(\n  parameters: UsePrepareTransactionRequestParameters<\n    config,\n    chainId,\n    request,\n    selectData\n  > = {} as any,\n): UsePrepareTransactionRequestReturnType<\n  config,\n  chainId,\n  request,\n  selectData\n> {\n  const { to, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = prepareTransactionRequestQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  } as PrepareTransactionRequestOptions<config, chainId, request>)\n  const enabled = Boolean(to && (query.enabled ?? true))\n\n  return useQuery({\n    ...(query as any),\n    ...options,\n    enabled,\n  }) as UsePrepareTransactionRequestReturnType<\n    config,\n    chainId,\n    request,\n    selectData\n  >\n}\n", "'use client'\n\nimport type { Config, GetProofErrorType, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetProofData,\n  type GetProofOptions,\n  type GetProofQueryKey,\n  getProofQueryOptions,\n} from '@wagmi/core/query'\nimport type { GetProofQueryFnData } from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseProofParameters<\n  config extends Config = Config,\n  selectData = GetProofData,\n> = Compute<\n  GetProofOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetProofQueryFnData,\n      GetProofErrorType,\n      selectData,\n      GetProofQueryKey<config>\n    >\n>\n\nexport type UseProofReturnType<selectData = GetProofData> = UseQueryReturnType<\n  selectData,\n  GetProofErrorType\n>\n\n/** https://wagmi.sh/react/api/hooks/useProof */\nexport function useProof<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetProofData,\n>(\n  parameters: UseProofParameters<config, selectData> = {},\n): UseProofReturnType<selectData> {\n  const { address, storageKeys, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getProofQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && storageKeys && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport {\n  type Config,\n  type GetPublicClientParameters,\n  type GetPublicClientReturnType,\n  type ResolvedRegister,\n  getPublicClient,\n  watchPublicClient,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/shim/with-selector.js'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UsePublicClientParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | number | undefined =\n    | config['chains'][number]['id']\n    | undefined,\n> = Compute<\n  GetPublicClientParameters<config, chainId> & ConfigParameter<config>\n>\n\nexport type UsePublicClientReturnType<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | number | undefined =\n    | config['chains'][number]['id']\n    | undefined,\n> = GetPublicClientReturnType<config, chainId>\n\n/** https://wagmi.sh/react/api/hooks/usePublicClient */\nexport function usePublicClient<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | number | undefined =\n    | config['chains'][number]['id']\n    | undefined,\n>(\n  parameters: UsePublicClientParameters<config, chainId> = {},\n): UsePublicClientReturnType<config, chainId> {\n  const config = useConfig(parameters)\n\n  return useSyncExternalStoreWithSelector(\n    (onChange) => watchPublicClient(config, { onChange }),\n    () => getPublicClient(config, parameters),\n    () => getPublicClient(config, parameters),\n    (x) => x,\n    (a, b) => a?.uid === b?.uid,\n  ) as any\n}\n", "'use client'\n\nimport type {\n  Config,\n  ReadContractErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { UnionCompute } from '@wagmi/core/internal'\nimport {\n  type ReadContractData,\n  type ReadContractOptions,\n  type ReadContractQueryFnData,\n  type ReadContractQueryKey,\n  readContractQueryOptions,\n  structuralSharing,\n} from '@wagmi/core/query'\nimport type { Abi, ContractFunctionArgs, ContractFunctionName, Hex } from 'viem'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseReadContractParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'pure' | 'view'\n  > = ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'pure' | 'view',\n    functionName\n  > = ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n  config extends Config = Config,\n  selectData = ReadContractData<abi, functionName, args>,\n> = UnionCompute<\n  ReadContractOptions<abi, functionName, args, config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      ReadContractQueryFnData<abi, functionName, args>,\n      ReadContractErrorType,\n      selectData,\n      ReadContractQueryKey<abi, functionName, args, config>\n    >\n>\n\nexport type UseReadContractReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'pure' | 'view'\n  > = ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'pure' | 'view',\n    functionName\n  > = ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n  selectData = ReadContractData<abi, functionName, args>,\n> = UseQueryReturnType<selectData, ReadContractErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useReadContract */\nexport function useReadContract<\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'pure' | 'view'>,\n  args extends ContractFunctionArgs<abi, 'pure' | 'view', functionName>,\n  config extends Config = ResolvedRegister['config'],\n  selectData = ReadContractData<abi, functionName, args>,\n>(\n  parameters: UseReadContractParameters<\n    abi,\n    functionName,\n    args,\n    config,\n    selectData\n  > = {} as any,\n): UseReadContractReturnType<abi, functionName, args, selectData> {\n  const { abi, address, functionName, query = {} } = parameters\n  // @ts-ignore\n  const code = parameters.code as Hex | undefined\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = readContractQueryOptions<config, abi, functionName, args>(\n    config,\n    { ...(parameters as any), chainId: parameters.chainId ?? chainId },\n  )\n  const enabled = Boolean(\n    (address || code) && abi && functionName && (query.enabled ?? true),\n  )\n\n  return useQuery({\n    ...query,\n    ...options,\n    enabled,\n    structuralSharing: query.structuralSharing ?? structuralSharing,\n  })\n}\n", "'use client'\n\nimport type {\n  Config,\n  ReadContractsErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type ReadContractsData,\n  type ReadContractsOptions,\n  type ReadContractsQueryFnData,\n  type ReadContractsQueryKey,\n  readContractsQueryOptions,\n  structuralSharing,\n} from '@wagmi/core/query'\nimport { useMemo } from 'react'\nimport type { ContractFunctionParameters } from 'viem'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseReadContractsParameters<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  config extends Config = Config,\n  selectData = ReadContractsData<contracts, allowFailure>,\n> = Compute<\n  ReadContractsOptions<contracts, allowFailure, config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      ReadContractsQueryFnData<contracts, allowFailure>,\n      ReadContractsErrorType,\n      selectData,\n      ReadContractsQueryKey<contracts, allowFailure, config>\n    >\n>\n\nexport type UseReadContractsReturnType<\n  contracts extends readonly unknown[] = readonly ContractFunctionParameters[],\n  allowFailure extends boolean = true,\n  selectData = ReadContractsData<contracts, allowFailure>,\n> = UseQueryReturnType<selectData, ReadContractsErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useReadContracts */\nexport function useReadContracts<\n  const contracts extends readonly unknown[],\n  allowFailure extends boolean = true,\n  config extends Config = ResolvedRegister['config'],\n  selectData = ReadContractsData<contracts, allowFailure>,\n>(\n  parameters: UseReadContractsParameters<\n    contracts,\n    allowFailure,\n    config,\n    selectData\n  > = {},\n): UseReadContractsReturnType<contracts, allowFailure, selectData> {\n  const { contracts = [], query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = readContractsQueryOptions<config, contracts, allowFailure>(\n    config,\n    { ...parameters, chainId },\n  )\n\n  const enabled = useMemo(() => {\n    let isContractsValid = false\n    for (const contract of contracts) {\n      const { abi, address, functionName } =\n        contract as ContractFunctionParameters\n      if (!abi || !address || !functionName) {\n        isContractsValid = false\n        break\n      }\n      isContractsValid = true\n    }\n    return Boolean(isContractsValid && (query.enabled ?? true))\n  }, [contracts, query.enabled])\n\n  return useQuery({\n    ...options,\n    ...query,\n    enabled,\n    structuralSharing: query.structuralSharing ?? structuralSharing,\n  })\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { Connector, ReconnectErrorType } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type ReconnectData,\n  type ReconnectMutate,\n  type ReconnectMutateAsync,\n  type ReconnectVariables,\n  reconnectMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseReconnectParameters<context = unknown> = Compute<\n  ConfigParameter & {\n    mutation?:\n      | UseMutationParameters<\n          ReconnectData,\n          ReconnectErrorType,\n          ReconnectVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseReconnectReturnType<context = unknown> = Compute<\n  UseMutationReturnType<\n    ReconnectData,\n    ReconnectErrorType,\n    ReconnectVariables,\n    context\n  > & {\n    connectors: readonly Connector[]\n    reconnect: ReconnectMutate<context>\n    reconnectAsync: ReconnectMutateAsync<context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useReconnect */\nexport function useReconnect<context = unknown>(\n  parameters: UseReconnectParameters<context> = {},\n): UseReconnectReturnType<context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = reconnectMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  return {\n    ...result,\n    connectors: config.connectors,\n    reconnect: mutate,\n    reconnectAsync: mutateAsync,\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { Config, ResolvedRegister, SendCallsErrorType } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type SendCallsData,\n  type SendCallsMutate,\n  type SendCallsMutateAsync,\n  type SendCallsVariables,\n  sendCallsMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseSendCallsParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          SendCallsData,\n          SendCallsErrorType,\n          SendCallsVariables<config, config['chains'][number]['id']>,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseSendCallsReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  UseMutationReturnType<\n    SendCallsData,\n    SendCallsErrorType,\n    SendCallsVariables<config, config['chains'][number]['id']>,\n    context\n  > & {\n    sendCalls: SendCallsMutate<config, context>\n    sendCallsAsync: SendCallsMutateAsync<config, context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useSendCalls */\nexport function useSendCalls<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseSendCallsParameters<config, context> = {},\n): UseSendCallsReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = sendCallsMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseSendCallsReturnType<config, context>\n  return {\n    ...result,\n    sendCalls: mutate as Return['sendCalls'],\n    sendCallsAsync: mutateAsync as Return['sendCallsAsync'],\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type {\n  Config,\n  ResolvedRegister,\n  SendTransactionErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type SendTransactionData,\n  type SendTransactionMutate,\n  type SendTransactionMutateAsync,\n  type SendTransactionVariables,\n  sendTransactionMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseSendTransactionParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          SendTransactionData,\n          SendTransactionErrorType,\n          SendTransactionVariables<config, config['chains'][number]['id']>,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseSendTransactionReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  UseMutationReturnType<\n    SendTransactionData,\n    SendTransactionErrorType,\n    SendTransactionVariables<config, config['chains'][number]['id']>,\n    context\n  > & {\n    sendTransaction: SendTransactionMutate<config, context>\n    sendTransactionAsync: SendTransactionMutateAsync<config, context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useSendTransaction */\nexport function useSendTransaction<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseSendTransactionParameters<config, context> = {},\n): UseSendTransactionReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = sendTransactionMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseSendTransactionReturnType<config, context>\n  return {\n    ...result,\n    sendTransaction: mutate as Return['sendTransaction'],\n    sendTransactionAsync: mutateAsync as Return['sendTransactionAsync'],\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type {\n  Config,\n  ResolvedRegister,\n  ShowCallsStatusErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type ShowCallsStatusData,\n  type ShowCallsStatusMutate,\n  type ShowCallsStatusMutateAsync,\n  type ShowCallsStatusVariables,\n  showCallsStatusMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseShowCallsStatusParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          ShowCallsStatusData,\n          ShowCallsStatusErrorType,\n          ShowCallsStatusVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseShowCallsStatusReturnType<context = unknown> = Compute<\n  UseMutationReturnType<\n    ShowCallsStatusData,\n    ShowCallsStatusErrorType,\n    ShowCallsStatusVariables,\n    context\n  > & {\n    showCallsStatus: ShowCallsStatusMutate\n    showCallsStatusAsync: ShowCallsStatusMutateAsync\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useShowCallsStatus */\nexport function useShowCallsStatus<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseShowCallsStatusParameters<config, context> = {},\n): UseShowCallsStatusReturnType<context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = showCallsStatusMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseShowCallsStatusReturnType\n  return {\n    ...result,\n    showCallsStatus: mutate as Return['showCallsStatus'],\n    showCallsStatusAsync: mutateAsync as Return['showCallsStatusAsync'],\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { SignMessageErrorType } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type SignMessageData,\n  type SignMessageMutate,\n  type SignMessageMutateAsync,\n  type SignMessageVariables,\n  signMessageMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseSignMessageParameters<context = unknown> = Compute<\n  ConfigParameter & {\n    mutation?:\n      | UseMutationParameters<\n          SignMessageData,\n          SignMessageErrorType,\n          SignMessageVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseSignMessageReturnType<context = unknown> = Compute<\n  UseMutationReturnType<\n    SignMessageData,\n    SignMessageErrorType,\n    SignMessageVariables,\n    context\n  > & {\n    signMessage: SignMessageMutate<context>\n    signMessageAsync: SignMessageMutateAsync<context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useSignMessage */\nexport function useSignMessage<context = unknown>(\n  parameters: UseSignMessageParameters<context> = {},\n): UseSignMessageReturnType<context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = signMessageMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  return {\n    ...result,\n    signMessage: mutate,\n    signMessageAsync: mutateAsync,\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { SignTypedDataErrorType } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type SignTypedDataData,\n  type SignTypedDataMutate,\n  type SignTypedDataMutateAsync,\n  type SignTypedDataVariables,\n  signTypedDataMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseSignTypedDataParameters<context = unknown> = Compute<\n  ConfigParameter & {\n    mutation?:\n      | UseMutationParameters<\n          SignTypedDataData,\n          SignTypedDataErrorType,\n          SignTypedDataVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseSignTypedDataReturnType<context = unknown> = Compute<\n  UseMutationReturnType<\n    SignTypedDataData,\n    SignTypedDataErrorType,\n    SignTypedDataVariables,\n    context\n  > & {\n    signTypedData: SignTypedDataMutate<context>\n    signTypedDataAsync: SignTypedDataMutateAsync<context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useSignTypedData */\nexport function useSignTypedData<context = unknown>(\n  parameters: UseSignTypedDataParameters<context> = {},\n): UseSignTypedDataReturnType<context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = signTypedDataMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseSignTypedDataReturnType<context>\n  return {\n    ...result,\n    signTypedData: mutate as Return['signTypedData'],\n    signTypedDataAsync: mutateAsync as Return['signTypedDataAsync'],\n  }\n}\n", "'use client'\n\nimport type {\n  Config,\n  ResolvedRegister,\n  SimulateContractErrorType,\n} from '@wagmi/core'\nimport {\n  type SimulateContractData,\n  type SimulateContractOptions,\n  type SimulateContractQueryFnData,\n  type SimulateContractQueryKey,\n  simulateContractQueryOptions,\n} from '@wagmi/core/query'\nimport type { Abi, ContractFunctionArgs, ContractFunctionName } from 'viem'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\nimport { useConnectorClient } from './useConnectorClient.js'\n\nexport type UseSimulateContractParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'nonpayable' | 'payable'\n  > = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  > = ContractFunctionArgs<abi, 'nonpayable' | 'payable', functionName>,\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = SimulateContractData<abi, functionName, args, config, chainId>,\n> = SimulateContractOptions<abi, functionName, args, config, chainId> &\n  ConfigParameter<config> &\n  QueryParameter<\n    SimulateContractQueryFnData<abi, functionName, args, config, chainId>,\n    SimulateContractErrorType,\n    selectData,\n    SimulateContractQueryKey<abi, functionName, args, config, chainId>\n  >\n\nexport type UseSimulateContractReturnType<\n  abi extends Abi | readonly unknown[] = Abi,\n  functionName extends ContractFunctionName<\n    abi,\n    'nonpayable' | 'payable'\n  > = ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  > = ContractFunctionArgs<abi, 'nonpayable' | 'payable', functionName>,\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = SimulateContractData<abi, functionName, args, config, chainId>,\n> = UseQueryReturnType<selectData, SimulateContractErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useSimulateContract */\nexport function useSimulateContract<\n  const abi extends Abi | readonly unknown[],\n  functionName extends ContractFunctionName<abi, 'nonpayable' | 'payable'>,\n  args extends ContractFunctionArgs<\n    abi,\n    'nonpayable' | 'payable',\n    functionName\n  >,\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = SimulateContractData<abi, functionName, args, config, chainId>,\n>(\n  parameters: UseSimulateContractParameters<\n    abi,\n    functionName,\n    args,\n    config,\n    chainId,\n    selectData\n  > = {} as any,\n): UseSimulateContractReturnType<\n  abi,\n  functionName,\n  args,\n  config,\n  chainId,\n  selectData\n> {\n  const { abi, address, connector, functionName, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const { data: connectorClient } = useConnectorClient({\n    config,\n    connector,\n    query: { enabled: parameters.account === undefined },\n  })\n  const chainId = useChainId({ config })\n\n  const options = simulateContractQueryOptions<\n    config,\n    abi,\n    functionName,\n    args,\n    chainId\n  >(config, {\n    ...parameters,\n    account: parameters.account ?? connectorClient?.account,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(\n    abi && address && functionName && (query.enabled ?? true),\n  )\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetStorageAtErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetStorageAtData,\n  type GetStorageAtOptions,\n  type GetStorageAtQueryKey,\n  getStorageAtQueryOptions,\n} from '@wagmi/core/query'\nimport type { GetStorageAtQueryFnData } from '@wagmi/core/query'\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseStorageAtParameters<\n  config extends Config = Config,\n  selectData = GetStorageAtData,\n> = Compute<\n  GetStorageAtOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetStorageAtQueryFnData,\n      GetStorageAtErrorType,\n      selectData,\n      GetStorageAtQueryKey<config>\n    >\n>\n\nexport type UseStorageAtReturnType<selectData = GetStorageAtData> =\n  UseQueryReturnType<selectData, GetStorageAtErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useStorageAt */\nexport function useStorageAt<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetStorageAtData,\n>(\n  parameters: UseStorageAtParameters<config, selectData> = {},\n): UseStorageAtReturnType<selectData> {\n  const { address, slot, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getStorageAtQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && slot && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type {\n  Config,\n  Connector,\n  ResolvedRegister,\n  SwitchAccountErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type SwitchAccountData,\n  type SwitchAccountMutate,\n  type SwitchAccountMutateAsync,\n  type SwitchAccountVariables,\n  switchAccountMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\nimport { useConnections } from './useConnections.js'\n\nexport type UseSwitchAccountParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          SwitchAccountData<config>,\n          SwitchAccountErrorType,\n          SwitchAccountVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseSwitchAccountReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  UseMutationReturnType<\n    SwitchAccountData<config>,\n    SwitchAccountErrorType,\n    SwitchAccountVariables,\n    context\n  > & {\n    connectors: readonly Connector[]\n    switchAccount: SwitchAccountMutate<config, context>\n    switchAccountAsync: SwitchAccountMutateAsync<config, context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useSwitchAccount */\nexport function useSwitchAccount<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseSwitchAccountParameters<config, context> = {},\n): UseSwitchAccountReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = switchAccountMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  return {\n    ...result,\n    connectors: useConnections({ config }).map(\n      (connection) => connection.connector,\n    ),\n    switchAccount: mutate,\n    switchAccountAsync: mutateAsync,\n  }\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type {\n  Config,\n  ResolvedRegister,\n  SwitchChainErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type SwitchChainData,\n  type SwitchChainMutate,\n  type SwitchChainMutateAsync,\n  type SwitchChainVariables,\n  switchChainMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useChains } from './useChains.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseSwitchChainParameters<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  ConfigParameter<config> & {\n    mutation?:\n      | UseMutationParameters<\n          SwitchChainData<config, config['chains'][number]['id']>,\n          SwitchChainErrorType,\n          SwitchChainVariables<config, config['chains'][number]['id']>,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseSwitchChainReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = Compute<\n  UseMutationReturnType<\n    SwitchChainData<config, config['chains'][number]['id']>,\n    SwitchChainErrorType,\n    SwitchChainVariables<config, config['chains'][number]['id']>,\n    context\n  > & {\n    chains: config['chains']\n    switchChain: SwitchChainMutate<config, context>\n    switchChainAsync: SwitchChainMutateAsync<config, context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useSwitchChain */\nexport function useSwitchChain<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseSwitchChainParameters<config, context> = {},\n): UseSwitchChainReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = switchChainMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseSwitchChainReturnType<config, context>\n  return {\n    ...result,\n    chains: useChains({ config }) as unknown as config['chains'],\n    switchChain: mutate as Return['switchChain'],\n    switchChainAsync: mutateAsync as Return['switchChainAsync'],\n  }\n}\n", "'use client'\n\nimport type { Config, GetTokenErrorType, ResolvedRegister } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetTokenData,\n  type GetTokenOptions,\n  type GetTokenQueryFnData,\n  type GetTokenQueryKey,\n  getTokenQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseTokenParameters<\n  config extends Config = Config,\n  selectData = GetTokenData,\n> = Compute<\n  GetTokenOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetTokenQueryFnData,\n      GetTokenErrorType,\n      selectData,\n      GetTokenQueryKey<config>\n    >\n>\n\nexport type UseTokenReturnType<selectData = GetTokenData> = UseQueryReturnType<\n  selectData,\n  GetTokenErrorType\n>\n\n/**\n * @deprecated\n *\n * https://wagmi.sh/react/api/hooks/useToken\n */\nexport function useToken<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetTokenData,\n>(\n  parameters: UseTokenParameters<config, selectData> = {},\n): UseTokenReturnType<selectData> {\n  const { address, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getTokenQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetTransactionErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetTransactionData,\n  type GetTransactionOptions,\n  type GetTransactionQueryFnData,\n  type GetTransactionQueryKey,\n  getTransactionQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseTransactionParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetTransactionData<config, chainId>,\n> = Compute<\n  GetTransactionOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetTransactionQueryFnData<config, chainId>,\n      GetTransactionErrorType,\n      selectData,\n      GetTransactionQueryKey<config, chainId>\n    >\n>\n\nexport type UseTransactionReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetTransactionData<config, chainId>,\n> = UseQueryReturnType<selectData, GetTransactionErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useTransaction */\nexport function useTransaction<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetTransactionData<config, chainId>,\n>(\n  parameters: UseTransactionParameters<config, chainId, selectData> = {},\n): UseTransactionReturnType<config, chainId, selectData> {\n  const { blockHash, blockNumber, blockTag, hash, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getTransactionQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(\n    !(blockHash && blockNumber && blockTag && hash) && (query.enabled ?? true),\n  )\n\n  return useQuery({\n    ...(query as any),\n    ...options,\n    enabled,\n  }) as UseTransactionReturnType<config, chainId, selectData>\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetTransactionConfirmationsErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport {\n  type GetTransactionConfirmationsData,\n  type GetTransactionConfirmationsOptions,\n  type GetTransactionConfirmationsQueryFnData,\n  type GetTransactionConfirmationsQueryKey,\n  getTransactionConfirmationsQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseTransactionConfirmationsParameters<\n  config extends Config = Config,\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = GetTransactionConfirmationsData,\n> = GetTransactionConfirmationsOptions<config, chainId> &\n  ConfigParameter<config> &\n  QueryParameter<\n    GetTransactionConfirmationsQueryFnData,\n    GetTransactionConfirmationsErrorType,\n    selectData,\n    GetTransactionConfirmationsQueryKey<config, chainId>\n  >\n\nexport type UseTransactionConfirmationsReturnType<\n  selectData = GetTransactionConfirmationsData,\n> = UseQueryReturnType<selectData, GetTransactionConfirmationsErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useTransactionConfirmations */\nexport function useTransactionConfirmations<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends config['chains'][number]['id'] | undefined = undefined,\n  selectData = GetTransactionConfirmationsData,\n>(\n  parameters: UseTransactionConfirmationsParameters<\n    config,\n    chainId,\n    selectData\n  > = {} as any,\n): UseTransactionConfirmationsReturnType<selectData> {\n  const { hash, transactionReceipt, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getTransactionConfirmationsQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(\n    !(hash && transactionReceipt) &&\n      (hash || transactionReceipt) &&\n      (query.enabled ?? true),\n  )\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetTransactionCountErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport type { GetTransactionCountQueryFnData } from '@wagmi/core/query'\nimport {\n  type GetTransactionCountData,\n  type GetTransactionCountOptions,\n  type GetTransactionCountQueryKey,\n  getTransactionCountQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseTransactionCountParameters<\n  config extends Config = Config,\n  selectData = GetTransactionCountData,\n> = Compute<\n  GetTransactionCountOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetTransactionCountQueryFnData,\n      GetTransactionCountErrorType,\n      selectData,\n      GetTransactionCountQueryKey<config>\n    >\n>\n\nexport type UseTransactionCountReturnType<\n  selectData = GetTransactionCountData,\n> = UseQueryReturnType<selectData, GetTransactionCountErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useTransactionCount */\nexport function useTransactionCount<\n  config extends Config = ResolvedRegister['config'],\n  selectData = GetTransactionCountData,\n>(\n  parameters: UseTransactionCountParameters<config, selectData> = {},\n): UseTransactionCountReturnType<selectData> {\n  const { address, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getTransactionCountQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(address && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  GetTransactionReceiptErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type GetTransactionReceiptData,\n  type GetTransactionReceiptOptions,\n  type GetTransactionReceiptQueryKey,\n  getTransactionReceiptQueryOptions,\n} from '@wagmi/core/query'\nimport type { GetTransactionReceiptQueryFnData } from '@wagmi/core/query'\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseTransactionReceiptParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetTransactionReceiptData<config, chainId>,\n> = Compute<\n  GetTransactionReceiptOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      GetTransactionReceiptQueryFnData<config, chainId>,\n      GetTransactionReceiptErrorType,\n      selectData,\n      GetTransactionReceiptQueryKey<config, chainId>\n    >\n>\n\nexport type UseTransactionReceiptReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetTransactionReceiptData<config, chainId>,\n> = UseQueryReturnType<selectData, GetTransactionReceiptErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useTransactionReceipt */\nexport function useTransactionReceipt<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetTransactionReceiptData<config, chainId>,\n>(\n  parameters: UseTransactionReceiptParameters<config, chainId, selectData> = {},\n): UseTransactionReceiptReturnType<config, chainId, selectData> {\n  const { hash, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = getTransactionReceiptQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(hash && (query.enabled ?? true))\n\n  return useQuery({\n    ...(query as any),\n    ...options,\n    enabled,\n  }) as UseTransactionReceiptReturnType<config, chainId, selectData>\n}\n", "'use client'\n\nimport type {\n  Config,\n  ResolvedRegister,\n  VerifyMessageErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type VerifyMessageData,\n  type VerifyMessageOptions,\n  type VerifyMessageQueryKey,\n  verifyMessageQueryOptions,\n} from '@wagmi/core/query'\nimport type { VerifyMessageQueryFnData } from '@wagmi/core/query'\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseVerifyMessageParameters<\n  config extends Config = Config,\n  selectData = VerifyMessageData,\n> = Compute<\n  VerifyMessageOptions<config> &\n    ConfigParameter<config> &\n    QueryParameter<\n      VerifyMessageQueryFnData,\n      VerifyMessageErrorType,\n      selectData,\n      VerifyMessageQueryKey<config>\n    >\n>\n\nexport type UseVerifyMessageReturnType<selectData = VerifyMessageData> =\n  UseQueryReturnType<selectData, VerifyMessageErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useVerifyMessage */\nexport function useVerifyMessage<\n  config extends Config = ResolvedRegister['config'],\n  selectData = VerifyMessageData,\n>(\n  parameters: UseVerifyMessageParameters<config, selectData> = {},\n): UseVerifyMessageReturnType<selectData> {\n  const { address, message, signature, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = verifyMessageQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(\n    address && message && signature && (query.enabled ?? true),\n  )\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  ResolvedRegister,\n  VerifyTypedDataErrorType,\n} from '@wagmi/core'\nimport {\n  type VerifyTypedDataData,\n  type VerifyTypedDataOptions,\n  type VerifyTypedDataQueryKey,\n  verifyTypedDataQueryOptions,\n} from '@wagmi/core/query'\nimport type { VerifyTypedDataQueryFnData } from '@wagmi/core/query'\nimport type { TypedData } from 'viem'\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseVerifyTypedDataParameters<\n  typedData extends TypedData | Record<string, unknown> = TypedData,\n  primaryType extends keyof typedData | 'EIP712Domain' = keyof typedData,\n  config extends Config = Config,\n  selectData = VerifyTypedDataData,\n> = VerifyTypedDataOptions<typedData, primaryType, config> &\n  ConfigParameter<config> &\n  QueryParameter<\n    VerifyTypedDataQueryFnData,\n    VerifyTypedDataErrorType,\n    selectData,\n    VerifyTypedDataQueryKey<typedData, primaryType, config>\n  >\n\nexport type UseVerifyTypedDataReturnType<selectData = VerifyTypedDataData> =\n  UseQueryReturnType<selectData, VerifyTypedDataErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useVerifyTypedData */\nexport function useVerifyTypedData<\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n  config extends Config = ResolvedRegister['config'],\n  selectData = VerifyTypedDataData,\n>(\n  parameters: UseVerifyTypedDataParameters<\n    typedData,\n    primaryType,\n    config,\n    selectData\n  > = {} as any,\n): UseVerifyTypedDataReturnType<selectData> {\n  const {\n    address,\n    message,\n    primaryType,\n    signature,\n    types,\n    query = {},\n  } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = verifyTypedDataQueryOptions<config, typedData, primaryType>(\n    config,\n    {\n      ...parameters,\n      chainId: parameters.chainId ?? chainId,\n    },\n  )\n  const enabled = Boolean(\n    address &&\n      message &&\n      primaryType &&\n      signature &&\n      types &&\n      (query.enabled ?? true),\n  )\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\n// Almost identical implementation to `useConnectorClient` (except for return type)\n// Should update both in tandem\n\nimport { useQueryClient } from '@tanstack/react-query'\nimport type {\n  Config,\n  GetWalletClientErrorType,\n  ResolvedRegister,\n} from '@wagmi/core'\nimport type { Compute, Omit } from '@wagmi/core/internal'\nimport {\n  type GetWalletClientData,\n  type GetWalletClientOptions,\n  type GetWalletClientQueryFnData,\n  type GetWalletClientQueryKey,\n  getWalletClientQueryOptions,\n} from '@wagmi/core/query'\nimport { useEffect, useRef } from 'react'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport {\n  type UseQueryParameters,\n  type UseQueryReturnType,\n  useQuery,\n} from '../utils/query.js'\nimport { useAccount } from './useAccount.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWalletClientParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetWalletClientData<config, chainId>,\n> = Compute<\n  GetWalletClientOptions<config, chainId> &\n    ConfigParameter<config> & {\n      query?:\n        | Compute<\n            Omit<\n              UseQueryParameters<\n                GetWalletClientQueryFnData<config, chainId>,\n                GetWalletClientErrorType,\n                selectData,\n                GetWalletClientQueryKey<config, chainId>\n              >,\n              'gcTime' | 'staleTime'\n            >\n          >\n        | undefined\n    }\n>\n\nexport type UseWalletClientReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetWalletClientData<config, chainId>,\n> = UseQueryReturnType<selectData, GetWalletClientErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useWalletClient */\nexport function useWalletClient<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = GetWalletClientData<config, chainId>,\n>(\n  parameters: UseWalletClientParameters<config, chainId, selectData> = {},\n): UseWalletClientReturnType<config, chainId, selectData> {\n  const { query = {}, ...rest } = parameters\n\n  const config = useConfig(rest)\n  const queryClient = useQueryClient()\n  const { address, connector, status } = useAccount({ config })\n  const chainId = useChainId({ config })\n  const activeConnector = parameters.connector ?? connector\n\n  const { queryKey, ...options } = getWalletClientQueryOptions<config, chainId>(\n    config,\n    {\n      ...parameters,\n      chainId: parameters.chainId ?? chainId,\n      connector: parameters.connector ?? connector,\n    },\n  )\n  const enabled = Boolean(\n    (status === 'connected' ||\n      (status === 'reconnecting' && activeConnector?.getProvider)) &&\n      (query.enabled ?? true),\n  )\n\n  const addressRef = useRef(address)\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `queryKey` not required\n  useEffect(() => {\n    const previousAddress = addressRef.current\n    if (!address && previousAddress) {\n      // remove when account is disconnected\n      queryClient.removeQueries({ queryKey })\n      addressRef.current = undefined\n    } else if (address !== previousAddress) {\n      // invalidate when address changes\n      queryClient.invalidateQueries({ queryKey })\n      addressRef.current = address\n    }\n  }, [address, queryClient])\n\n  return useQuery({\n    ...query,\n    ...options,\n    queryKey,\n    enabled,\n    staleTime: Number.POSITIVE_INFINITY,\n  } as any) as UseWalletClientReturnType<config, chainId, selectData>\n}\n", "'use client'\n\nimport type {\n  Config,\n  ResolvedRegister,\n  WaitForCallsStatusErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type WaitForCallsStatusData,\n  type WaitForCallsStatusOptions,\n  type WaitForCallsStatusQueryFnData,\n  type WaitForCallsStatusQueryKey,\n  waitForCallsStatusQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWaitForCallsStatusParameters<\n  config extends Config = Config,\n  selectData = WaitForCallsStatusData,\n> = Compute<\n  WaitForCallsStatusOptions &\n    ConfigParameter<config> &\n    QueryParameter<\n      WaitForCallsStatusQueryFnData,\n      WaitForCallsStatusErrorType,\n      selectData,\n      WaitForCallsStatusQueryKey\n    >\n>\n\nexport type UseWaitForCallsStatusReturnType<\n  selectData = WaitForCallsStatusData,\n> = UseQueryReturnType<selectData, WaitForCallsStatusErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useWaitForCallsStatus */\nexport function useWaitForCallsStatus<\n  config extends Config = ResolvedRegister['config'],\n  selectData = WaitForCallsStatusData,\n>(\n  parameters: UseWaitForCallsStatusParameters<config, selectData>,\n): UseWaitForCallsStatusReturnType<selectData> {\n  const { id, query = {} } = parameters\n\n  const config = useConfig(parameters)\n\n  const options = waitForCallsStatusQueryOptions(config, parameters)\n  const enabled = Boolean(id && (query.enabled ?? true))\n\n  return useQuery({ ...query, ...options, enabled })\n}\n", "'use client'\n\nimport type {\n  Config,\n  ResolvedRegister,\n  WaitForTransactionReceiptErrorType,\n} from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type WaitForTransactionReceiptData,\n  type WaitForTransactionReceiptOptions,\n  type WaitForTransactionReceiptQueryFnData,\n  type WaitForTransactionReceiptQueryKey,\n  waitForTransactionReceiptQueryOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter, QueryParameter } from '../types/properties.js'\nimport { type UseQueryReturnType, useQuery } from '../utils/query.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWaitForTransactionReceiptParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = WaitForTransactionReceiptData<config, chainId>,\n> = Compute<\n  WaitForTransactionReceiptOptions<config, chainId> &\n    ConfigParameter<config> &\n    QueryParameter<\n      WaitForTransactionReceiptQueryFnData<config, chainId>,\n      WaitForTransactionReceiptErrorType,\n      selectData,\n      WaitForTransactionReceiptQueryKey<config, chainId>\n    >\n>\n\nexport type UseWaitForTransactionReceiptReturnType<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = WaitForTransactionReceiptData<config, chainId>,\n> = UseQueryReturnType<selectData, WaitForTransactionReceiptErrorType>\n\n/** https://wagmi.sh/react/api/hooks/useWaitForTransactionReceipt */\nexport function useWaitForTransactionReceipt<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n  selectData = WaitForTransactionReceiptData<config, chainId>,\n>(\n  parameters: UseWaitForTransactionReceiptParameters<\n    config,\n    chainId,\n    selectData\n  > = {},\n): UseWaitForTransactionReceiptReturnType<config, chainId, selectData> {\n  const { hash, query = {} } = parameters\n\n  const config = useConfig(parameters)\n  const chainId = useChainId({ config })\n\n  const options = waitForTransactionReceiptQueryOptions(config, {\n    ...parameters,\n    chainId: parameters.chainId ?? chainId,\n  })\n  const enabled = Boolean(hash && (query.enabled ?? true))\n\n  return useQuery({\n    ...(query as any),\n    ...options,\n    enabled,\n  }) as UseWaitForTransactionReceiptReturnType<config, chainId, selectData>\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type { WatchAssetErrorType } from '@wagmi/core'\nimport type { Compute } from '@wagmi/core/internal'\nimport {\n  type WatchAssetData,\n  type WatchAssetMutate,\n  type WatchAssetMutateAsync,\n  type WatchAssetVariables,\n  watchAssetMutationOptions,\n} from '@wagmi/core/query'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWatchAssetParameters<context = unknown> = Compute<\n  ConfigParameter & {\n    mutation?:\n      | UseMutationParameters<\n          WatchAssetData,\n          WatchAssetErrorType,\n          WatchAssetVariables,\n          context\n        >\n      | undefined\n  }\n>\n\nexport type UseWatchAssetReturnType<context = unknown> = Compute<\n  UseMutationReturnType<\n    WatchAssetData,\n    WatchAssetErrorType,\n    WatchAssetVariables,\n    context\n  > & {\n    watchAsset: WatchAssetMutate<context>\n    watchAssetAsync: WatchAssetMutateAsync<context>\n  }\n>\n\n/** https://wagmi.sh/react/api/hooks/useWatchAsset */\nexport function useWatchAsset<context = unknown>(\n  parameters: UseWatchAssetParameters<context> = {},\n): UseWatchAssetReturnType<context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = watchAssetMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  return {\n    ...result,\n    watchAsset: mutate,\n    watchAssetAsync: mutateAsync,\n  }\n}\n", "'use client'\n\nimport {\n  type Config,\n  type ResolvedRegister,\n  type WatchContractEventParameters,\n  watchContractEvent,\n} from '@wagmi/core'\nimport type { UnionCompute, UnionExactPartial } from '@wagmi/core/internal'\nimport { useEffect } from 'react'\nimport type { Abi, ContractEventName } from 'viem'\n\nimport type { ConfigParameter, EnabledParameter } from '../types/properties.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWatchContractEventParameters<\n  abi extends Abi | readonly unknown[] = Abi,\n  eventName extends ContractEventName<abi> = ContractEventName<abi>,\n  strict extends boolean | undefined = undefined,\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = UnionCompute<\n  UnionExactPartial<\n    WatchContractEventParameters<abi, eventName, strict, config, chainId>\n  > &\n    ConfigParameter<config> &\n    EnabledParameter\n>\n\nexport type UseWatchContractEventReturnType = void\n\n/** https://wagmi.sh/react/api/hooks/useWatchContractEvent */\nexport function useWatchContractEvent<\n  const abi extends Abi | readonly unknown[],\n  eventName extends ContractEventName<abi>,\n  strict extends boolean | undefined = undefined,\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  parameters: UseWatchContractEventParameters<\n    abi,\n    eventName,\n    strict,\n    config,\n    chainId\n  > = {} as any,\n): UseWatchContractEventReturnType {\n  const { enabled = true, onLogs, config: _, ...rest } = parameters\n\n  const config = useConfig(parameters)\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  // TODO(react@19): cleanup\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `rest` changes every render so only including properties in dependency array\n  useEffect(() => {\n    if (!enabled) return\n    if (!onLogs) return\n    return watchContractEvent(config, {\n      ...(rest as any),\n      chainId,\n      onLogs,\n    })\n  }, [\n    chainId,\n    config,\n    enabled,\n    onLogs,\n    ///\n    rest.abi,\n    rest.address,\n    rest.args,\n    rest.batch,\n    rest.eventName,\n    rest.fromBlock,\n    rest.onError,\n    rest.poll,\n    rest.pollingInterval,\n    rest.strict,\n    rest.syncConnectedChain,\n  ])\n}\n", "'use client'\n\nimport {\n  type Config,\n  type ResolvedRegister,\n  type WatchPendingTransactionsParameters,\n  watchPendingTransactions,\n} from '@wagmi/core'\nimport type { UnionCompute, UnionExactPartial } from '@wagmi/core/internal'\nimport { useEffect } from 'react'\n\nimport type { ConfigParameter, EnabledParameter } from '../types/properties.js'\nimport { useChainId } from './useChainId.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWatchPendingTransactionsParameters<\n  config extends Config = Config,\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n> = UnionCompute<\n  UnionExactPartial<WatchPendingTransactionsParameters<config, chainId>> &\n    ConfigParameter<config> &\n    EnabledParameter\n>\n\nexport type UseWatchPendingTransactionsReturnType = void\n\n/** https://wagmi.sh/react/api/hooks/useWatchPendingTransactions */\nexport function useWatchPendingTransactions<\n  config extends Config = ResolvedRegister['config'],\n  chainId extends\n    config['chains'][number]['id'] = config['chains'][number]['id'],\n>(\n  parameters: UseWatchPendingTransactionsParameters<\n    config,\n    chainId\n  > = {} as any,\n): UseWatchPendingTransactionsReturnType {\n  const { enabled = true, onTransactions, config: _, ...rest } = parameters\n\n  const config = useConfig(parameters)\n  const configChainId = useChainId({ config })\n  const chainId = parameters.chainId ?? configChainId\n\n  // TODO(react@19): cleanup\n  // biome-ignore lint/correctness/useExhaustiveDependencies: `rest` changes every render so only including properties in dependency array\n  useEffect(() => {\n    if (!enabled) return\n    if (!onTransactions) return\n    return watchPendingTransactions(config, {\n      ...(rest as any),\n      chainId,\n      onTransactions,\n    })\n  }, [\n    chainId,\n    config,\n    enabled,\n    onTransactions,\n    ///\n    rest.batch,\n    rest.onError,\n    rest.poll,\n    rest.pollingInterval,\n    rest.syncConnectedChain,\n  ])\n}\n", "'use client'\n\nimport { useMutation } from '@tanstack/react-query'\nimport type {\n  Config,\n  ResolvedRegister,\n  WriteContractErrorType,\n} from '@wagmi/core'\nimport {\n  type WriteContractData,\n  type WriteContractMutate,\n  type WriteContractMutateAsync,\n  type WriteContractVariables,\n  writeContractMutationOptions,\n} from '@wagmi/core/query'\nimport type { Abi } from 'viem'\n\nimport type { ConfigParameter } from '../types/properties.js'\nimport type {\n  UseMutationParameters,\n  UseMutationReturnType,\n} from '../utils/query.js'\nimport { useConfig } from './useConfig.js'\n\nexport type UseWriteContractParameters<\n  config extends Config = Config,\n  context = unknown,\n> = ConfigParameter<config> & {\n  mutation?:\n    | UseMutationParameters<\n        WriteContractData,\n        WriteContractErrorType,\n        WriteContractVariables<\n          Abi,\n          string,\n          readonly unknown[],\n          config,\n          config['chains'][number]['id']\n        >,\n        context\n      >\n    | undefined\n}\n\nexport type UseWriteContractReturnType<\n  config extends Config = Config,\n  context = unknown,\n> = UseMutationReturnType<\n  WriteContractData,\n  WriteContractErrorType,\n  WriteContractVariables<\n    Abi,\n    string,\n    readonly unknown[],\n    config,\n    config['chains'][number]['id']\n  >,\n  context\n> & {\n  writeContract: WriteContractMutate<config, context>\n  writeContractAsync: WriteContractMutateAsync<config, context>\n}\n\n/** https://wagmi.sh/react/api/hooks/useWriteContract */\nexport function useWriteContract<\n  config extends Config = ResolvedRegister['config'],\n  context = unknown,\n>(\n  parameters: UseWriteContractParameters<config, context> = {},\n): UseWriteContractReturnType<config, context> {\n  const { mutation } = parameters\n\n  const config = useConfig(parameters)\n\n  const mutationOptions = writeContractMutationOptions(config)\n  const { mutate, mutateAsync, ...result } = useMutation({\n    ...mutation,\n    ...mutationOptions,\n  })\n\n  type Return = UseWriteContractReturnType<config, context>\n  return {\n    ...result,\n    writeContract: mutate as Return['writeContract'],\n    writeContractAsync: mutateAsync as Return['writeContractAsync'],\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,6BACE,WAAW,MAAM,oBACf,oBAAoB,MACtB,QAAQ;AAAA,UACN;AAAA,QACF;AACF,YAAI,QAAQ,YAAY;AACxB,YAAI,CAAC,4BAA4B;AAC/B,cAAI,cAAc,YAAY;AAC9B,mBAAS,OAAO,WAAW,MACxB,QAAQ;AAAA,YACP;AAAA,UACF,GACC,6BAA6B;AAAA,QAClC;AACA,sBAAc,SAAS;AAAA,UACrB,MAAM,EAAE,OAAc,YAAyB;AAAA,QACjD,CAAC;AACD,YAAI,OAAO,YAAY,CAAC,EAAE,MACxB,cAAc,YAAY,CAAC;AAC7B;AAAA,UACE,WAAY;AACV,iBAAK,QAAQ;AACb,iBAAK,cAAc;AACnB,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,UAC5D;AAAA,UACA,CAAC,WAAW,OAAO,WAAW;AAAA,QAChC;AACA,QAAAA;AAAA,UACE,WAAY;AACV,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAC1D,mBAAO,UAAU,WAAY;AAC3B,qCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,YAC5D,CAAC;AAAA,UACH;AAAA,UACA,CAAC,SAAS;AAAA,QACZ;AACA,sBAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,eAAS,uBAAuB,MAAM;AACpC,YAAI,oBAAoB,KAAK;AAC7B,eAAO,KAAK;AACZ,YAAI;AACF,cAAI,YAAY,kBAAkB;AAClC,iBAAO,CAAC,SAAS,MAAM,SAAS;AAAA,QAClC,SAAS,OAAO;AACd,iBAAO;AAAA,QACT;AAAA,MACF;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,eAAO,YAAY;AAAA,MACrB;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAI,QAAQ,iBACV,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzD,WAAW,MAAM,UACjBA,cAAY,MAAM,WAClB,kBAAkB,MAAM,iBACxB,gBAAgB,MAAM,eACtB,oBAAoB,OACpB,6BAA6B,OAC7B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,YAC9B,gBAAgB,OAAO,OAAO,SAAS,gBACnC,yBACA;AACR,cAAQ,uBACN,WAAW,MAAM,uBAAuB,MAAM,uBAAuB;AACvE,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AC9FL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAI,QAAQ,iBACV,OAAO,gBACP,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzDC,wBAAuB,KAAK,sBAC5BC,UAAS,MAAM,QACfC,cAAY,MAAM,WAClBC,WAAU,MAAM,SAChB,gBAAgB,MAAM;AACxB,cAAQ,mCAAmC,SACzC,WACA,aACA,mBACA,UACA,SACA;AACA,YAAI,UAAUF,QAAO,IAAI;AACzB,YAAI,SAAS,QAAQ,SAAS;AAC5B,cAAI,OAAO,EAAE,UAAU,OAAI,OAAO,KAAK;AACvC,kBAAQ,UAAU;AAAA,QACpB,MAAO,QAAO,QAAQ;AACtB,kBAAUE;AAAA,UACR,WAAY;AACV,qBAAS,iBAAiB,cAAc;AACtC,kBAAI,CAAC,SAAS;AACZ,0BAAU;AACV,mCAAmB;AACnB,+BAAe,SAAS,YAAY;AACpC,oBAAI,WAAW,WAAW,KAAK,UAAU;AACvC,sBAAI,mBAAmB,KAAK;AAC5B,sBAAI,QAAQ,kBAAkB,YAAY;AACxC,2BAAQ,oBAAoB;AAAA,gBAChC;AACA,uBAAQ,oBAAoB;AAAA,cAC9B;AACA,iCAAmB;AACnB,kBAAI,SAAS,kBAAkB,YAAY;AACzC,uBAAO;AACT,kBAAI,gBAAgB,SAAS,YAAY;AACzC,kBAAI,WAAW,WAAW,QAAQ,kBAAkB,aAAa;AAC/D,uBAAQ,mBAAmB,cAAe;AAC5C,iCAAmB;AACnB,qBAAQ,oBAAoB;AAAA,YAC9B;AACA,gBAAI,UAAU,OACZ,kBACA,mBACA,yBACE,WAAW,oBAAoB,OAAO;AAC1C,mBAAO;AAAA,cACL,WAAY;AACV,uBAAO,iBAAiB,YAAY,CAAC;AAAA,cACvC;AAAA,cACA,SAAS,yBACL,SACA,WAAY;AACV,uBAAO,iBAAiB,uBAAuB,CAAC;AAAA,cAClD;AAAA,YACN;AAAA,UACF;AAAA,UACA,CAAC,aAAa,mBAAmB,UAAU,OAAO;AAAA,QACpD;AACA,YAAI,QAAQH,sBAAqB,WAAW,QAAQ,CAAC,GAAG,QAAQ,CAAC,CAAC;AAClE,QAAAE;AAAA,UACE,WAAY;AACV,iBAAK,WAAW;AAChB,iBAAK,QAAQ;AAAA,UACf;AAAA,UACA,CAAC,KAAK;AAAA,QACR;AACA,sBAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AChGL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACHA,IAAAE,gBAA6C;;;AC6EvC,SAAU,gBAUd,mBAAoC;AACpC,SAAO;AACT;;;ACxDA,SAAS,OAAO;AACV,SAAU,SAAS,aAAiC,CAAA,GAAE;AAC1D,QAAM,EAAE,iBAAiB,MAAM,yBAAwB,IAAK;AAE5D,WAAS,YAAS;AAChB,UAAM,SAAS,WAAW;AAC1B,QAAI,OAAO,WAAW,YAAY;AAChC,YAAM,SAAS,OAAM;AACrB,UAAI;AAAQ,eAAO;IACrB;AAEA,QAAI,OAAO,WAAW;AAAU,aAAO;AAEvC,QAAI,OAAO,WAAW;AACpB,aAAO;QACL,GAAI,UAAU,MAAgC,KAAK;UACjD,IAAI;UACJ,MAAM,GAAG,OAAO,CAAC,EAAG,YAAW,CAAE,GAAG,OAAO,MAAM,CAAC,CAAC;UACnD,UAAU,KAAK,OAAO,CAAC,EAAG,YAAW,CAAE,GAAG,OAAO,MAAM,CAAC,CAAC;;;AAI/D,WAAO;MACL,IAAI;MACJ,MAAM;MACN,SAASC,SAAM;AACb,eAAOA,WAAA,gBAAAA,QAAQ;MACjB;;EAEJ;AAUA,MAAI;AACJ,MAAI;AACJ,MAAIC;AACJ,MAAIC;AAEJ,SAAO,gBAAmD,CAAC,YAAY;IACrE,IAAI,OAAI;AACN,aAAO,UAAS,EAAG;IACrB;IACA,IAAI,KAAE;AACJ,aAAO,UAAS,EAAG;IACrB;IACA,IAAI,OAAI;AACN,aAAO,UAAS,EAAG;IACrB;;IAEA,IAAI,qBAAkB;AACpB,aAAO;IACT;IACA,MAAM,SAAS;IACf,MAAM,QAAK;AACT,YAAM,WAAW,MAAM,KAAK,YAAW;AAEvC,WAAI,qCAAU,OAAM,WAAW,QAAQ;AACrC,YAAI,CAACD,UAAS;AACZ,UAAAA,WAAU,KAAK,UAAU,KAAK,IAAI;AAClC,mBAAS,GAAG,WAAWA,QAAO;QAChC;AAIA,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,mBAAS,GAAG,mBAAmB,eAAe;QAChD;MACF;IACF;IACA,MAAM,QAAQ,EAAE,SAAS,eAAc,IAAK,CAAA,GAAE;AAhHlD;AAiHM,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAE9C,UAAI,WAA+B,CAAA;AACnC,UAAI;AAAgB,mBAAW,MAAM,KAAK,YAAW,EAAG,MAAM,MAAM,CAAA,CAAE;eAC7D,gBAAgB;AAEvB,YAAI;AACF,gBAAM,cAAc,MAAM,SAAS,QAAQ;YACzC,QAAQ;YACR,QAAQ,CAAC,EAAE,cAAc,CAAA,EAAE,CAAE;WAC9B;AACD,sBAAY,mCAAY,CAAC,MAAb,mBAAgB,YAAhB,mBAA0B,OAA1B,mBAA8B,UAA9B,mBAAkD,IAC5D,CAAC,MAAM,WAAW,CAAC;AAKrB,cAAI,SAAS,SAAS,GAAG;AACvB,kBAAM,iBAAiB,MAAM,KAAK,YAAW;AAC7C,uBAAW;UACb;QACF,SAAS,KAAK;AACZ,gBAAM,QAAQ;AAGd,cAAI,MAAM,SAAS,yBAAyB;AAC1C,kBAAM,IAAI,yBAAyB,KAAK;AAE1C,cAAI,MAAM,SAAS,4BAA4B;AAAM,kBAAM;QAC7D;MACF;AAEA,UAAI;AACF,YAAI,EAAC,qCAAU,WAAU,CAAC,gBAAgB;AACxC,gBAAM,oBAAoB,MAAM,SAAS,QAAQ;YAC/C,QAAQ;WACT;AACD,qBAAW,kBAAkB,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;QACvD;AAIA,YAAIA,UAAS;AACX,mBAAS,eAAe,WAAWA,QAAO;AAC1C,UAAAA,WAAU;QACZ;AACA,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,mBAAS,GAAG,mBAAmB,eAAe;QAChD;AACA,YAAI,CAAC,cAAc;AACjB,yBAAe,KAAK,eAAe,KAAK,IAAI;AAC5C,mBAAS,GAAG,gBAAgB,YAAY;QAC1C;AACA,YAAI,CAACC,aAAY;AACf,UAAAA,cAAa,KAAK,aAAa,KAAK,IAAI;AACxC,mBAAS,GAAG,cAAcA,WAAU;QACtC;AAGA,YAAI,iBAAiB,MAAM,KAAK,WAAU;AAC1C,YAAI,WAAW,mBAAmB,SAAS;AACzC,gBAAM,QAAQ,MAAM,KAAK,YAAa,EAAE,QAAO,CAAE,EAAE,MAAM,CAAC,UAAS;AACjE,gBAAI,MAAM,SAAS,yBAAyB;AAAM,oBAAM;AACxD,mBAAO,EAAE,IAAI,eAAc;UAC7B,CAAC;AACD,4BAAiB,+BAAO,OAAM;QAChC;AAGA,YAAI;AACF,kBAAM,YAAO,YAAP,mBAAgB,WAAW,GAAG,KAAK,EAAE;AAG7C,YAAI,CAAC,WAAW;AACd,kBAAM,YAAO,YAAP,mBAAgB,QAAQ,sBAAsB;AAEtD,eAAO,EAAE,UAAU,SAAS,eAAc;MAC5C,SAAS,KAAK;AACZ,cAAM,QAAQ;AACd,YAAI,MAAM,SAAS,yBAAyB;AAC1C,gBAAM,IAAI,yBAAyB,KAAK;AAC1C,YAAI,MAAM,SAAS,4BAA4B;AAC7C,gBAAM,IAAI,4BAA4B,KAAK;AAC7C,cAAM;MACR;IACF;IACA,MAAM,aAAU;AAzMpB;AA0MM,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAG9C,UAAI,cAAc;AAChB,iBAAS,eAAe,gBAAgB,YAAY;AACpD,uBAAe;MACjB;AACA,UAAIA,aAAY;AACd,iBAAS,eAAe,cAAcA,WAAU;AAChD,QAAAA,cAAa;MACf;AACA,UAAI,CAACD,UAAS;AACZ,QAAAA,WAAU,KAAK,UAAU,KAAK,IAAI;AAClC,iBAAS,GAAG,WAAWA,QAAO;MAChC;AAIA,UAAI;AAGF,cAAM,YACJ;;UAEE,SAAS,QAIN;;YAED,QAAQ;YACR,QAAQ,CAAC,EAAE,cAAc,CAAA,EAAE,CAAE;WAC9B;WACH,EAAE,SAAS,IAAG,CAAE;MAEpB,QAAQ;MAAC;AAGT,UAAI,gBAAgB;AAClB,gBAAM,YAAO,YAAP,mBAAgB,QAAQ,GAAG,KAAK,EAAE,iBAAiB;MAC3D;AAEA,UAAI,CAAC,WAAW;AACd,gBAAM,YAAO,YAAP,mBAAgB,WAAW;IACrC;IACA,MAAM,cAAW;AACf,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAC9C,YAAM,WAAW,MAAM,SAAS,QAAQ,EAAE,QAAQ,eAAc,CAAE;AAClE,aAAO,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;IAC1C;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAC9C,YAAM,aAAa,MAAM,SAAS,QAAQ,EAAE,QAAQ,cAAa,CAAE;AACnE,aAAO,OAAO,UAAU;IAC1B;IACA,MAAM,cAAW;AACf,UAAI,OAAO,WAAW;AAAa,eAAO;AAE1C,UAAI;AACJ,YAAM,SAAS,UAAS;AACxB,UAAI,OAAO,OAAO,aAAa;AAC7B,mBAAW,OAAO,SAAS,MAA4B;eAChD,OAAO,OAAO,aAAa;AAClC,mBAAW,aAAa,QAAQ,OAAO,QAAQ;;AAC5C,mBAAW,OAAO;AAIvB,UAAI,YAAY,CAAC,SAAS,gBAAgB;AAExC,YAAI,SAAS,YAAY,OAAO,SAAS,QAAQ;AAC/C,mBAAS,iBACP,SAAS;;AACR,mBAAS,iBAAiB,MAAK;UAAE;MACxC;AAEA,aAAO;IACT;IACA,MAAM,eAAY;AA3RtB;AA4RM,UAAI;AACF,cAAM,iBACJ;QAEC,QAAM,YAAO,YAAP,mBAAgB,QAAQ,GAAG,KAAK,EAAE;AAC3C,YAAI;AAAgB,iBAAO;AAK3B,YAAI,CAAC,WAAW,QAAQ;AACtB,gBAAM,YAAY,QAAM,YAAO,YAAP,mBAAgB,QAAQ;AAChD,cAAI,CAAC;AAAW,mBAAO;QACzB;AAEA,cAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAI,CAAC,UAAU;AACb,cACE,6BAA6B,UAC7B,6BAA6B,OAC7B;AAIA,kBAAM,iBAAiB,YAAW;AAChC,kBAAI,OAAO,WAAW;AACpB,uBAAO,oBACL,wBACA,cAAc;AAElB,oBAAME,YAAW,MAAM,KAAK,YAAW;AACvC,qBAAO,CAAC,CAACA;YACX;AACA,kBAAM,UACJ,OAAO,6BAA6B,WAChC,2BACA;AACN,kBAAM,MAAM,MAAM,QAAQ,KAAK;cAC7B,GAAI,OAAO,WAAW,cAClB;gBACE,IAAI,QAAiB,CAAC,YACpB,OAAO,iBACL,wBACA,MAAM,QAAQ,eAAc,CAAE,GAC9B,EAAE,MAAM,KAAI,CAAE,CACf;kBAGL,CAAA;cACJ,IAAI,QAAiB,CAAC,YACpB,WAAW,MAAM,QAAQ,eAAc,CAAE,GAAG,OAAO,CAAC;aAEvD;AACD,gBAAI;AAAK,qBAAO;UAClB;AAEA,gBAAM,IAAI,sBAAqB;QACjC;AAIA,cAAM,WAAW,MAAM,UAAU,MAAM,KAAK,YAAW,CAAE;AACzD,eAAO,CAAC,CAAC,SAAS;MACpB,QAAQ;AACN,eAAO;MACT;IACF;IACA,MAAM,YAAY,EAAE,2BAA2B,QAAO,GAAE;AA/V5D;AAgWM,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,CAAC;AAAU,cAAM,IAAI,sBAAqB;AAE9C,YAAM,QAAQ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACxD,UAAI,CAAC;AAAO,cAAM,IAAI,iBAAiB,IAAI,wBAAuB,CAAE;AAEpE,YAAM,UAAU,IAAI,QAAc,CAAC,YAAW;AAC5C,cAAM,WAAY,CAAC,SAAQ;AACzB,cAAI,aAAa,QAAQ,KAAK,YAAY,SAAS;AACjD,mBAAO,QAAQ,IAAI,UAAU,QAAQ;AACrC,oBAAO;UACT;QACF;AACA,eAAO,QAAQ,GAAG,UAAU,QAAQ;MACtC,CAAC;AAED,UAAI;AACF,cAAM,QAAQ,IAAI;UAChB,SACG,QAAQ;YACP,QAAQ;YACR,QAAQ,CAAC,EAAE,SAAS,YAAY,OAAO,EAAC,CAAE;WAC3C,EAMA,KAAK,YAAW;AACf,kBAAM,iBAAiB,MAAM,KAAK,WAAU;AAC5C,gBAAI,mBAAmB;AACrB,qBAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;UAC7C,CAAC;UACH;SACD;AACD,eAAO;MACT,SAAS,KAAK;AACZ,cAAM,QAAQ;AAGd,YACE,MAAM,SAAS;;UAGd,0CACG,SADH,mBACS,kBADT,mBACwB,UAAS,MAClC;AACA,cAAI;AACF,kBAAM,EAAE,SAAS,eAAe,GAAG,eAAc,IAC/C,MAAM,kBAAkB,CAAA;AAC1B,gBAAI;AACJ,gBAAI,uEAA2B;AAC7B,kCAAoB,0BAA0B;qBACvC;AACP,kCAAoB;gBAClB,cAAc;gBACd,GAAG,OAAO,OAAO,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG;;AAGrD,gBAAI;AACJ,iBAAI,4EAA2B,YAA3B,mBAAoC;AACtC,wBAAU,0BAA0B;;AACjC,wBAAU,GAAC,WAAM,QAAQ,YAAd,mBAAuB,KAAK,OAAM,EAAE;AAEpD,kBAAM,mBAAmB;cACvB;cACA,SAAS,YAAY,OAAO;cAC5B,YAAW,uEAA2B,cAAa,MAAM;cACzD,UAAU,uEAA2B;cACrC,iBACE,uEAA2B,mBAC3B,MAAM;cACR;;AAGF,kBAAM,QAAQ,IAAI;cAChB,SACG,QAAQ;gBACP,QAAQ;gBACR,QAAQ,CAAC,gBAAgB;eAC1B,EACA,KAAK,YAAW;AACf,sBAAM,iBAAiB,MAAM,KAAK,WAAU;AAC5C,oBAAI,mBAAmB;AACrB,yBAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;;AAEzC,wBAAM,IAAI,yBACR,IAAI,MAAM,4CAA4C,CAAC;cAE7D,CAAC;cACH;aACD;AAED,mBAAO;UACT,SAASC,QAAO;AACd,kBAAM,IAAI,yBAAyBA,MAAc;UACnD;QACF;AAEA,YAAI,MAAM,SAAS,yBAAyB;AAC1C,gBAAM,IAAI,yBAAyB,KAAK;AAC1C,cAAM,IAAI,iBAAiB,KAAK;MAClC;IACF;IACA,MAAM,kBAAkB,UAAQ;AAxcpC;AA0cM,UAAI,SAAS,WAAW;AAAG,aAAK,aAAY;eAEnC,OAAO,QAAQ,cAAc,SAAS,GAAG;AAChD,cAAM,WAAW,MAAM,KAAK,WAAU,GAAI,SAAQ;AAClD,aAAK,UAAU,EAAE,QAAO,CAAE;AAE1B,YAAI;AACF,kBAAM,YAAO,YAAP,mBAAgB,WAAW,GAAG,KAAK,EAAE;MAC/C;AAGE,eAAO,QAAQ,KAAK,UAAU;UAC5B,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;SAC5C;IACL;IACA,eAAe,OAAK;AAClB,YAAM,UAAU,OAAO,KAAK;AAC5B,aAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;IAC3C;IACA,MAAM,UAAU,aAAW;AACzB,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,SAAS,WAAW;AAAG;AAE3B,YAAM,UAAU,OAAO,YAAY,OAAO;AAC1C,aAAO,QAAQ,KAAK,WAAW,EAAE,UAAU,QAAO,CAAE;AAGpD,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,UAAI,UAAU;AACZ,YAAIH,UAAS;AACX,mBAAS,eAAe,WAAWA,QAAO;AAC1C,UAAAA,WAAU;QACZ;AACA,YAAI,CAAC,iBAAiB;AACpB,4BAAkB,KAAK,kBAAkB,KAAK,IAAI;AAClD,mBAAS,GAAG,mBAAmB,eAAe;QAChD;AACA,YAAI,CAAC,cAAc;AACjB,yBAAe,KAAK,eAAe,KAAK,IAAI;AAC5C,mBAAS,GAAG,gBAAgB,YAAY;QAC1C;AACA,YAAI,CAACC,aAAY;AACf,UAAAA,cAAa,KAAK,aAAa,KAAK,IAAI;AACxC,mBAAS,GAAG,cAAcA,WAAU;QACtC;MACF;IACF;IACA,MAAM,aAAa,OAAK;AACtB,YAAM,WAAW,MAAM,KAAK,YAAW;AAIvC,UAAI,SAAU,MAAyB,SAAS,MAAM;AACpD,YAAI,YAAY,CAAC,EAAE,MAAM,KAAK,YAAW,GAAI;AAAQ;MACvD;AAKA,aAAO,QAAQ,KAAK,YAAY;AAGhC,UAAI,UAAU;AACZ,YAAI,cAAc;AAChB,mBAAS,eAAe,gBAAgB,YAAY;AACpD,yBAAe;QACjB;AACA,YAAIA,aAAY;AACd,mBAAS,eAAe,cAAcA,WAAU;AAChD,UAAAA,cAAa;QACf;AACA,YAAI,CAACD,UAAS;AACZ,UAAAA,WAAU,KAAK,UAAU,KAAK,IAAI;AAClC,mBAAS,GAAG,WAAWA,QAAO;QAChC;MACF;IACF;IACA;AACJ;AAEA,IAAM,YAAY;EAChB,gBAAgB;IACd,IAAI;IACJ,MAAM;IACN,SAASD,SAAM;AACb,UAAIA,WAAA,gBAAAA,QAAQ;AAAyB,eAAOA,QAAO;AACnD,aAAO,aAAaA,SAAQ,kBAAkB;IAChD;;EAEF,UAAU;IACR,IAAI;IACJ,MAAM;IACN,SAASA,SAAM;AACb,aAAO,aAAaA,SAAQ,CAAC,aAAY;AACvC,YAAI,CAAC,SAAS;AAAY,iBAAO;AAGjC,YAAI,SAAS,iBAAiB,CAAC,SAAS,WAAW,CAAC,SAAS;AAC3D,iBAAO;AAET,cAAM,QAAQ;UACZ;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;AAEF,mBAAW,QAAQ;AAAO,cAAI,SAAS,IAAI;AAAG,mBAAO;AACrD,eAAO;MACT,CAAC;IACH;;EAEF,SAAS;IACP,IAAI;IACJ,MAAM;IACN,SAASA,SAAM;AA1kBnB;AA2kBM,WAAI,KAAAA,WAAA,gBAAAA,QAAQ,YAAR,mBAAiB;AAAU,gBAAO,KAAAA,QAAO,YAAP,mBAAgB;AACtD,aAAO,aAAaA,SAAQ,WAAW;IACzC;;;AA4FJ,SAAS,aACPA,SACA,QAAsE;AAEtE,WAAS,WAAW,UAAwB;AAC1C,QAAI,OAAO,WAAW;AAAY,aAAO,OAAO,QAAQ;AACxD,QAAI,OAAO,WAAW;AAAU,aAAO,SAAS,MAAM;AACtD,WAAO;EACT;AAEA,QAAM,WAAYA,QAAkB;AACpC,MAAI,qCAAU;AACZ,WAAO,SAAS,UAAU,KAAK,CAAC,aAAa,WAAW,QAAQ,CAAC;AACnE,MAAI,YAAY,WAAW,QAAQ;AAAG,WAAO;AAC7C,SAAO;AACT;;;AC/oBA,KAAK,OAAO;AACN,SAAU,KAAK,YAA0B;AAC7C,QAAM,mBAAmB,oBAAI,IAAG;AAChC,QAAM,WACJ,WAAW,YACV,EAAE,kBAAkB,MAAK;AAe5B,MAAI,YAAY,SAAS;AACzB,MAAI;AAEJ,SAAO,gBAAsC,CAAC,YAAY;IACxD,IAAI;IACJ,MAAM;IACN,MAAM,KAAK;IACX,MAAM,QAAK;AACT,yBAAmB,OAAO,OAAO,CAAC,EAAE;IACtC;IACA,MAAM,QAAQ,EAAE,QAAO,IAAK,CAAA,GAAE;AAC5B,UAAI,SAAS,cAAc;AACzB,YAAI,OAAO,SAAS,iBAAiB;AACnC,gBAAM,IAAI,yBAAyB,IAAI,MAAM,oBAAoB,CAAC;AACpE,cAAM,SAAS;MACjB;AAEA,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,WAAW,MAAM,SAAS,QAAQ;QACtC,QAAQ;OACT;AAED,UAAI,iBAAiB,MAAM,KAAK,WAAU;AAC1C,UAAI,WAAW,mBAAmB,SAAS;AACzC,cAAM,QAAQ,MAAM,KAAK,YAAa,EAAE,QAAO,CAAE;AACjD,yBAAiB,MAAM;MACzB;AAEA,kBAAY;AAEZ,aAAO;QACL,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;QAC3C,SAAS;;IAEb;IACA,MAAM,aAAU;AACd,kBAAY;IACd;IACA,MAAM,cAAW;AACf,UAAI,CAAC;AAAW,cAAM,IAAI,2BAA0B;AACpD,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,WAAW,MAAM,SAAS,QAAQ,EAAE,QAAQ,eAAc,CAAE;AAClE,aAAO,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;IAC1C;IACA,MAAM,aAAU;AACd,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,aAAa,MAAM,SAAS,QAAQ,EAAE,QAAQ,cAAa,CAAE;AACnE,aAAO,QAAQ,YAAY,QAAQ;IACrC;IACA,MAAM,eAAY;AAChB,UAAI,CAAC,SAAS;AAAW,eAAO;AAChC,UAAI,CAAC;AAAW,eAAO;AACvB,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,aAAO,CAAC,CAAC,SAAS;IACpB;IACA,MAAM,YAAY,EAAE,QAAO,GAAE;AAC3B,YAAM,WAAW,MAAM,KAAK,YAAW;AACvC,YAAM,QAAQ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AACxD,UAAI,CAAC;AAAO,cAAM,IAAI,iBAAiB,IAAI,wBAAuB,CAAE;AAEpE,YAAM,SAAS,QAAQ;QACrB,QAAQ;QACR,QAAQ,CAAC,EAAE,SAAS,YAAY,OAAO,EAAC,CAAE;OAC3C;AACD,aAAO;IACT;IACA,kBAAkB,UAAQ;AACxB,UAAI,SAAS,WAAW;AAAG,aAAK,aAAY;;AAE1C,eAAO,QAAQ,KAAK,UAAU;UAC5B,UAAU,SAAS,IAAI,CAAC,MAAM,WAAW,CAAC,CAAC;SAC5C;IACL;IACA,eAAe,OAAK;AAClB,YAAM,UAAU,OAAO,KAAK;AAC5B,aAAO,QAAQ,KAAK,UAAU,EAAE,QAAO,CAAE;IAC3C;IACA,MAAM,aAAa,QAAM;AACvB,aAAO,QAAQ,KAAK,YAAY;AAChC,kBAAY;IACd;IACA,MAAM,YAAY,EAAE,QAAO,IAAK,CAAA,GAAE;AAChC,YAAM,QACJ,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO,KAAK,OAAO,OAAO,CAAC;AAChE,YAAM,MAAM,MAAM,QAAQ,QAAQ,KAAK,CAAC;AAExC,YAAM,UAA4B,OAAO,EAAE,QAAQ,OAAM,MAAM;AAE7D,YAAI,WAAW;AAAe,iBAAO,YAAY,gBAAgB;AACjE,YAAI,WAAW;AAAuB,iBAAO,WAAW;AACxD,YAAI,WAAW;AACb,cAAI,SAAS,oBAAoB;AAC/B,gBAAI,OAAO,SAAS,uBAAuB;AACzC,oBAAM,IAAI,yBACR,IAAI,MAAM,4BAA4B,CAAC;AAE3C,kBAAM,SAAS;UACjB;;AAGF,YAAI,WAAW,8BAA8B;AAC3C,cAAI,SAAS,kBAAkB;AAC7B,gBAAI,OAAO,SAAS,qBAAqB;AACvC,oBAAM,IAAI,yBACR,IAAI,MAAM,yBAAyB,CAAC;AAExC,kBAAM,SAAS;UACjB;AAEA,6BAAmB,QAAS,OAAkB,CAAC,EAAE,SAAS,QAAQ;AAClE,eAAK,eAAe,iBAAiB,SAAQ,CAAE;AAC/C;QACF;AAEA,YAAI,WAAW,qBAAqB;AAClC,cAAI,SAAS,iBAAiB;AAC5B,gBAAI,OAAO,SAAS,oBAAoB;AACtC,oBAAM,IAAI,yBACR,IAAI,MAAM,yBAAyB,CAAC;AAExC,kBAAM,SAAS;UACjB;AACA,iBAAO;QACT;AAEA,YAAI,WAAW;AACb,iBAAO;YACL,UAAU;cACR,kBAAkB;gBAChB,WACG,OAAiB,CAAC,MACnB;;cAEJ,aAAa;gBACX,WAAW;;;YAGf,WAAW;cACT,kBAAkB;gBAChB,WACG,OAAiB,CAAC,MACnB;;;;AAKV,YAAI,WAAW,oBAAoB;AACjC,gBAAM,SAAS,CAAA;AACf,gBAAM,QAAS,OAAe,CAAC,EAAE;AACjC,qBAAWK,SAAQ,OAAO;AACxB,kBAAM,EAAE,QAAAC,SAAQ,OAAAC,OAAK,IAAK,MAAM,IAAI,KAAK,KAAK;cAC5C,MAAM;gBACJ,QAAQ;gBACR,QAAQ,CAACF,KAAI;;aAEhB;AACD,gBAAIE;AACF,oBAAM,IAAI,gBAAgB;gBACxB,MAAM,EAAE,QAAQ,OAAM;gBACtB,OAAAA;gBACA;eACD;AACH,mBAAO,KAAKD,OAAM;UACpB;AACA,gBAAM,KAAK,UAAU,YAAY,KAAK,UAAU,KAAK,CAAC,CAAC;AACvD,2BAAiB,IAAI,IAAI,MAAM;AAC/B,iBAAO,EAAE,GAAE;QACb;AAEA,YAAI,WAAW,yBAAyB;AACtC,gBAAM,SAAS,iBAAiB,IAAK,OAAe,CAAC,CAAC;AACtD,cAAI,CAAC;AACH,mBAAO;cACL,QAAQ;cACR,SAAS;cACT,IAAK,OAAe,CAAC;cACrB,QAAQ;cACR,UAAU,CAAA;cACV,SAAS;;AAGb,gBAAM,WAAW,MAAM,QAAQ,IAC7B,OAAO,IAAI,OAAO,SAAQ;AACxB,kBAAM,EAAE,QAAAA,SAAQ,OAAAC,OAAK,IAAK,MAAM,IAAI,KAAK,KAAK;cAC5C,MAAM;gBACJ,QAAQ;gBACR,QAAQ,CAAC,IAAI;gBACb,IAAI;;aAEP;AACD,gBAAIA;AACF,oBAAM,IAAI,gBAAgB;gBACxB,MAAM,EAAE,QAAQ,OAAM;gBACtB,OAAAA;gBACA;eACD;AACH,gBAAI,CAACD;AAAQ,qBAAO;AACpB,mBAAO;cACL,WAAWA,QAAO;cAClB,aAAaA,QAAO;cACpB,SAASA,QAAO;cAChB,MAAMA,QAAO;cACb,QAAQA,QAAO;cACf,iBAAiBA,QAAO;;UAE5B,CAAC,CAAC;AAEJ,gBAAM,YAAY,SAAS,OAAO,CAAC,MAAM,MAAM,IAAI;AACnD,cAAI,UAAU,WAAW;AACvB,mBAAO;cACL,QAAQ;cACR,SAAS;cACT,IAAK,OAAe,CAAC;cACrB,QAAQ;cACR,UAAU,CAAA;cACV,SAAS;;AAEb,iBAAO;YACL,QAAQ;YACR,SAAS;YACT,IAAK,OAAe,CAAC;YACrB,QAAQ;YACR,UAAU;YACV,SAAS;;QAEb;AAEA,YAAI,WAAW;AAA0B;AAGzC,YAAI,WAAW,iBAAiB;AAC9B,cAAI,SAAS,kBAAkB;AAC7B,gBAAI,OAAO,SAAS,qBAAqB;AACvC,oBAAM,IAAI,yBACR,IAAI,MAAM,yBAAyB,CAAC;AAExC,kBAAM,SAAS;UACjB;AAEA,mBAAS;AAET,mBAAS,CAAE,OAAkB,CAAC,GAAI,OAAkB,CAAC,CAAC;QACxD;AAEA,cAAM,OAAO,EAAE,QAAQ,OAAM;AAC7B,cAAM,EAAE,OAAO,OAAM,IAAK,MAAM,IAAI,KAAK,KAAK,EAAE,KAAI,CAAE;AACtD,YAAI;AAAO,gBAAM,IAAI,gBAAgB,EAAE,MAAM,OAAO,IAAG,CAAE;AAEzD,eAAO;MACT;AACA,aAAO,OAAO,EAAE,QAAO,CAAE,EAAE,EAAE,YAAY,EAAC,CAAE;IAC9C;IACA;AACJ;;;AC9QM,SAAU,iBACd,UAAoC;AAEpC,MAAI,OAAO,WAAW;AAAa;AACnC,QAAM,UAAU,CAAC,UACf,SAAS,MAAM,MAAM;AAEvB,SAAO,iBAAiB,4BAA4B,OAAO;AAE3D,SAAO,cAAc,IAAI,YAAY,yBAAyB,CAAC;AAE/D,SAAO,MAAM,OAAO,oBAAoB,4BAA4B,OAAO;AAC7E;;;ACNM,SAAU,cAAW;AACzB,QAAM,YAA2B,oBAAI,IAAG;AACxC,MAAI,kBAAoD,CAAA;AAExD,QAAM,UAAU,MACd,iBAAiB,CAAC,mBAAkB;AAClC,QACE,gBAAgB,KACd,CAAC,EAAE,KAAI,MAAO,KAAK,SAAS,eAAe,KAAK,IAAI;AAGtD;AAEF,sBAAkB,CAAC,GAAG,iBAAiB,cAAc;AACrD,cAAU,QAAQ,CAAC,aACjB,SAAS,iBAAiB,EAAE,OAAO,CAAC,cAAc,EAAC,CAAE,CAAC;EAE1D,CAAC;AACH,MAAI,UAAU,QAAO;AAErB,SAAO;IACL,aAAU;AACR,aAAO;IACT;IACA,QAAK;AACH,gBAAU,QAAQ,CAAC,aACjB,SAAS,CAAA,GAAI,EAAE,SAAS,CAAC,GAAG,eAAe,EAAC,CAAE,CAAC;AAEjD,wBAAkB,CAAA;IACpB;IACA,UAAO;AACL,WAAK,MAAK;AACV,gBAAU,MAAK;AACf;IACF;IACA,aAAa,EAAE,KAAI,GAAE;AACnB,aAAO,gBAAgB,KACrB,CAAC,mBAAmB,eAAe,KAAK,SAAS,IAAI;IAEzD;IACA,eAAY;AACV,aAAO;IACT;IACA,QAAK;AACH,WAAK,MAAK;AACV;AACA,gBAAU,QAAO;IACnB;IACA,UAAU,UAAU,EAAE,gBAAe,IAAK,CAAA,GAAE;AAC1C,gBAAU,IAAI,QAAQ;AACtB,UAAI;AAAiB,iBAAS,iBAAiB,EAAE,OAAO,gBAAe,CAAE;AACzE,aAAO,MAAM,UAAU,OAAO,QAAQ;IACxC;;AAEJ;;;AC+GA,IAAM,4BAA4B,CAAC,OAAO,CAAC,KAAK,KAAK,QAAQ;AAC3D,QAAM,gBAAgB,IAAI;AAC1B,MAAI,YAAY,CAAC,UAAU,aAAa,YAAY;AAClD,QAAI,WAAW;AACf,QAAI,aAAa;AACf,YAAM,cAAc,WAAW,OAAO,SAAS,QAAQ,eAAe,OAAO;AAC7E,UAAI,eAAe,SAAS,IAAI,SAAS,CAAC;AAC1C,iBAAW,CAAC,UAAU;AACpB,cAAM,YAAY,SAAS,KAAK;AAChC,YAAI,CAAC,WAAW,cAAc,SAAS,GAAG;AACxC,gBAAM,gBAAgB;AACtB,sBAAY,eAAe,WAAW,aAAa;AAAA,QACrD;AAAA,MACF;AACA,UAAI,WAAW,OAAO,SAAS,QAAQ,iBAAiB;AACtD,oBAAY,cAAc,YAAY;AAAA,MACxC;AAAA,IACF;AACA,WAAO,cAAc,QAAQ;AAAA,EAC/B;AACA,QAAM,eAAe,GAAG,KAAK,KAAK,GAAG;AACrC,SAAO;AACT;AACA,IAAM,wBAAwB;AAI9B,SAAS,kBAAkB,YAAY,SAAS;AAC9C,MAAI;AACJ,MAAI;AACF,cAAU,WAAW;AAAA,EACvB,SAAS,GAAG;AACV;AAAA,EACF;AACA,QAAM,iBAAiB;AAAA,IACrB,SAAS,CAAC,SAAS;AACjB,UAAI;AACJ,YAAM,QAAQ,CAAC,SAAS;AACtB,YAAI,SAAS,MAAM;AACjB,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,MAAM,MAAM,WAAW,OAAO,SAAS,QAAQ,OAAO;AAAA,MACpE;AACA,YAAM,OAAO,KAAK,QAAQ,QAAQ,IAAI,MAAM,OAAO,KAAK;AACxD,UAAI,eAAe,SAAS;AAC1B,eAAO,IAAI,KAAK,KAAK;AAAA,MACvB;AACA,aAAO,MAAM,GAAG;AAAA,IAClB;AAAA,IACA,SAAS,CAAC,MAAM,aAAa,QAAQ;AAAA,MACnC;AAAA,MACA,KAAK,UAAU,UAAU,WAAW,OAAO,SAAS,QAAQ,QAAQ;AAAA,IACtE;AAAA,IACA,YAAY,CAAC,SAAS,QAAQ,WAAW,IAAI;AAAA,EAC/C;AACA,SAAO;AACT;AACA,IAAM,aAAa,CAAC,OAAO,CAAC,UAAU;AACpC,MAAI;AACF,UAAM,SAAS,GAAG,KAAK;AACvB,QAAI,kBAAkB,SAAS;AAC7B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MACL,KAAK,aAAa;AAChB,eAAO,WAAW,WAAW,EAAE,MAAM;AAAA,MACvC;AAAA,MACA,MAAM,aAAa;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AACV,WAAO;AAAA,MACL,KAAK,cAAc;AACjB,eAAO;AAAA,MACT;AAAA,MACA,MAAM,YAAY;AAChB,eAAO,WAAW,UAAU,EAAE,CAAC;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,cAAc,CAAC,QAAQ,gBAAgB,CAAC,KAAK,KAAK,QAAQ;AAC9D,MAAI,UAAU;AAAA,IACZ,SAAS,kBAAkB,MAAM,YAAY;AAAA,IAC7C,YAAY,CAAC,UAAU;AAAA,IACvB,SAAS;AAAA,IACT,OAAO,CAAC,gBAAgB,kBAAkB;AAAA,MACxC,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,IACA,GAAG;AAAA,EACL;AACA,MAAI,cAAc;AAClB,QAAM,qBAAqC,oBAAI,IAAI;AACnD,QAAM,2BAA2C,oBAAI,IAAI;AACzD,MAAI,UAAU,QAAQ;AACtB,MAAI,CAAC,SAAS;AACZ,WAAO;AAAA,MACL,IAAI,SAAS;AACX,gBAAQ;AAAA,UACN,uDAAuD,QAAQ,IAAI;AAAA,QACrE;AACA,YAAI,GAAG,IAAI;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,UAAU,MAAM;AACpB,UAAM,QAAQ,QAAQ,WAAW,EAAE,GAAG,IAAI,EAAE,CAAC;AAC7C,WAAO,QAAQ,QAAQ,QAAQ,MAAM;AAAA,MACnC;AAAA,MACA,SAAS,QAAQ;AAAA,IACnB,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,IAAI;AAC1B,MAAI,WAAW,CAAC,OAAO,YAAY;AACjC,kBAAc,OAAO,OAAO;AAC5B,SAAK,QAAQ;AAAA,EACf;AACA,QAAM,eAAe;AAAA,IACnB,IAAI,SAAS;AACX,UAAI,GAAG,IAAI;AACX,WAAK,QAAQ;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,MAAI,kBAAkB,MAAM;AAC5B,MAAI;AACJ,QAAME,WAAU,MAAM;AACpB,QAAI,IAAI;AACR,QAAI,CAAC,QAAS;AACd,kBAAc;AACd,uBAAmB,QAAQ,CAAC,OAAO;AACjC,UAAI;AACJ,aAAO,IAAI,MAAM,IAAI,MAAM,OAAO,MAAM,YAAY;AAAA,IACtD,CAAC;AACD,UAAM,4BAA4B,KAAK,QAAQ,uBAAuB,OAAO,SAAS,GAAG,KAAK,UAAU,KAAK,IAAI,MAAM,OAAO,KAAK,YAAY,MAAM;AACrJ,WAAO,WAAW,QAAQ,QAAQ,KAAK,OAAO,CAAC,EAAE,QAAQ,IAAI,EAAE,KAAK,CAAC,6BAA6B;AAChG,UAAI,0BAA0B;AAC5B,YAAI,OAAO,yBAAyB,YAAY,YAAY,yBAAyB,YAAY,QAAQ,SAAS;AAChH,cAAI,QAAQ,SAAS;AACnB,mBAAO;AAAA,cACL;AAAA,cACA,QAAQ;AAAA,gBACN,yBAAyB;AAAA,gBACzB,yBAAyB;AAAA,cAC3B;AAAA,YACF;AAAA,UACF;AACA,kBAAQ;AAAA,YACN;AAAA,UACF;AAAA,QACF,OAAO;AACL,iBAAO,CAAC,OAAO,yBAAyB,KAAK;AAAA,QAC/C;AAAA,MACF;AACA,aAAO,CAAC,OAAO,MAAM;AAAA,IACvB,CAAC,EAAE,KAAK,CAAC,oBAAoB;AAC3B,UAAI;AACJ,YAAM,CAAC,UAAU,aAAa,IAAI;AAClC,yBAAmB,QAAQ;AAAA,QACzB;AAAA,SACC,MAAM,IAAI,MAAM,OAAO,MAAM;AAAA,MAChC;AACA,UAAI,kBAAkB,IAAI;AAC1B,UAAI,UAAU;AACZ,eAAO,QAAQ;AAAA,MACjB;AAAA,IACF,CAAC,EAAE,KAAK,MAAM;AACZ,iCAA2B,OAAO,SAAS,wBAAwB,kBAAkB,MAAM;AAC3F,yBAAmB,IAAI;AACvB,oBAAc;AACd,+BAAyB,QAAQ,CAAC,OAAO,GAAG,gBAAgB,CAAC;AAAA,IAC/D,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,iCAA2B,OAAO,SAAS,wBAAwB,QAAQ,CAAC;AAAA,IAC9E,CAAC;AAAA,EACH;AACA,MAAI,UAAU;AAAA,IACZ,YAAY,CAAC,eAAe;AAC1B,gBAAU;AAAA,QACR,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AACA,UAAI,WAAW,SAAS;AACtB,kBAAU,WAAW;AAAA,MACvB;AAAA,IACF;AAAA,IACA,cAAc,MAAM;AAClB,iBAAW,OAAO,SAAS,QAAQ,WAAW,QAAQ,IAAI;AAAA,IAC5D;AAAA,IACA,YAAY,MAAM;AAAA,IAClB,WAAW,MAAMA,SAAQ;AAAA,IACzB,aAAa,MAAM;AAAA,IACnB,WAAW,CAAC,OAAO;AACjB,yBAAmB,IAAI,EAAE;AACzB,aAAO,MAAM;AACX,2BAAmB,OAAO,EAAE;AAAA,MAC9B;AAAA,IACF;AAAA,IACA,mBAAmB,CAAC,OAAO;AACzB,+BAAyB,IAAI,EAAE;AAC/B,aAAO,MAAM;AACX,iCAAyB,OAAO,EAAE;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,eAAe;AAC1B,IAAAA,SAAQ;AAAA,EACV;AACA,SAAO,oBAAoB;AAC7B;AACA,IAAM,UAAU;;;AC7ahB,IAAM,kBAAkB,CAAC,gBAAgB;AACvC,MAAI;AACJ,QAAM,YAA4B,oBAAI,IAAI;AAC1C,QAAM,WAAW,CAAC,SAAS,YAAY;AACrC,UAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACnE,QAAI,CAAC,OAAO,GAAG,WAAW,KAAK,GAAG;AAChC,YAAM,gBAAgB;AACtB,eAAS,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,QAAQ,YAAY,OAAO,OAAO,CAAC,GAAG,OAAO,SAAS;AAC1I,gBAAU,QAAQ,CAAC,aAAa,SAAS,OAAO,aAAa,CAAC;AAAA,IAChE;AAAA,EACF;AACA,QAAM,WAAW,MAAM;AACvB,QAAM,kBAAkB,MAAM;AAC9B,QAAM,YAAY,CAAC,aAAa;AAC9B,cAAU,IAAI,QAAQ;AACtB,WAAO,MAAM,UAAU,OAAO,QAAQ;AAAA,EACxC;AACA,QAAM,MAAM,EAAE,UAAU,UAAU,iBAAiB,UAAU;AAC7D,QAAM,eAAe,QAAQ,YAAY,UAAU,UAAU,GAAG;AAChE,SAAO;AACT;AACA,IAAMC,eAAc,CAAC,gBAAgB,cAAc,gBAAgB,WAAW,IAAI;;;ACP5E,IAAO,UAAP,MAAc;EAGlB,YAAmBC,MAAW;AAAlB,WAAA,eAAA,MAAA,OAAA;;;;aAAOA;;AAFnB,WAAA,eAAA,MAAA,YAAA;;;;aAAW,IAAI,aAAAC,QAAY;;EAEM;EAEjC,GACE,WACA,IAIC;AAED,SAAK,SAAS,GAAG,WAAW,EAAa;EAC3C;EAEA,KACE,WACA,IAIC;AAED,SAAK,SAAS,KAAK,WAAW,EAAa;EAC7C;EAEA,IACE,WACA,IAIC;AAED,SAAK,SAAS,IAAI,WAAW,EAAa;EAC5C;EAEA,KACE,cACG,QAAkE;AAErE,UAAM,OAAO,OAAO,CAAC;AACrB,SAAK,SAAS,KAAK,WAAW,EAAE,KAAK,KAAK,KAAK,GAAG,KAAI,CAAE;EAC1D;EAEA,cAA8C,WAAc;AAC1D,WAAO,KAAK,SAAS,cAAc,SAAS;EAC9C;;AAGI,SAAU,cAAyCD,MAAW;AAClE,SAAO,IAAI,QAAkBA,IAAG;AAClC;;;ACjEM,SAAU,YAAkB,OAAe,SAAiB;AAChE,SAAO,KAAK,MAAM,OAAO,CAAC,KAAK,WAAU;AACvC,QAAIE,SAAQ;AACZ,SAAIA,UAAA,gBAAAA,OAAO,YAAW;AAAU,MAAAA,SAAQ,OAAOA,OAAM,KAAK;AAC1D,SAAIA,UAAA,gBAAAA,OAAO,YAAW;AAAO,MAAAA,SAAQ,IAAI,IAAIA,OAAM,KAAK;AACxD,YAAO,mCAAU,KAAKA,YAAUA;EAClC,CAAC;AACH;;;ACFA,SAAS,gBAAgB,MAAgB,QAAc;AACrD,SAAO,KAAK,MAAM,GAAG,MAAM,EAAE,KAAK,GAAG,KAAK;AAC5C;AASA,SAAS,UAAU,OAAc,OAAU;AACzC,QAAM,EAAE,OAAM,IAAK;AAEnB,WAASC,SAAQ,GAAGA,SAAQ,QAAQ,EAAEA,QAAO;AAC3C,QAAI,MAAMA,MAAK,MAAM,OAAO;AAC1B,aAAOA,SAAQ;IACjB;EACF;AAEA,SAAO;AACT;AAYA,SAAS,eACP,UACA,kBAAsD;AAEtD,QAAM,cAAc,OAAO,aAAa;AACxC,QAAM,sBAAsB,OAAO,qBAAqB;AAExD,QAAM,QAAe,CAAA;AACrB,QAAM,OAAiB,CAAA;AAEvB,SAAO,SAAS,QAAmB,KAAa,OAAU;AACxD,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,MAAM,QAAQ;AAChB,cAAM,aAAa,UAAU,OAAO,IAAI;AAExC,YAAI,eAAe,GAAG;AACpB,gBAAM,MAAM,MAAM,IAAI;QACxB,OAAO;AACL,gBAAM,OAAO,UAAU;AACvB,eAAK,OAAO,UAAU;QACxB;AAEA,aAAK,KAAK,MAAM,IAAI;AAEpB,cAAM,cAAc,UAAU,OAAO,KAAK;AAE1C,YAAI,gBAAgB,GAAG;AACrB,iBAAO,sBACH,iBAAiB,KACf,MACA,KACA,OACA,gBAAgB,MAAM,WAAW,CAAC,IAEpC,QAAQ,gBAAgB,MAAM,WAAW,CAAC;QAChD;MACF,OAAO;AACL,cAAM,CAAC,IAAI;AACX,aAAK,CAAC,IAAI;MACZ;IACF;AAEA,WAAO,cAAc,SAAS,KAAK,MAAM,KAAK,KAAK,IAAI;EACzD;AACF;AAaM,SAAU,UACd,OACA,UACA,QACA,kBAAsD;AAEtD,SAAO,KAAK,UACV,OACA,eAAe,CAAC,KAAK,WAAU;AAC7B,QAAIC,SAAQ;AACZ,QAAI,OAAOA,WAAU;AACnB,MAAAA,SAAQ,EAAE,QAAQ,UAAU,OAAO,OAAO,SAAQ,EAAE;AACtD,QAAIA,kBAAiB;AACnB,MAAAA,SAAQ,EAAE,QAAQ,OAAO,OAAO,MAAM,KAAK,OAAO,QAAO,CAAE,EAAC;AAC9D,YAAO,qCAAW,KAAKA,YAAUA;EACnC,GAAG,gBAAgB,GACnB,UAAU,MAAS;AAEvB;;;AClEM,SAAU,cAGd,YAAmC;AACnC,QAAM,EACJ,aAAAC,eAAc,aACd,KAAK,SAAS,SACd,WAAAC,aAAY,WACZ,UAAU,YAAW,IACnB;AAEJ,WAAS,OAAa,OAAW;AAC/B,QAAI,iBAAiB;AAAS,aAAO,MAAM,KAAK,CAAC,MAAM,CAAC,EAAE,MAAM,MAAM,IAAI;AAC1E,WAAO;EACT;AAEA,SAAO;IACL,GAAG;IACH,KAAK;IACL,MAAM,QAAQ,KAAK,cAAY;AAC7B,YAAM,QAAQ,QAAQ,QAAQ,GAAG,MAAM,IAAI,GAAa,EAAE;AAC1D,YAAM,YAAY,MAAM,OAAO,KAAK;AACpC,UAAI;AAAW,eAAOD,aAAY,SAAS,KAAK;AAChD,aAAQ,gBAAgB;IAC1B;IACA,MAAM,QAAQ,KAAK,OAAK;AACtB,YAAM,aAAa,GAAG,MAAM,IAAI,GAAa;AAC7C,UAAI,UAAU;AAAM,cAAM,OAAO,QAAQ,WAAW,UAAU,CAAC;;AAC1D,cAAM,OAAO,QAAQ,QAAQ,YAAYC,WAAU,KAAK,CAAC,CAAC;IACjE;IACA,MAAM,WAAW,KAAG;AAClB,YAAM,OAAO,QAAQ,WAAW,GAAG,MAAM,IAAI,GAAa,EAAE,CAAC;IAC/D;;AAEJ;AAEO,IAAM,cAAc;EACzB,SAAS,MAAM;EACf,SAAS,MAAK;EAAE;EAChB,YAAY,MAAK;EAAE;;AAGf,SAAU,oBAAiB;AAC/B,QAAM,WAAW,MAAK;AACpB,QAAI,OAAO,WAAW,eAAe,OAAO;AAC1C,aAAO,OAAO;AAChB,WAAO;EACT,GAAE;AACF,SAAO;IACL,QAAQ,KAAG;AACT,aAAO,QAAQ,QAAQ,GAAG;IAC5B;IACA,WAAW,KAAG;AACZ,cAAQ,WAAW,GAAG;IACxB;IACA,QAAQ,KAAK,OAAK;AAChB,UAAI;AACF,gBAAQ,QAAQ,KAAK,KAAK;MAE5B,QAAQ;MAAC;IACX;;AAEJ;;;AC/GA,IAAM,OAAO;AACb,IAAI,QAAQ;AACZ,IAAI;AAEE,SAAU,IAAI,SAAS,IAAE;AAC7B,MAAI,CAAC,UAAU,QAAQ,SAAS,OAAO,GAAG;AACxC,aAAS;AACT,YAAQ;AACR,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,iBAAY,MAAM,KAAK,OAAM,IAAK,MAAO,GAAG,SAAS,EAAE,EAAE,UAAU,CAAC;IACtE;EACF;AACA,SAAO,OAAO,UAAU,OAAO,UAAU,MAAM;AACjD;;;AC0BM,SAAU,aAKd,YAAoE;AAEpE,QAAM,EACJ,iCAAiC,MACjC,UAAU,cAAc;IACtB,SAAS,kBAAiB;GAC3B,GACD,qBAAqB,MACrB,MAAM,OACN,GAAG,KAAI,IACL;AAMJ,QAAM,OACJ,OAAO,WAAW,eAAe,iCAC7B,YAAU,IACV;AAEN,QAAM,SAASC,aAAY,MAAM,KAAK,MAAM;AAC5C,QAAM,aAAaA,aAAY,MAAK;AAClC,UAAM,aAAa,CAAA;AACnB,UAAM,UAAU,oBAAI,IAAG;AACvB,eAAW,gBAAgB,KAAK,cAAc,CAAA,GAAI;AAChD,YAAM,YAAY,MAAM,YAAY;AACpC,iBAAW,KAAK,SAAS;AACzB,UAAI,CAAC,OAAO,UAAU,MAAM;AAC1B,cAAM,aACJ,OAAO,UAAU,SAAS,WAAW,CAAC,UAAU,IAAI,IAAI,UAAU;AACpE,mBAAW,QAAQ,YAAY;AAC7B,kBAAQ,IAAI,IAAI;QAClB;MACF;IACF;AACA,QAAI,CAAC,OAAO,MAAM;AAChB,YAAM,YAAY,KAAK,aAAY;AACnC,iBAAW,YAAY,WAAW;AAChC,YAAI,QAAQ,IAAI,SAAS,KAAK,IAAI;AAAG;AACrC,mBAAW,KAAK,MAAM,0BAA0B,QAAQ,CAAC,CAAC;MAC5D;IACF;AACA,WAAO;EACT,CAAC;AACD,WAAS,MAAM,aAA8B;AAzF/C;AA2FI,UAAM,UAAU,cAAiC,IAAG,CAAE;AACtD,UAAM,YAAY;MAChB,GAAG,YAAY;QACb;QACA,QAAQ,OAAO,SAAQ;QACvB;QACA,YAAY,KAAK;OAClB;MACD;MACA,KAAK,QAAQ;;AAKf,YAAQ,GAAG,WAAWC,QAAO;AAC7B,oBAAU,UAAV;AAEA,WAAO;EACT;AACA,WAAS,0BAA0B,gBAAqC;AACtE,UAAM,EAAE,KAAI,IAAK;AACjB,UAAM,WAAW,eAAe;AAChC,WAAO,SAAS,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,KAAK,MAAM,SAAQ,EAAE,CAAE;EAClE;AAEA,QAAM,UAAU,oBAAI,IAAG;AACvB,WAASC,WACP,SAAmE,CAAA,GAAE;AAErE,UAAM,UAAU,OAAO,WAAW,MAAM,SAAQ,EAAG;AACnD,UAAM,QAAQ,OAAO,SAAQ,EAAG,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AAG5D,QAAI,OAAO,WAAW,CAAC;AAAO,YAAM,IAAI,wBAAuB;AAI/D;AACE,YAAMC,UAAS,QAAQ,IAAI,MAAM,SAAQ,EAAG,OAAO;AACnD,UAAIA,WAAU,CAAC;AAAO,eAAOA;AAC7B,UAAI,CAAC;AAAO,cAAM,IAAI,wBAAuB;IAC/C;AAGA;AACE,YAAMA,UAAS,QAAQ,IAAI,OAAO;AAClC,UAAIA;AAAQ,eAAOA;IACrB;AAEA,QAAI;AACJ,QAAI,KAAK;AAAQ,eAAS,KAAK,OAAO,EAAE,MAAK,CAAE;SAC1C;AACH,YAAMC,WAAU,MAAM;AACtB,YAAM,WAAW,OAAO,SAAQ,EAAG,IAAI,CAAC,MAAM,EAAE,EAAE;AAElD,YAAM,aAAyC,CAAA;AAC/C,YAAM,UAAU,OAAO,QAAQ,IAAI;AAEnC,iBAAW,CAAC,KAAK,KAAK,KAAK,SAAS;AAClC,YACE,QAAQ,YACR,QAAQ,YACR,QAAQ,gBACR,QAAQ;AAER;AAEF,YAAI,OAAO,UAAU,UAAU;AAG7B,cAAIA,YAAW;AAAO,uBAAW,GAAG,IAAI,MAAMA,QAAO;eAChD;AAEH,kBAAM,wBAAwB,SAAS,KAAK,CAAC,MAAM,KAAK,KAAK;AAC7D,gBAAI;AAAuB;AAC3B,uBAAW,GAAG,IAAI;UACpB;QACF;AAAO,qBAAW,GAAG,IAAI;MAC3B;AAEA,eAAS,aAAa;QACpB,GAAG;QACH;QACA,OAAO,WAAW,SAAS,EAAE,WAAW,KAAI;QAC5C,WAAW,CAACC,gBACV,KAAK,WAAWD,QAAO,EAAE,EAAE,GAAGC,aAAY,WAAU,CAAE;OACzD;IACH;AAEA,YAAQ,IAAI,SAAS,MAAM;AAC3B,WAAO;EACT;AAMA,WAAS,kBAAe;AACtB,WAAO;MACL,SAAS,OAAO,SAAQ,EAAG,CAAC,EAAE;MAC9B,aAAa,oBAAI,IAAG;MACpB,SAAS;MACT,QAAQ;;EAEZ;AAEA,MAAI;AACJ,QAAM,SAAS;AACf,MAAI,QAAQ,WAAW,MAAM;AAC3B,qBAAiB,OAAO,SAAS,QAAQ,QAAQ,QAAQ,EAAE,CAAC;;AAEzD,qBAAiB,OAAO,SAAS,QAAQ,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG;AAElE,QAAM,QAAQL,aACZ;;IAEE,UACI,QAAQ,iBAAiB;MACvB,QAAQ,gBAAgBM,UAAO;AAC7B,YAAIA,aAAY;AAAgB,iBAAO;AAEvC,cAAM,eAAe,gBAAe;AACpC,cAAM,UAAU,yBACd,gBACA,aAAa,OAAO;AAEtB,eAAO,EAAE,GAAG,cAAc,QAAO;MACnC;MACA,MAAM;MACN,WAAW,OAAK;AAEd,eAAO;UACL,aAAa;YACX,QAAQ;YACR,OAAO,MAAM,KAAK,MAAM,YAAY,QAAO,CAAE,EAAE,IAC7C,CAAC,CAAC,KAAK,UAAU,MAAK;AACpB,oBAAM,EAAE,IAAI,MAAM,MAAM,KAAAC,KAAG,IAAK,WAAW;AAC3C,oBAAM,YAAY,EAAE,IAAI,MAAM,MAAM,KAAAA,KAAG;AACvC,qBAAO,CAAC,KAAK,EAAE,GAAG,YAAY,UAAS,CAAE;YAC3C,CAAC;;UAGL,SAAS,MAAM;UACf,SAAS,MAAM;;MAEnB;MACA,MAAM,gBAAgB,cAAY;AAEhC,YACE,OAAO,mBAAmB,YAC1B,kBACA,YAAY;AAEZ,iBAAO,eAAe;AAExB,cAAM,UAAU,yBACd,gBACA,aAAa,OAAO;AAEtB,eAAO;UACL,GAAG;UACH,GAAI;UACJ;;MAEJ;MACA,eAAe;MACf;MACA,SAAS;KACV,IACD;EAAe,CACpB;AAEH,QAAM,SAAS,gBAAe,CAAE;AAEhC,WAAS,yBACP,gBACA,gBAAsB;AAEtB,WAAO,kBACL,OAAO,mBAAmB,YAC1B,aAAa,kBACb,OAAO,eAAe,YAAY,YAClC,OAAO,SAAQ,EAAG,KAAK,CAAC,MAAM,EAAE,OAAO,eAAe,OAAO,IAC3D,eAAe,UACf;EACN;AAOA,MAAI;AACF,UAAM,UACJ,CAAC,EAAE,aAAa,QAAO,MAAI;AA7RjC;AA8RQ,wBAAU,iBAAY,IAAI,OAAO,MAAvB,mBAA0B,UAAU;OAChD,CAAC,YAAW;AAEV,YAAM,oBAAoB,OACvB,SAAQ,EACR,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;AAC/B,UAAI,CAAC;AAAmB;AAExB,aAAO,MAAM,SAAS,CAAC,OAAO;QAC5B,GAAG;QACH,SAAS,WAAW,EAAE;QACtB;IACJ,CAAC;AAIL,+BAAM,UAAU,CAAC,oBAAmB;AAClC,UAAM,iBAAiB,oBAAI,IAAG;AAC9B,UAAM,mBAAmB,oBAAI,IAAG;AAChC,eAAW,aAAa,WAAW,SAAQ,GAAI;AAC7C,qBAAe,IAAI,UAAU,EAAE;AAC/B,UAAI,UAAU,MAAM;AAClB,cAAM,aACJ,OAAO,UAAU,SAAS,WAAW,CAAC,UAAU,IAAI,IAAI,UAAU;AACpE,mBAAW,QAAQ,YAAY;AAC7B,2BAAiB,IAAI,IAAI;QAC3B;MACF;IACF;AAEA,UAAM,gBAA6B,CAAA;AACnC,eAAW,kBAAkB,iBAAiB;AAC5C,UAAI,iBAAiB,IAAI,eAAe,KAAK,IAAI;AAAG;AACpD,YAAM,YAAY,MAAM,0BAA0B,cAAc,CAAC;AACjE,UAAI,eAAe,IAAI,UAAU,EAAE;AAAG;AACtC,oBAAc,KAAK,SAAS;IAC9B;AAEA,QAAI,WAAW,CAAC,MAAM,QAAQ,YAAW;AAAI;AAC7C,eAAW,SAAS,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,aAAa,GAAG,IAAI;EAC3D;AAMA,WAAS,OAAO,MAA4C;AAC1D,UAAM,SAAS,CAAC,MAAK;AACnB,YAAM,aAAa,EAAE,YAAY,IAAI,KAAK,GAAG;AAC7C,UAAI,CAAC;AAAY,eAAO;AACxB,aAAO;QACL,GAAG;QACH,aAAa,IAAI,IAAI,EAAE,WAAW,EAAE,IAAI,KAAK,KAAK;UAChD,UACG,KAAK,YACN,WAAW;UACb,SAAS,KAAK,WAAW,WAAW;UACpC,WAAW,WAAW;SACvB;;IAEL,CAAC;EACH;AACA,WAASN,SAAQ,MAA6C;AAE5D,QACE,MAAM,SAAQ,EAAG,WAAW,gBAC5B,MAAM,SAAQ,EAAG,WAAW;AAE5B;AAEF,UAAM,SAAS,CAAC,MAAK;AACnB,YAAM,YAAY,WAAW,SAAQ,EAAG,KAAK,CAACO,OAAMA,GAAE,QAAQ,KAAK,GAAG;AACtE,UAAI,CAAC;AAAW,eAAO;AAEvB,UAAI,UAAU,QAAQ,cAAc,SAAS;AAC3C,kBAAU,QAAQ,IAAI,WAAW,MAAM;AACzC,UAAI,CAAC,UAAU,QAAQ,cAAc,QAAQ;AAC3C,kBAAU,QAAQ,GAAG,UAAU,MAAM;AACvC,UAAI,CAAC,UAAU,QAAQ,cAAc,YAAY;AAC/C,kBAAU,QAAQ,GAAG,cAAcC,WAAU;AAE/C,aAAO;QACL,GAAG;QACH,aAAa,IAAI,IAAI,EAAE,WAAW,EAAE,IAAI,KAAK,KAAK;UAChD,UAAU,KAAK;UACf,SAAS,KAAK;UACd;SACD;QACD,SAAS,KAAK;QACd,QAAQ;;IAEZ,CAAC;EACH;AACA,WAASA,YAAW,MAAgD;AAClE,UAAM,SAAS,CAAC,MAAK;AACnB,YAAM,aAAa,EAAE,YAAY,IAAI,KAAK,GAAG;AAC7C,UAAI,YAAY;AACd,cAAM,YAAY,WAAW;AAC7B,YAAI,UAAU,QAAQ,cAAc,QAAQ;AAC1C,qBAAW,UAAU,QAAQ,IAAI,UAAU,MAAM;AACnD,YAAI,UAAU,QAAQ,cAAc,YAAY;AAC9C,qBAAW,UAAU,QAAQ,IAAI,cAAcA,WAAU;AAC3D,YAAI,CAAC,UAAU,QAAQ,cAAc,SAAS;AAC5C,qBAAW,UAAU,QAAQ,GAAG,WAAWR,QAAO;MACtD;AAEA,QAAE,YAAY,OAAO,KAAK,GAAG;AAE7B,UAAI,EAAE,YAAY,SAAS;AACzB,eAAO;UACL,GAAG;UACH,aAAa,oBAAI,IAAG;UACpB,SAAS;UACT,QAAQ;;AAGZ,YAAM,iBAAiB,EAAE,YAAY,OAAM,EAAG,KAAI,EAAG;AACrD,aAAO;QACL,GAAG;QACH,aAAa,IAAI,IAAI,EAAE,WAAW;QAClC,SAAS,eAAe,UAAU;;IAEtC,CAAC;EACH;AAEA,SAAO;IACL,IAAI,SAAM;AACR,aAAO,OAAO,SAAQ;IACxB;IACA,IAAI,aAAU;AACZ,aAAO,WAAW,SAAQ;IAC5B;IACA;IAEA,WAAAC;IACA,IAAI,QAAK;AACP,aAAO,MAAM,SAAQ;IACvB;IACA,SAAS,OAAK;AACZ,UAAI;AACJ,UAAI,OAAO,UAAU;AAAY,mBAAW,MAAM,MAAM,SAAQ,CAAS;;AACpE,mBAAW;AAGhB,YAAM,eAAe,gBAAe;AACpC,UAAI,OAAO,aAAa;AAAU,mBAAW;AAC7C,YAAM,YAAY,OAAO,KAAK,YAAY,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,SAAS;AACxE,UAAI;AAAW,mBAAW;AAE1B,YAAM,SAAS,UAAU,IAAI;IAC/B;IACA,UAAU,UAAU,UAAU,SAAO;AACnC,aAAO,MAAM,UACX,UACA,UACA,UACK;QACC,GAAG;QACH,iBAAiB,QAAQ;;UAG3B,MAAS;IAEjB;IAEA,WAAW;MACT;MACA;MACA,KAAK,QAAQ,GAAG;MAChB;MACA,YAAY,KAAK;MACjB,QAAQ;QACN,SAAS,OAAK;AACZ,gBAAM,aACJ,OAAO,UAAU,aAAa,MAAM,OAAO,SAAQ,CAAE,IAAI;AAE3D,cAAI,WAAW,WAAW;AAAG;AAC7B,iBAAO,OAAO,SAAS,YAAY,IAAI;QACzC;QACA,UAAU,UAAQ;AAChB,iBAAO,OAAO,UAAU,QAAQ;QAClC;;MAEF,YAAY;QACV;QACA;QAGA,SAAS,OAAK;AACZ,iBAAO,WAAW,SAChB,OAAO,UAAU,aAAa,MAAM,WAAW,SAAQ,CAAE,IAAI,OAC7D,IAAI;QAER;QACA,UAAU,UAAQ;AAChB,iBAAO,WAAW,UAAU,QAAQ;QACtC;;MAEF,QAAQ,EAAE,QAAQ,SAAAD,UAAS,YAAAQ,YAAU;;;AAG3C;;;AC/dM,SAAU,QAAQ,QAAgB,YAA6B;AACnE,QAAM,EAAE,cAAc,iBAAgB,IAAK;AAE3C,MAAI,gBAAgB,CAAC,OAAO,UAAU,MAAM,QAAQ,YAAW;AAC7D,WAAO,SAAS;MACd,GAAG;MACH,SAAS,OAAO,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO,aAAa,OAAO,IAC5D,aAAa,UACb,OAAO,OAAO,CAAC,EAAE;MACrB,aAAa,mBAAmB,aAAa,cAAc,oBAAI,IAAG;MAClE,QAAQ,mBAAmB,iBAAiB;KAC7C;AAEH,SAAO;IACL,MAAM,UAAO;AACX,UAAI,OAAO,UAAU,KAAK;AACxB,cAAM,OAAO,UAAU,MAAM,QAAQ,UAAS;AAC9C,YAAI,OAAO,UAAU,MAAM;AACzB,iBAAO,UAAU,WAAW,SAAS,CAAC,eAAc;AA1B9D;AA2BY,kBAAM,UAAU,oBAAI,IAAG;AACvB,uBAAW,aAAa,cAAc,CAAA,GAAI;AACxC,kBAAI,UAAU,MAAM;AAClB,sBAAM,aAAa,MAAM,QAAQ,UAAU,IAAI,IAC3C,UAAU,OACV,CAAC,UAAU,IAAI;AACnB,2BAAW,QAAQ,YAAY;AAC7B,0BAAQ,IAAI,IAAI;gBAClB;cACF;YACF;AACA,kBAAM,iBAAiB,CAAA;AACvB,kBAAM,cAAY,YAAO,UAAU,SAAjB,mBAAuB,mBAAkB,CAAA;AAC3D,uBAAW,YAAY,WAAW;AAChC,kBAAI,QAAQ,IAAI,SAAS,KAAK,IAAI;AAAG;AACrC,oBAAM,cACJ,OAAO,UAAU,WAAW,0BAA0B,QAAQ;AAChE,oBAAM,YAAY,OAAO,UAAU,WAAW,MAAM,WAAW;AAC/D,6BAAe,KAAK,SAAS;YAC/B;AACA,mBAAO,CAAC,GAAG,YAAY,GAAG,cAAc;UAC1C,CAAC;QACH;MACF;AAEA,UAAI;AAAkB,kBAAU,MAAM;eAC7B,OAAO;AAEd,eAAO,SAAS,CAAC,OAAO;UACtB,GAAG;UACH,aAAa,oBAAI,IAAG;UACpB;IACN;;AAEJ;;;AChCM,SAAU,mBACd,WACA,SAAmC,CAAA,GAAE;AAErC,QAAM,EAAE,KAAI,IAAK;AACjB,QAAM,EAAE,MAAM,aAAa,OAAO,aAAa,WAAU,IAAK;AAE9D,SAAO,CAAC,eAAc;AACpB,UAAM,EAAE,OAAO,WAAU,IAAK;AAC9B,UAAM,aAAa,OAAO,cAAc,WAAW;AAEnD,UAAM,UAA4B,OAAO,EAAE,QAAQ,OAAM,MAAM;AAC7D,YAAMC,aAAY,yCAAY,WAAW,KAAK,CAAC,MAAM,EAAE,SAAS;AAChE,UAAI,CAACA;AACH,cAAM,IAAI,0BACR,IAAI,MACF,qCAAqC,IAAI,iDAAiD,CAC3F;AAGL,YAAM,WAAY,MAAMA,WAAU,YAAY;QAC5C,SAAS,+BAAO;OACjB;AACD,UAAI,CAAC;AACH,cAAM,IAAI,0BACR,IAAI,MAAM,2BAA2B,CAAC;AAK1C,YAAM,UAAU,YACd,MAAM,UAAU,MACd,YAAY,MAAM,SAAS,QAAQ,EAAE,QAAQ,cAAa,CAAE,GAAG;QAC7D,SAAS;OACV,CAAC,CACH;AAEH,UAAI,SAAS,YAAY,MAAM;AAC7B,cAAM,IAAI,uBACR,IAAI,MACF,2CAA2C,OAAO,0DAA0D,MAAM,EAAE,MAAM,MAAM,IAAI,IAAI,CACzI;AAGL,YAAM,OAAO,EAAE,QAAQ,OAAM;AAC7B,aAAO,SAAS,QAAQ,IAAI;IAC9B;AAEA,WAAO,gBAAgB;MACrB;MACA;MACA;MACA;MACA;MACA,MAAM;KACP;EACH;AACF;;;AClFM,SAAUC,UACd,YACA,QAA4C;AAE5C,SAAO,SAAc,YAAY,MAAM;AACzC;;;ACLO,IAAM,gBAAgB;EAC3B,QAAQ,KAAG;AACT,QAAI,OAAO,WAAW;AAAa,aAAO;AAC1C,UAAM,QAAQ,YAAY,SAAS,QAAQ,GAAG;AAC9C,WAAO,SAAS;EAClB;EACA,QAAQ,KAAK,OAAK;AAChB,QAAI,OAAO,WAAW;AAAa;AACnC,aAAS,SAAS,GAAG,GAAG,IAAI,KAAK;EACnC;EACA,WAAW,KAAG;AACZ,QAAI,OAAO,WAAW;AAAa;AACnC,aAAS,SAAS,GAAG,GAAG;EAC1B;;AAGI,SAAU,qBAAqB,QAAgB,QAAsB;AAlB3E;AAmBE,MAAI,CAAC;AAAQ,WAAO;AACpB,QAAM,MAAM,IAAG,YAAO,YAAP,mBAAgB,GAAG;AAClC,QAAM,SAAS,YAAY,QAAQ,GAAG;AACtC,MAAI,CAAC;AAAQ,WAAO;AACpB,SAAO,YAA8B,MAAM,EAAE;AAC/C;AAEM,SAAU,YAAY,QAAgB,KAAW;AACrD,QAAM,WAAW,OAAO,MAAM,IAAI,EAAE,KAAK,CAAC,MAAM,EAAE,WAAW,GAAG,GAAG,GAAG,CAAC;AACvE,MAAI,CAAC;AAAU,WAAO;AACtB,SAAO,SAAS,UAAU,IAAI,SAAS,CAAC;AAC1C;;;ACzBM,SAAU,eAAe,YAAoC;AAAnE;AACE,QAAM,EAAE,MAAK,IAAK;AAClB,QAAM,cAAc,MAAM,QAAQ,QAAQ,KAAK,CAAC;AAEhD,MAAI,CAAC,WAAW;AAAY,WAAO,CAAC,WAAW;AAE/C,QAAM,aAAY,sBAAW,eAAX,mBAAwB,MAAM,QAA9B,4BAAoC,EAAE,MAAK;AAC7D,QAAM,eAAc,4CAAW,UAAX,mBAAkB,eAE9B,CAAC,SAAS;AAClB,SAAO,WAAW,IAAI,CAAC,EAAE,MAAK,OAAO,+BAAO,QAAO,WAAW;AAChE;;;ACjBM,SAAU,iBAAiB,SAA2C;AAC1E,MAAI,OAAO,YAAY;AACrB,WAAO,OAAO,SACZ,SACA,QAAQ,KAAI,EAAG,UAAU,GAAG,CAAC,MAAM,OAAO,KAAK,EAAE;AAErD,MAAI,OAAO,YAAY;AAAU,WAAO,OAAO,OAAO;AACtD,MAAI,OAAO,YAAY;AAAU,WAAO;AACxC,QAAM,IAAI,MACR,6BAA6B,OAAO,cAAc,OAAO,OAAO,GAAG;AAEvE;;;ACTA,mBAAqD;AAQ/C,SAAU,QAAQ,YAAiD;AACvE,QAAM,EAAE,UAAU,QAAQ,cAAc,mBAAmB,KAAI,IAAK;AAEpE,QAAM,EAAE,QAAO,IAAK,QAAQ,QAAQ;IAClC;IACA;GACD;AAGD,MAAI,CAAC,OAAO,UAAU;AAAK,YAAO;AAGlC,QAAM,aAAS,qBAAO,IAAI;AAE1B,8BAAU,MAAK;AACb,QAAI,CAAC,OAAO;AAAS;AACrB,QAAI,CAAC,OAAO,UAAU;AAAK;AAC3B,YAAO;AACP,WAAO,MAAK;AACV,aAAO,UAAU;IACnB;EACF,GAAG,CAAA,CAAE;AAEL,SAAO;AACT;;;ApB7BO,IAAM,mBAAe,6BAE1B,MAAS;AAQL,SAAU,cACd,YAAuD;AAEvD,QAAM,EAAE,UAAU,OAAM,IAAK;AAE7B,QAAM,QAAQ,EAAE,OAAO,OAAM;AAC7B,aAAO,6BACL,SACA,gBACA,6BAAc,aAAa,UAAU,OAAO,QAAQ,CAAC;AAEzD;;;AqB3BO,IAAMC,WAAU;;;ACEhB,IAAM,aAAa,MAAM,SAASC,QAAO;;;ACG1C,IAAOC,aAAP,cAAyB,UAAS;EAAxC,cAAA;;AACW,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOlB;EANE,IAAa,cAAW;AACtB,WAAO;EACT;EACA,IAAa,UAAO;AAClB,WAAO,WAAU;EACnB;;;;ACPI,IAAO,6BAAP,cAA0CC,WAAS;EAEvD,cAAA;AACE,UAAM,oDAAoD;MACxD,UAAU;KACX;AAJM,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKhB;;;;ACRF,IAAAC,gBAA2B;AAYrB,SAAU,UACd,aAA0C,CAAA,GAAE;AAE5C,QAAM,SAAS,WAAW,cAAU,0BAAW,YAAY;AAC3D,MAAI,CAAC;AAAQ,UAAM,IAAI,2BAA0B;AACjD,SAAO;AACT;;;ACJM,SAAU,YACd,QACA,YAAyC;AAEzC,QAAM,EAAE,SAAQ,IAAK;AACrB,SAAO,OAAO,UAAU,OAAO,UAAU,CAAC,QAAQ,eAAc;AAC9D,aACE,QACA,UAAoD;EAExD,CAAC;AACH;;;ACzBA,IAAAC,gBAAgC;AAChC,2BAAiD;AAEjD,IAAM,gBAAgB,CAAC,QACrB,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,GAAG;AAEzC,SAAU,gCAId,WACA,aACA,oBAAyD,aACzD,UAAmD,WAAS;AAE5D,QAAM,kBAAc,sBAAiB,CAAA,CAAE;AACvC,QAAM,aAAS,uDACb,WACA,aACA,mBACA,CAAC,MAAM,GACP,CAAC,GAAG,MAAK;AACP,QAAI,cAAc,CAAC,KAAK,cAAc,CAAC,KAAK,YAAY,QAAQ,QAAQ;AACtE,iBAAW,OAAO,YAAY,SAAS;AACrC,cAAM,QAAQ,QACX,EAA4B,GAAG,GAC/B,EAA4B,GAAG,CAAC;AAEnC,YAAI,CAAC;AAAO,iBAAO;MACrB;AACA,aAAO;IACT;AACA,WAAO,QAAQ,GAAG,CAAC;EACrB,CAAC;AAGH,aAAO,uBAAQ,MAAK;AAClB,QAAI,cAAc,MAAM,GAAG;AACzB,YAAM,gBAAgB,EAAE,GAAG,OAAM;AACjC,UAAI,aAAa,CAAA;AACjB,iBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAChC,aAAuC,GACtC;AACD,qBAAa;UACX,GAAG;UACH,CAAC,GAAG,GAAG;YACL,cAAc;YACd,YAAY;YACZ,KAAK,MAAK;AACR,kBAAI,CAAC,YAAY,QAAQ,SAAS,GAAG,GAAG;AACtC,4BAAY,QAAQ,KAAK,GAAG;cAC9B;AACA,qBAAO;YACT;;;MAGN;AACA,aAAO,iBAAiB,eAAe,UAAU;AACjD,aAAO;IACT;AAEA,WAAO;EACT,GAAG,CAAC,MAAM,CAAC;AACb;;;AC7CM,SAAU,WACd,aAA2C,CAAA,GAAE;AAE7C,QAAM,SAAS,UAAU,UAAU;AAEnC,SAAO,gCACL,CAAC,aAAa,aAAa,QAAQ,EAAE,SAAQ,CAAE,GAC/C,MAAM,WAAW,MAAM,CAAC;AAE5B;;;AC1BA,IAAAC,gBAA0B;AAsBpB,SAAU,iBAAiB,aAAyC,CAAA,GAAE;AAC1E,QAAM,EAAE,WAAW,aAAY,IAAK;AAEpC,QAAM,SAAS,UAAU,UAAU;AAEnC,+BAAU,MAAK;AACb,WAAO,aAAa,QAAQ;MAC1B,SAAS,MAAM,UAAQ;AACrB,aACG,SAAS,WAAW,kBAClB,SAAS,WAAW,gBACnB,SAAS,YAAY,WACzB,KAAK,WAAW,aAChB;AACA,gBAAM,EAAE,SAAS,WAAW,OAAO,SAAS,UAAS,IAAK;AAC1D,gBAAM,gBACJ,SAAS,WAAW;UAEpB,SAAS,WAAW;AACtB,iDAAY;YACV;YACA;YACA;YACA;YACA;YACA;;QAEJ,WACE,SAAS,WAAW,eACpB,KAAK,WAAW;AAEhB;MACJ;KACD;EACH,GAAG,CAAC,QAAQ,WAAW,YAAY,CAAC;AACtC;;;AC3DM,SAAU,kBACd,SACA,SAAa;AAEb,SAAO,iBAAiB,SAAS,OAAO;AAC1C;AAEM,SAAU,OAAO,UAAkB;AACvC,SAAO,KAAK,UAAU,UAAU,CAAC,GAAG,UAAS;AAC3C,QAAIC,eAAc,KAAK;AACrB,aAAO,OAAO,KAAK,KAAK,EACrB,KAAI,EACJ,OAAO,CAAC,QAAQ,QAAO;AACtB,eAAO,GAAG,IAAI,MAAM,GAAG;AACvB,eAAO;MACT,GAAG,CAAA,CAAS;AAChB,QAAI,OAAO,UAAU;AAAU,aAAO,MAAM,SAAQ;AACpD,WAAO;EACT,CAAC;AACH;AAGA,SAASA,eAAc,OAAU;AAC/B,MAAI,CAAC,mBAAmB,KAAK,GAAG;AAC9B,WAAO;EACT;AAGA,QAAM,OAAO,MAAM;AACnB,MAAI,OAAO,SAAS;AAAa,WAAO;AAGxC,QAAM,OAAO,KAAK;AAClB,MAAI,CAAC,mBAAmB,IAAI;AAAG,WAAO;AAItC,MAAI,CAAC,KAAK,eAAe,eAAe;AAAG,WAAO;AAGlD,SAAO;AACT;AAEA,SAAS,mBAAmB,GAAM;AAChC,SAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAC/C;AAEM,SAAU,mBACd,SAAa;AAIb,QAAM;;IAEJ;IAAY;IAAU;IAAQ;IAAa;IAAsB;IAAU;IAAM;IAAa;IAAS;IAAW;IAAU;IAAgB;IAAO;IAAY,mBAAAC;;IAG/J;IAAsB;IAAkB;;IAGxC;IAAoB;IAAS;IAAqB;IAAiB;IAAiB;IAA6B;IAAgB;IAAoB;IAAsB;IAAc;IAAQ;IAAW;IAAU;;;;IAKtN;IAAQ;IAAW;IACnB,GAAG;EAAI,IACL;AAEJ,SAAO;AACT;;;ACvDM,SAAU,iBACd,QACA,UAA+B,CAAA,GAAE;AAEjC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,OAAO,MAAM,KAAK,QAAQ;QAC9B,GAAG;OACc;AACnB,aAAO,QAAQ;IACjB;IACA,UAAU,aAAa,OAAO;;AAOlC;AAMM,SAAU,aACd,SAA4B;AAE5B,SAAO,CAAC,QAAQ,mBAAmB,OAAO,CAAC;AAC7C;;;ACjCM,SAAU,uBAA8C,QAAc;AAC1E,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,QAAQ,QAAQ,SAAS;IAClC;IACA,aAAa,CAAC,SAAS;;AAM3B;;;ACZM,SAAU,8BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,eAAe,QAAQ,SAAS;IACzC;IACA,aAAa,CAAC,gBAAgB;;AAMlC;;;ACdM,SAAU,0BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,WAAW,QAAQ,SAAS;IACrC;IACA,aAAa,CAAC,YAAY;;AAM9B;;;ACHM,SAAU,+BAGd,QAAgB,UAAmD,CAAA,GAAE;AACrE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,aAAO,mBAAmB,QAAQ,UAAU;IAC9C;IACA,UAAU,2BAA2B,OAAO;;AAOhD;AAQM,SAAU,2BAGd,UAAmD,CAAA,GAAE;AACrD,SAAO,CAAC,sBAAsB,mBAAmB,OAAO,CAAC;AAC3D;;;AC/BM,SAAU,wBAGd,QAAgB,UAA+C,CAAA,GAAS;AACxE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAS,IAAK;AACtB,YAAM,EAAE,SAAS,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1D,UAAI,CAAC,WAAW,CAAC;AACf,cAAM,IAAI,MAAM,kCAAkC;AACpD,aAAO,YAAY,QAAQ,EAAE,SAAS,WAAW,GAAI,WAAkB,CAAE;IAC3E;IACA,UAAU,oBAAoB,OAAO;;AAOzC;AAMM,SAAU,oBAGd,UAA+C,CAAA,GAAS;AACxD,QAAM,EAAE,WAAW,GAAG,GAAG,KAAI,IAAK;AAClC,SAAO,CAAC,eAAe,mBAAmB,IAAI,CAAC;AACjD;;;AC/BM,SAAU,yCACd,QACA,UAAuD,CAAA,GAAE;AAEzD,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,aAAO,6BAA6B,QAAQ,UAAU;IACxD;IACA,UAAU,qCAAqC,OAAO;;AAO1D;AAQM,SAAU,qCACd,UAAuD,CAAA,GAAE;AAEzD,SAAO,CAAC,gCAAgC,mBAAmB,OAAO,CAAC;AACrE;;;AC9BM,SAAU,uBACd,QACA,UAAqC,CAAA,GAAE;AAEvC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1D,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,YAAM,UAAU,MAAM,WAAW,QAAQ;QACvC,GAAI;QACJ;OACD;AACD,aAAO,WAAW;IACpB;IACA,UAAU,mBAAmB,OAAO;;AAOxC;AAMM,SAAU,mBACd,UAAqC,CAAA,GAAE;AAEvC,SAAO,CAAC,WAAW,mBAAmB,OAAO,CAAC;AAChD;;;ACrBM,SAAU,qBAMd,QACA,UAA2E,CAAA,GAAE;AAE7E,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,QAAQ,MAAM,SAAS,QAAQ,UAAU;AAC/C,aAAQ,SAAS;IACnB;IACA,UAAU,iBAAiB,OAAO;;AAOtC;AAgBM,SAAU,iBAMd,UAA2E,CAAA,GAAE;AAE7E,SAAO,CAAC,SAAS,mBAAmB,OAAO,CAAC;AAC9C;;;ACtDM,SAAU,2BAGd,QAAgB,UAAkD,CAAA,GAAE;AACpE,SAAO;IACL,QAAQ;IACR,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,cAAc,MAAM,eAAe,QAAQ,UAAU;AAC3D,aAAO,eAAe;IACxB;IACA,UAAU,uBAAuB,OAAO;;AAO5C;AAMM,SAAU,uBAGd,UAAkD,CAAA,GAAE;AACpD,SAAO,CAAC,eAAe,mBAAmB,OAAO,CAAC;AACpD;;;AC5BM,SAAU,qCAId,QACA,UAA4D,CAAA,GAAE;AAE9D,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,wBAAwB,MAAM,yBAClC,QACA,UAAU;AAEZ,aAAO,yBAAyB;IAClC;IACA,UAAU,iCAAiC,OAAO;;AAOtD;AAOM,SAAU,iCAGd,UAA4D,CAAA,GAAE;AAC9D,SAAO,CAAC,yBAAyB,mBAAmB,OAAO,CAAC;AAC9D;;;ACvCM,SAAU,wBACd,QACA,UAAsC,CAAA,GAAE;AAExC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1D,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,YAAM,WAAW,MAAM,YAAY,QAAQ,EAAE,GAAG,YAAY,QAAO,CAAE;AACrE,aAAQ,YAAY;IACtB;IACA,UAAU,oBAAoB,OAAO;;AAOzC;AAKM,SAAU,oBACd,SAAmC;AAEnC,SAAO,CAAC,eAAe,mBAAmB,OAAO,CAAC;AACpD;;;AC1BM,SAAU,2BACd,QACA,SAA8B;AAE9B,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,SAAS,MAAM,eAAe,QAAQ,UAAU;AACtD,aAAO;IACT;IACA,UAAU,uBAAuB,OAAO;IACxC,MAAM,cAAc,OAAK;AACvB,UAAI,iBAAiB;AAA4B,eAAO;AACxD,aAAO,eAAe;IACxB;;AAOJ;AAMM,SAAU,uBAAuB,SAA8B;AACnE,SAAO,CAAC,eAAe,mBAAmB,OAAO,CAAC;AACpD;;;AC1BM,SAAU,4BAGd,QAAgB,UAAmD,CAAA,GAAE;AACrE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,eAAe,MAAM,gBAAgB,QAAQ,UAAU;AAC7D,aAAO;IACT;IACA,UAAU,wBAAwB,OAAO;IACzC,MAAM,cAAc,OAAK;AACvB,UAAI,iBAAiB;AAA4B,eAAO;AACxD,aAAO,eAAe;IACxB;;AAOJ;AAYM,SAAU,wBAGd,UAAmD,CAAA,GAAE;AACrD,SAAO,CAAC,gBAAgB,mBAAmB,OAAO,CAAC;AACrD;;;ACtCM,SAAU,+BAGd,QAAgB,UAAsD,CAAA,GAAE;AACxE,SAAO;IACL,QAAQ;IACR,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAS,IAAK;AACtB,YAAM,EAAE,cAAc,GAAG,UAAU,IAAI,GAAG,WAAU,IAAK,SAAS,CAAC;AACnE,aAAO,mBAAmB,QAAQ;QAChC,GAAG;QACH;OACD;IACH;IACA,UAAU,2BAA2B,OAAO;;AAOhD;AAYM,SAAU,2BAGd,UAAsD,CAAA,GAAE;AACxD,QAAM,EAAE,WAAW,GAAG,WAAU,IAAK;AACrC,SAAO;IACL;IACA,EAAE,GAAG,mBAAmB,UAAU,GAAG,cAAc,uCAAW,IAAG;;AAErE;;;AC9CM,SAAU,0BACd,QACA,UAAwC,CAAA,GAAE;AAE1C,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,MAAM,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACvD,UAAI,CAAC;AAAM,cAAM,IAAI,MAAM,kBAAkB;AAC7C,aAAO,cAAc,QAAQ,EAAE,GAAG,YAAY,KAAI,CAAE;IACtD;IACA,UAAU,sBAAsB,OAAO;;AAO3C;AAMM,SAAU,sBACd,UAAwC,CAAA,GAAE;AAE1C,SAAO,CAAC,cAAc,mBAAmB,OAAO,CAAC;AACnD;;;AC3BM,SAAU,yBACd,QACA,UAAuC,CAAA,GAAE;AAEzC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,MAAM,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACvD,UAAI,CAAC;AAAM,cAAM,IAAI,MAAM,kBAAkB;AAC7C,aAAO,aAAa,QAAQ,EAAE,GAAG,YAAY,KAAI,CAAE;IACrD;IACA,UAAU,qBAAqB,OAAO;;AAO1C;AAMM,SAAU,qBACd,UAAuC,CAAA,GAAE;AAEzC,SAAO,CAAC,aAAa,mBAAmB,OAAO,CAAC;AAClD;;;AC3BM,SAAU,uBACd,QACA,UAAqC,CAAA,GAAE;AAEvC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1D,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,aAAO,WAAW,QAAQ,EAAE,GAAG,YAAY,QAAO,CAAE;IACtD;IACA,UAAU,mBAAmB,OAAO;;AAOxC;AAMM,SAAU,mBACd,UAAqC,CAAA,GAAE;AAEvC,SAAO,CAAC,WAAW,mBAAmB,OAAO,CAAC;AAChD;;;AC3BM,SAAU,2BACd,QACA,UAAyC,CAAA,GAAE;AAE3C,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,MAAM,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACvD,UAAI,CAAC;AAAM,cAAM,IAAI,MAAM,kBAAkB;AAC7C,aAAO,eAAe,QAAQ,EAAE,GAAG,YAAY,KAAI,CAAE;IACvD;IACA,UAAU,uBAAuB,OAAO;;AAO5C;AAMM,SAAU,uBACd,UAAyC,CAAA,GAAE;AAE3C,SAAO,CAAC,eAAe,mBAAmB,OAAO,CAAC;AACpD;;;AC3BM,SAAU,uBACd,QACA,UAAqC,CAAA,GAAE;AAEvC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,KAAK,MAAM,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC5D,UAAI,CAAC,OAAO,CAAC;AAAM,cAAM,IAAI,MAAM,2BAA2B;AAC9D,aAAO,WAAW,QAAQ,EAAE,GAAG,YAAY,KAAK,KAAI,CAAE;IACxD;IACA,UAAU,mBAAmB,OAAO;;AAOxC;AAMM,SAAU,mBACd,UAAqC,CAAA,GAAE;AAEvC,SAAO,CAAC,WAAW,mBAAmB,OAAO,CAAC;AAChD;;;ACpBM,SAAU,0BAGd,QAAgB,UAAiD,CAAA,GAAE;AACnE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EACJ,YACA,mBACA,UAAU,GACV,GAAG,WAAU,IACX,SAAS,CAAC;AACd,UAAI,CAAC;AAAY,cAAM,IAAI,MAAM,wBAAwB;AACzD,UAAI,CAAC;AAAmB,cAAM,IAAI,MAAM,+BAA+B;AACvE,YAAM,aAAa,MAAM,cAAc,QAAQ;QAC7C,GAAI;QACJ;QACA;OACD;AACD,aAAO,cAAc;IACvB;IACA,UAAU,sBAAsB,OAAO;;AAO3C;AAMM,SAAU,sBAGd,UAAiD,CAAA,GAAE;AACnD,SAAO,CAAC,cAAc,mBAAmB,OAAO,CAAC;AACnD;;;AC3CM,SAAU,wBAGd,QAAgB,UAA+C,CAAA,GAAE;AACjE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,WAAW,MAAM,YAAY,QAAQ,UAAU;AACrD,aAAO,YAAY;IACrB;IACA,UAAU,oBAAoB,OAAO;;AAOzC;AAMM,SAAU,oBAGd,UAA+C,CAAA,GAAE;AACjD,SAAO,CAAC,YAAY,mBAAmB,OAAO,CAAC;AACjD;;;AC/BM,SAAU,qBACd,QACA,UAAmC,CAAA,GAAE;AAErC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,UAAU,GAAG,aAAa,GAAG,WAAU,IAAK,SAAS,CAAC;AACvE,UAAI,CAAC,WAAW,CAAC;AACf,cAAM,IAAI,MAAM,sCAAsC;AACxD,aAAO,SAAS,QAAQ,EAAE,GAAG,YAAY,SAAS,YAAW,CAAE;IACjE;IACA,UAAU,iBAAiB,OAAO;;AAOtC;AAMM,SAAU,iBACd,SAAgC;AAEhC,SAAO,CAAC,YAAY,mBAAmB,OAAO,CAAC;AACjD;;;AC5BM,SAAU,yBACd,QACA,UAAuC,CAAA,GAAE;AAEzC,SAAO;IACL,QAAQ,EAAE,SAAQ,GAAE;AAClB,YAAM,EAAE,SAAS,MAAM,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAChE,UAAI,CAAC,WAAW,CAAC;AAAM,cAAM,IAAI,MAAM,+BAA+B;AACtE,aAAO,aAAa,QAAQ,EAAE,GAAG,YAAY,SAAS,KAAI,CAAE;IAC9D;IACA,UAAU,qBAAqB,OAAO;;AAO1C;AAMM,SAAU,qBACd,SAAoC;AAEpC,SAAO,CAAC,gBAAgB,mBAAmB,OAAO,CAAC;AACrD;;;AC3BM,SAAU,qBACd,QACA,UAAmC,CAAA,GAAE;AAErC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1D,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,aAAO,SAAS,QAAQ,EAAE,GAAG,YAAY,QAAO,CAAE;IACpD;IACA,UAAU,iBAAiB,OAAO;;AAOtC;AAMM,SAAU,iBACd,UAAmC,CAAA,GAAE;AAErC,SAAO,CAAC,SAAS,mBAAmB,OAAO,CAAC;AAC9C;;;ACxBM,SAAU,2BAGd,QAAgB,UAAkD,CAAA,GAAE;AACpE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,WAAW,aAAa,UAAU,MAAM,OAAAC,OAAK,IAAK,SAAS,CAAC;AACpE,UAAI,CAAC,aAAa,CAAC,eAAe,CAAC,YAAY,CAAC;AAC9C,cAAM,IAAI,MAAM,uDAAuD;AACzE,UAAI,CAAC,QAAQ,CAACA;AACZ,cAAM,IAAI,MACR,2DAA2D;AAE/D,YAAM,EAAE,UAAU,GAAG,GAAG,KAAI,IAAK,SAAS,CAAC;AAC3C,aAAO,eACL,QACA,IAAgC;IAEpC;IACA,UAAU,uBAAuB,OAAO;;AAO5C;AAYM,SAAU,uBAGd,UAAkD,CAAA,GAAE;AACpD,SAAO,CAAC,eAAe,mBAAmB,OAAO,CAAC;AACpD;;;AC1CM,SAAU,wCAMd,QACA,UAA+D,CAAA,GAAS;AAExE,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EACJ,MACA,oBACA,UAAU,GACV,GAAG,WAAU,IACX,SAAS,CAAC;AACd,UAAI,CAAC,QAAQ,CAAC;AACZ,cAAM,IAAI,MAAM,wCAAwC;AAE1D,YAAM,gBAAgB,MAAM,4BAA4B,QAAQ;QAC9D;QACA;QACA,GAAI;OACL;AACD,aAAO,iBAAiB;IAC1B;IACA,UAAU,oCAAoC,OAAO;;AAOzD;AAQM,SAAU,oCAKd,UAA+D,CAAA,GAAS;AACxE,SAAO,CAAC,4BAA4B,mBAAmB,OAAO,CAAC;AACjE;;;ACpDM,SAAU,gCACd,QACA,UAA8C,CAAA,GAAE;AAEhD,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1D,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,YAAM,mBAAmB,MAAM,oBAAoB,QAAQ;QACzD,GAAI;QACJ;OACD;AACD,aAAO,oBAAoB;IAC7B;IACA,UAAU,4BAA4B,OAAO;;AAOjD;AAOM,SAAU,4BACd,UAA8C,CAAA,GAAE;AAEhD,SAAO,CAAC,oBAAoB,mBAAmB,OAAO,CAAC;AACzD;;;AC7BM,SAAU,kCAGd,QAAgB,UAAyD,CAAA,GAAE;AAC3E,SAAO;IACL,QAAQ,EAAE,SAAQ,GAAE;AAClB,YAAM,EAAE,MAAM,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACvD,UAAI,CAAC;AAAM,cAAM,IAAI,MAAM,kBAAkB;AAC7C,aAAO,sBAAsB,QAAQ,EAAE,GAAG,YAAY,KAAI,CAAE;IAC9D;IACA,UAAU,8BAA8B,OAAO;;AAOnD;AAWM,SAAU,8BAGd,SAAsD;AACtD,SAAO,CAAC,yBAAyB,mBAAmB,OAAO,CAAC;AAC9D;;;AClCM,SAAU,4BAGd,QAAgB,UAAmD,CAAA,GAAE;AACrE,SAAO;IACL,QAAQ;IACR,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAS,IAAK;AACtB,YAAM,EAAE,cAAc,GAAG,UAAU,IAAI,GAAG,WAAU,IAAK,SAAS,CAAC;AACnE,aAAO,gBAAgB,QAAQ,EAAE,GAAG,YAAY,UAAS,CAAE;IAC7D;IACA,UAAU,wBAAwB,OAAO;;AAO7C;AAYM,SAAU,wBAGd,UAAmD,CAAA,GAAE;AACrD,QAAM,EAAE,WAAW,GAAG,WAAU,IAAK;AACrC,SAAO;IACL;IACA,EAAE,GAAG,mBAAmB,UAAU,GAAG,cAAc,uCAAW,IAAG;;AAErE;;;AC3BM,SAAU,kCAMd,QACA,SAOkE;AAElE,SAAO;IACL,GAAG,QAAQ;IACX,MAAM,QAAQ,EAAE,WAAW,SAAQ,GAAE;AACnC,YAAM,EAAE,UAAS,IAAK;AACtB,YAAM,EAAE,UAAU,GAAG,UAAU,IAAI,GAAG,WAAU,IAAK,SAAS,CAAC;AAC/D,aAAQ,MAAM,cAAc,QAAQ;QAClC,GAAG;QACH,WAAW,UAAU,SAAgB;OACtC;IACH;IACA,UAAU,8BAA8B,OAAO;;AASnD;AA4BM,SAAU,8BAMd,SAOkE;AAElE,QAAM,EAAE,WAAW,GAAG,OAAO,IAAI,GAAG,WAAU,IAAK;AACnD,SAAO,CAAC,yBAAyB,mBAAmB,UAAU,CAAC;AACjE;;;ACpFM,SAAU,sCAQd,QACA,UAII,CAAA,GAAS;AAEb,SAAO;IACL,QAAQ,EAAE,SAAQ,GAAE;AAClB,YAAM,EAAE,UAAU,GAAG,IAAI,GAAG,WAAU,IAAK,SAAS,CAAC;AACrD,UAAI,CAAC;AAAI,cAAM,IAAI,MAAM,gBAAgB;AACzC,aAAO,0BAA0B,QAAQ;QACvC;QACA,GAAI;OACL;IAGH;IACA,UAAU,kCAAkC,OAAO;;AAOvD;AAmBM,SAAU,kCAOd,SAAmE;AACnE,SAAO,CAAC,6BAA6B,mBAAmB,OAAO,CAAC;AAClE;;;ACnEM,SAAU,yBAMd,QACA,UAAgE,CAAA,GAAS;AAEzE,SAAO;;;IAGL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,MAAM,QAAQ;AACpB,UAAI,CAAC;AAAK,cAAM,IAAI,MAAM,iBAAiB;AAE3C,YAAM,EAAE,cAAc,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAC/D,YAAM,uBAAuB,MAAK;AAChC,cAAM,SAAS,SAAS,CAAC;AACzB,YAAI,OAAO;AAAS,iBAAO,EAAE,SAAS,OAAO,QAAO;AACpD,YAAI,OAAO;AAAM,iBAAO,EAAE,MAAM,OAAO,KAAI;AAC3C,cAAM,IAAI,MAAM,6BAA6B;MAC/C,GAAE;AAEF,UAAI,CAAC;AAAc,cAAM,IAAI,MAAM,0BAA0B;AAE7D,aAAO,aAAa,QAAQ;QAC1B;QACA;QACA,MAAM,WAAW;QACjB,GAAG;QACH,GAAG;OACJ;IACH;IACA,UAAU,qBAAqB,OAAc;;AAOjD;AAcM,SAAU,qBAKd,UAAgE,CAAA,GAAS;AACzE,QAAM,EAAE,KAAK,GAAG,GAAG,KAAI,IAAK;AAC5B,SAAO,CAAC,gBAAgB,mBAAmB,IAAI,CAAC;AAClD;;;ACvDM,SAAU,0BAKd,QACA,UAC6B,CAAA,GAAE;AAE/B,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AAlC9B;AAmCM,YAAM,YAA0C,CAAA;AAChD,YAAM,SAAS,SAAS,CAAC,EAAE,UAAU;AACrC,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAM,WAAW,SAAS,CAAC,EAAE,UAAU,CAAC;AACxC,cAAM,QAAO,aAAQ,cAAR,mBAAoB,IAAkC;AACnE,kBAAU,KAAK,EAAE,GAAG,UAAU,IAAG,CAAE;MACrC;AACA,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,aAAO,cAAc,QAAQ;QAC3B,GAAG;QACH;OACD;IACH;IACA,UAAU,sBAAsB,OAAO;;AAO3C;AAYM,SAAU,sBAKd,UAC6B,CAAA,GAAE;AAE/B,QAAM,YAAY,CAAA;AAClB,aAAW,YAAa,QAAQ,aAC9B,CAAA,GAA6D;AAC7D,UAAM,EAAE,KAAK,GAAG,GAAG,KAAI,IAAK;AAC5B,cAAU,KAAK,EAAE,GAAG,MAAM,SAAS,KAAK,WAAW,QAAQ,QAAO,CAAE;EACtE;AACA,SAAO;IACL;IACA,mBAAmB,EAAE,GAAG,SAAS,UAAS,CAAE;;AAEhD;;;AC/EM,SAAU,yBAAyB,QAAc;AACrD,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,UAAU,QAAQ,SAAS;IACpC;IACA,aAAa,CAAC,WAAW;;AAM7B;;;ACZM,SAAU,yBACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,UAAU,QAAQ,SAAS;IACpC;IACA,aAAa,CAAC,WAAW;;AAM7B;;;ACbM,SAAU,+BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,gBAAgB,QAAQ,SAAS;IAC1C;IACA,aAAa,CAAC,iBAAiB;;AAMnC;;;ACbM,SAAU,+BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,gBAAgB,QAAQ,SAAS;IAC1C;IACA,aAAa,CAAC,iBAAiB;;AAMnC;;;ACZM,SAAU,2BAA2B,QAAc;AACvD,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,YAAY,QAAQ,SAAS;IACtC;IACA,aAAa,CAAC,aAAa;;AAM/B;;;ACXM,SAAU,6BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,cAAc,QAAQ,SAAS;IACxC;IACA,aAAa,CAAC,eAAe;;AAMjC;;;ACbM,SAAU,6BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,cAAc,QAAQ,SAAS;IACxC;IACA,aAAa,CAAC,eAAe;;AAMjC;;;ACIM,SAAU,6BAWd,QACA,UAMI,CAAA,GAAS;AAEb,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,KAAK,UAAS,IAAK;AAC3B,UAAI,CAAC;AAAK,cAAM,IAAI,MAAM,iBAAiB;AAC3C,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AACjD,YAAM,EAAE,SAAS,aAAY,IAAK;AAClC,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,UAAI,CAAC;AAAc,cAAM,IAAI,MAAM,0BAA0B;AAC7D,aAAO,iBAAiB,QAAQ;QAC9B;QACA;QACA,GAAI;OACL;IACH;IACA,UAAU,yBAAyB,OAAO;;AAO9C;AA0BM,SAAU,yBAWd,UAMI,CAAA,GAAS;AAEb,QAAM,EAAE,KAAK,GAAG,WAAW,IAAI,GAAG,KAAI,IAAK;AAC3C,SAAO,CAAC,oBAAoB,mBAAmB,IAAI,CAAC;AACtD;;;AC1GM,SAAU,2BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,YAAY,QAAQ,SAAS;IACtC;IACA,aAAa,CAAC,aAAa;;AAM/B;;;ACPM,SAAU,0BACd,QACA,UAAwC,CAAA,GAAE;AAE1C,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,SAAS,SAAS,UAAS,IAAK,SAAS,CAAC;AAClD,UAAI,CAAC,WAAW,CAAC,WAAW,CAAC;AAC3B,cAAM,IAAI,MAAM,8CAA8C;AAEhE,YAAM,EAAE,UAAU,GAAG,GAAG,WAAU,IAAK,SAAS,CAAC;AAEjD,YAAM,WAAW,MAAM,cACrB,QACA,UAAqC;AAEvC,aAAO,YAAY;IACrB;IACA,UAAU,sBAAsB,OAAO;;AAO3C;AAKM,SAAU,sBACd,SAAqC;AAErC,SAAO,CAAC,iBAAiB,mBAAmB,OAAO,CAAC;AACtD;;;AC9BM,SAAU,4BAKd,QACA,UAAkE,CAAA,GAAS;AAE3E,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EACJ,SACA,SACA,aACA,WACA,OACA,UAAU,GACV,GAAG,WAAU,IACX,SAAS,CAAC;AACd,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,UAAI,CAAC;AAAS,cAAM,IAAI,MAAM,qBAAqB;AACnD,UAAI,CAAC;AAAa,cAAM,IAAI,MAAM,yBAAyB;AAC3D,UAAI,CAAC;AAAW,cAAM,IAAI,MAAM,uBAAuB;AACvD,UAAI,CAAC;AAAO,cAAM,IAAI,MAAM,mBAAmB;AAE/C,YAAM,WAAW,MAAM,gBAAgB,QAAQ;QAC7C,GAAG;QACH;QACA;QACA;QACA;QACA;OAC4B;AAC9B,aAAO,YAAY;IACrB;IACA,UAAU,wBAAwB,OAAO;;AAO7C;AAMM,SAAU,wBAId,SAA+D;AAC/D,SAAO,CAAC,mBAAmB,mBAAmB,OAAO,CAAC;AACxD;;;ACzDM,SAAU,+BACd,QACA,SAAkC;AAElC,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,UAAU,GAAG,IAAI,GAAG,WAAU,IAAK,SAAS,CAAC;AACrD,UAAI,CAAC;AAAI,cAAM,IAAI,MAAM,gBAAgB;AACzC,YAAM,SAAS,MAAM,mBAAmB,QAAQ,EAAE,GAAG,YAAY,GAAE,CAAE;AACrE,aAAO;IACT;IACA,UAAU,2BAA2B,OAAO;IAC5C,MAAM,cAAc,OAAK;AACvB,UAAI,iBAAiB;AAA4B,eAAO;AACxD,aAAO,eAAe;IACxB;;AAOJ;AAMM,SAAU,2BAA2B,SAAkC;AAC3E,SAAO,CAAC,eAAe,mBAAmB,OAAO,CAAC;AACpD;;;AC3BM,SAAU,sCAId,QACA,UAA6D,CAAA,GAAE;AAE/D,SAAO;IACL,MAAM,QAAQ,EAAE,SAAQ,GAAE;AACxB,YAAM,EAAE,MAAM,GAAG,WAAU,IAAK,SAAS,CAAC;AAC1C,UAAI,CAAC;AAAM,cAAM,IAAI,MAAM,kBAAkB;AAC7C,aAAO,0BAA0B,QAAQ;QACvC,GAAG;QACH,YAAY,QAAQ;QACpB;OACD;IAGH;IACA,UAAU,kCAAkC,OAAO;;AAOvD;AAYM,SAAU,kCAGd,UAA6D,CAAA,GAAE;AAC/D,QAAM,EAAE,YAAY,GAAG,GAAG,KAAI,IAAK;AACnC,SAAO,CAAC,6BAA6B,mBAAmB,IAAI,CAAC;AAC/D;;;ACrDM,SAAU,0BAA0B,QAAc;AACtD,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,WAAW,QAAQ,SAAS;IACrC;IACA,aAAa,CAAC,YAAY;;AAM9B;;;ACXM,SAAU,6BACd,QAAc;AAEd,SAAO;IACL,WAAW,WAAS;AAClB,aAAO,cAAc,QAAQ,SAAS;IACxC;IACA,aAAa,CAAC,eAAe;;AAYjC;;;AC2CM,SAAUC,UACd,YAEC;AAED,QAAM,SAAS,SAAkB;IAC/B,GAAI;IACJ,gBAAgB;;GACjB;AACD,SAAO,WAAW,WAAW;AAC7B,SAAO;AACT;AA2CM,SAAUC,kBAMd,YAEC;AAED,QAAM,SAAS,iBAA0B;IACvC,GAAI;IACJ,gBAAgB;;GACjB;AACD,SAAO,WAAW,WAAW;AAC7B,SAAO;AACT;;;ACvIA,IAAAC,gBAAqC;AAY/B,SAAU,WACd,aAA2C,CAAA,GAAE;AAE7C,QAAM,SAAS,UAAU,UAAU;AAEnC,aAAO,oCACL,CAAC,aAAa,aAAa,QAAQ,EAAE,SAAQ,CAAE,GAC/C,MAAM,WAAW,MAAM,GACvB,MAAM,WAAW,MAAM,CAAC;AAE5B;;;ACIM,SAAU,WAId,aAAuD,CAAA,GAAE;AAEzD,QAAM,EAAE,SAAS,QAAQ,CAAA,EAAE,IAAK;AAEhC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,uBAAuB,QAAQ;IAC7C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,YAAY,MAAM,WAAW,KAAK;AAE1D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;AC5CA,IAAAC,gBAA0B;AAwBpB,SAAU,eAOd,aAKI,CAAA,GAAS;AAEb,QAAM,EAAE,UAAU,MAAM,SAAS,QAAQ,GAAG,GAAG,KAAI,IAAK;AAExD,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAItC,+BAAU,MAAK;AACb,QAAI,CAAC;AAAS;AACd,QAAI,CAAC;AAAS;AACd,WAAO,YAAY,QAAQ;MACzB,GAAI;MACJ;MACA;KACD;EACH,GAAG;IACD;IACA;IACA;IACA;;IAEA,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;GACN;AACH;;;ACRM,SAAU,SAQd,aAMI,CAAA,GAAE;AAQN,QAAM,EAAE,QAAQ,CAAA,GAAI,MAAK,IAAK;AAE9B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,cAAc,eAAc;AAClC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAEtC,QAAM,UAAU,qBAAqB,QAAQ;IAC3C,GAAG;IACH;GACD;AACD,QAAM,UAAU,QAAQ,MAAM,WAAW,IAAI;AAE7C,iBAAe;IACb,GAAI;MACF,QAAQ,WAAW;MACnB,SAAS,WAAW;MACpB,GAAI,OAAO,UAAU,WAAW,QAAQ,CAAA;;IAE1C,SAAS,QACP,YAAY,OAAO,UAAU,WAAW,MAAM,UAAU,MAAM;IAEhE,QAAQ,OAAK;AACX,kBAAY,aAAa,QAAQ,UAAU,KAAK;IAClD;GACD;AAED,SAAOC,UAAS;IACd,GAAI;IACJ,GAAG;IACH;GACD;AAOH;;;ACzHA,IAAAC,gBAA0B;AAmBpB,SAAU,oBAKd,aAA6D,CAAA,GAAS;AAEtE,QAAM,EAAE,UAAU,MAAM,eAAe,QAAQ,GAAG,GAAG,KAAI,IAAK;AAE9D,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAItC,+BAAU,MAAK;AACb,QAAI,CAAC;AAAS;AACd,QAAI,CAAC;AAAe;AACpB,WAAO,iBAAiB,QAAQ;MAC9B,GAAI;MACJ;MACA;KACD;EACH,GAAG;IACD;IACA;IACA;IACA;;IAEA,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;GACN;AACH;;;ACJM,SAAU,eAMd,aAAoE,CAAA,GAAE;AAEtE,QAAM,EAAE,QAAQ,CAAA,GAAI,MAAK,IAAK;AAE9B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,cAAc,eAAc;AAClC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAEtC,QAAM,UAAU,2BAA2B,QAAQ;IACjD,GAAG;IACH;GACD;AAED,sBAAoB;IAClB,GAAI;MACF,QAAQ,WAAW;MACnB,SAAS,WAAW;MACpB,GAAI,OAAO,UAAU,WAAW,QAAQ,CAAA;;IAE1C,SAAS,SACN,MAAM,WAAW,UACf,OAAO,UAAU,WAAW,MAAM,UAAU,MAAM;IAEvD,cAAc,aAAW;AACvB,kBAAY,aAAa,QAAQ,UAAU,WAAW;IACxD;GACD;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;ACtDM,SAAU,yBAMd,aAII,CAAA,GAAE;AAEN,QAAM,EAAE,QAAQ,CAAA,EAAE,IAAK;AAEvB,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAEtC,QAAM,UAAU,qCAAqC,QAAQ;IAC3D,GAAG;IACH;GACD;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;AC5BM,SAAU,YAId,aAAwD,CAAA,GAAE;AAE1D,QAAM,EAAE,SAAS,QAAQ,CAAA,EAAE,IAAK;AAEhC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,wBAAwB,QAAQ;IAC9C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,YAAY,MAAM,WAAW,KAAK;AAE1D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;AClBM,SAAU,eAId,YAAwD;AAExD,QAAM,EAAE,QAAQ,CAAA,EAAE,IAAK;AAEvB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,UAAU,2BAA2B,QAAQ,UAAU;AAE7D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;ACRM,SAAU,gBAKd,aAAqE,CAAA,GAAE;AAEvE,QAAM,EAAE,SAAS,QAAQ,CAAA,EAAE,IAAK;AAEhC,QAAM,EAAE,QAAO,IAAK,WAAU;AAC9B,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,UAAU,4BAA4B,QAAQ;IAClD,GAAG;IACH,SAAS,WAAW;GACrB;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;ACxBM,SAAU,QAId,aAAoD,CAAA,GAAE;AAEtD,QAAM,EAAE,QAAQ,CAAA,EAAE,IAAK;AAEvB,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,iBAAiB,QAAQ;IACvC,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;AC7CA,IAAAC,gBAAqC;AAY/B,SAAU,UACd,aAA0C,CAAA,GAAE;AAE5C,QAAM,SAAS,UAAU,UAAU;AAEnC,aAAO,oCACL,CAAC,aAAa,YAAY,QAAQ,EAAE,SAAQ,CAAE,GAC9C,MAAM,UAAU,MAAM,GACtB,MAAM,UAAU,MAAM,CAAC;AAE3B;;;ACpBA,IAAAC,wBAAiD;AAoB3C,SAAU,UAMd,aAAmD,CAAA,GAAE;AAErD,QAAM,SAAS,UAAU,UAAU;AAEnC,aAAO,wDACL,CAAC,aAAa,YAAY,QAAQ,EAAE,SAAQ,CAAE,GAC9C,MAAM,UAAU,QAAQ,UAAU,GAClC,MAAM,UAAU,QAAQ,UAAU,GAClC,CAAC,MAAM,GACP,CAAC,GAAG,OAAM,uBAAG,UAAQ,uBAAG,IAAG;AAE/B;;;ACpCA,IAAAC,iBAA0B;;;ACH1B,IAAAC,iBAAqC;AAY/B,SAAU,cAGd,aAA8C,CAAA,GAAE;AAEhD,QAAM,SAAS,UAAU,UAAU;AAEnC,aAAO,qCACL,CAAC,aAAa,gBAAgB,QAAQ,EAAE,SAAQ,CAAE,GAClD,MAAM,cAAc,MAAM,GAC1B,MAAM,cAAc,MAAM,CAAC;AAE/B;;;ADsBM,SAAU,WAId,aAAoD,CAAA,GAAE;AAEtD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,uBAAuB,MAAM;AACrD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,gCAAU,MAAK;AACb,WAAO,OAAO,UACZ,CAAC,EAAE,OAAM,MAAO,QAChB,CAAC,QAAQ,mBAAkB;AACzB,UAAI,mBAAmB,eAAe,WAAW;AAC/C,eAAO,MAAK;IAChB,CAAC;EAEL,GAAG,CAAC,QAAQ,OAAO,KAAK,CAAC;AAGzB,SAAO;IACL,GAAI;IACJ,SAAS;IACT,cAAc;IACd,YAAY,cAAc,EAAE,OAAM,CAAE;;AAExC;;;AElFA,IAAAC,iBAAqC;AAU/B,SAAU,eACd,aAAuC,CAAA,GAAE;AAEzC,QAAM,SAAS,UAAU,UAAU;AAEnC,aAAO,qCACL,CAAC,aAAa,iBAAiB,QAAQ,EAAE,SAAQ,CAAE,GACnD,MAAM,eAAe,MAAM,GAC3B,MAAM,eAAe,MAAM,CAAC;AAEhC;;;ACXA,IAAAC,iBAAkC;AA4C5B,SAAU,mBAMd,aAAwE,CAAA,GAAE;AAE1E,QAAM,EAAE,QAAQ,CAAA,GAAI,GAAG,KAAI,IAAK;AAEhC,QAAM,SAAS,UAAU,IAAI;AAC7B,QAAM,cAAc,eAAc;AAClC,QAAM,EAAE,SAAS,WAAW,OAAM,IAAK,WAAW,EAAE,OAAM,CAAE;AAC5D,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AACrC,QAAM,kBAAkB,WAAW,aAAa;AAEhD,QAAM,EAAE,UAAU,GAAG,QAAO,IAAK,+BAG/B,QAAQ;IACR,GAAG;IACH,SAAS,WAAW,WAAW;IAC/B,WAAW;GACZ;AACD,QAAM,UAAU,SACb,WAAW,eACT,WAAW,mBAAkB,mDAAiB,kBAC9C,MAAM,WAAW,KAAK;AAG3B,QAAM,iBAAa,uBAAO,OAAO;AAEjC,gCAAU,MAAK;AACb,UAAM,kBAAkB,WAAW;AACnC,QAAI,CAAC,WAAW,iBAAiB;AAE/B,kBAAY,cAAc,EAAE,SAAQ,CAAE;AACtC,iBAAW,UAAU;IACvB,WAAW,YAAY,iBAAiB;AAEtC,kBAAY,kBAAkB,EAAE,SAAQ,CAAE;AAC1C,iBAAW,UAAU;IACvB;EACF,GAAG,CAAC,SAAS,WAAW,CAAC;AAEzB,SAAOC,UAAS;IACd,GAAG;IACH,GAAG;IACH;IACA;IACA,WAAW,OAAO;GACnB;AACH;;;ACzDM,SAAU,kBAId,aAA2D,CAAA,GAAE;AAE7D,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,8BAA8B,MAAM;AAC5D,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,gBAAgB;IAChB,qBAAqB;;AAEzB;;;AC7BM,SAAU,cACd,aAA+C,CAAA,GAAE;AAEjD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,0BAA0B,MAAM;AACxD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAED,SAAO;IACL,GAAG;IACH,YAAY,eAAe,EAAE,OAAM,CAAE,EAAE,IACrC,CAAC,eAAe,WAAW,SAAS;IAEtC,YAAY;IACZ,iBAAiB;;AAErB;;;AC9BM,SAAU,cAId,aAA0D,CAAA,GAAE;AAE5D,QAAM,EAAE,MAAM,QAAQ,CAAA,EAAE,IAAK;AAE7B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,0BAA0B,QAAQ;IAChD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,SAAS,MAAM,WAAW,KAAK;AAEvD,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;AClBM,SAAU,aAId,aAAyD,CAAA,GAAE;AAE3D,QAAM,EAAE,MAAM,QAAQ,CAAA,EAAE,IAAK;AAE7B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,yBAAyB,QAAQ;IAC/C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,SAAS,MAAM,WAAW,KAAK;AAEvD,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACtBM,SAAU,WAId,aAAuD,CAAA,GAAE;AAEzD,QAAM,EAAE,SAAS,QAAQ,CAAA,EAAE,IAAK;AAEhC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,uBAAuB,QAAQ;IAC7C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,YAAY,MAAM,WAAW,KAAK;AAE1D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACdM,SAAU,eAId,aAA2D,CAAA,GAAE;AAE7D,QAAM,EAAE,MAAM,QAAQ,CAAA,EAAE,IAAK;AAE7B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,2BAA2B,QAAQ;IACjD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,SAAS,MAAM,WAAW,KAAK;AAEvD,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACtBM,SAAU,WAId,aAAuD,CAAA,GAAE;AAEzD,QAAM,EAAE,KAAK,MAAM,QAAQ,CAAA,EAAE,IAAK;AAElC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,uBAAuB,QAAQ;IAC7C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,OAAO,SAAS,MAAM,WAAW,KAAK;AAE9D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACVM,SAAU,sBAKd,aAAwE,CAAA,GAAE;AAE1E,QAAM,EAAE,QAAQ,CAAA,EAAE,IAAK;AAEvB,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,+BAA+B,QAAQ;IACrD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;ACfM,SAAU,eACd,aAAuC,CAAA,GAAE;AAEzC,QAAM,EAAE,WAAW,QAAQ,CAAA,EAAE,IAAK;AAElC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,EAAE,MAAM,gBAAe,IAAK,mBAAmB;IACnD;IACA;IACA,OAAO,EAAE,SAAS,WAAW,YAAY,OAAS;GACnD;AACD,QAAM,UAAU,WAAW,YAAW,mDAAiB;AACvD,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,wBAAwB,QAAQ;IAC9C,GAAG;IACH;IACA,SAAS,WAAW,WAAW;IAC/B;GACD;AACD,QAAM,UAAU,SAAS,WAAW,eAAe,MAAM,WAAW,KAAK;AAEzE,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;AC7BM,SAAU,gCAId,aAGI,CAAA,GAAE;AAEN,QAAM,EAAE,QAAQ,CAAA,EAAE,IAAK;AAEvB,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,yCAAyC,QAAQ;IAC/D,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;ACnBM,SAAU,cAMd,aAAmE,CAAA,GAAE;AAErE,QAAM,EAAE,YAAY,mBAAmB,QAAQ,CAAA,EAAE,IAAK;AAEtD,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,0BAA0B,QAAQ;IAChD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QACd,cAAc,sBAAsB,MAAM,WAAW,KAAK;AAG5D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACtBM,SAAU,YAMd,aAAiE,CAAA,GAAE;AAEnE,QAAM,EAAE,QAAQ,CAAA,EAAE,IAAK;AAEvB,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAEtC,QAAM,UAAU,wBAAwB,QAAQ;IAC9C,GAAG;IACH;GACD;AAED,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,QAAO,CAAE;AAC1C;;;ACNM,SAAU,yBAOd,YAMC;AAED,QAAM,EAAE,YAAY,CAAA,GAAI,MAAK,IAAK;AAElC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,kCAAkC,QAAQ;IACxD,GAAG;IACH;IACA;IACA;GACD;AAED,SAAOC,kBAAiB;IACtB,GAAI;IACJ,GAAG;IACH,kBAAkB,QAAQ;IAC1B,mBAAmB,MAAM,qBAAqB;GAC/C;AACH;;;AChCM,SAAU,6BAYd,aAKI,CAAA,GAAS;AAOb,QAAM,EAAE,IAAI,QAAQ,CAAA,EAAE,IAAK;AAE3B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,sCAAsC,QAAQ;IAC5D,GAAG;IACH,SAAS,WAAW,WAAW;GAC8B;AAC/D,QAAM,UAAU,QAAQ,OAAO,MAAM,WAAW,KAAK;AAErD,SAAOC,UAAS;IACd,GAAI;IACJ,GAAG;IACH;GACD;AAMH;;;AChEM,SAAU,SAId,aAAqD,CAAA,GAAE;AAEvD,QAAM,EAAE,SAAS,aAAa,QAAQ,CAAA,EAAE,IAAK;AAE7C,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,qBAAqB,QAAQ;IAC3C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,WAAW,gBAAgB,MAAM,WAAW,KAAK;AAEzE,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;AC5CA,IAAAC,wBAAiD;AAsB3C,SAAU,gBAMd,aAAyD,CAAA,GAAE;AAE3D,QAAM,SAAS,UAAU,UAAU;AAEnC,aAAO,wDACL,CAAC,aAAa,kBAAkB,QAAQ,EAAE,SAAQ,CAAE,GACpD,MAAM,gBAAgB,QAAQ,UAAU,GACxC,MAAM,gBAAgB,QAAQ,UAAU,GACxC,CAAC,MAAM,GACP,CAAC,GAAG,OAAM,uBAAG,UAAQ,uBAAG,IAAG;AAE/B;;;ACYM,SAAU,gBAOd,aAMI,CAAA,GAAS;AAEb,QAAM,EAAE,KAAK,SAAS,cAAc,QAAQ,CAAA,EAAE,IAAK;AAEnD,QAAM,OAAO,WAAW;AAExB,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,yBACd,QACA,EAAE,GAAI,YAAoB,SAAS,WAAW,WAAW,QAAO,CAAE;AAEpE,QAAM,UAAU,SACb,WAAW,SAAS,OAAO,iBAAiB,MAAM,WAAW,KAAK;AAGrE,SAAOC,UAAS;IACd,GAAG;IACH,GAAG;IACH;IACA,mBAAmB,MAAM,qBAAqB;GAC/C;AACH;;;AClFA,IAAAC,iBAAwB;AA+BlB,SAAU,iBAMd,aAKI,CAAA,GAAE;AAEN,QAAM,EAAE,YAAY,CAAA,GAAI,QAAQ,CAAA,EAAE,IAAK;AAEvC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,0BACd,QACA,EAAE,GAAG,YAAY,QAAO,CAAE;AAG5B,QAAM,cAAU,wBAAQ,MAAK;AAC3B,QAAI,mBAAmB;AACvB,eAAW,YAAY,WAAW;AAChC,YAAM,EAAE,KAAK,SAAS,aAAY,IAChC;AACF,UAAI,CAAC,OAAO,CAAC,WAAW,CAAC,cAAc;AACrC,2BAAmB;AACnB;MACF;AACA,yBAAmB;IACrB;AACA,WAAO,QAAQ,qBAAqB,MAAM,WAAW,KAAK;EAC5D,GAAG,CAAC,WAAW,MAAM,OAAO,CAAC;AAE7B,SAAOC,UAAS;IACd,GAAG;IACH,GAAG;IACH;IACA,mBAAmB,MAAM,qBAAqB;GAC/C;AACH;;;AC3CM,SAAU,aACd,aAA8C,CAAA,GAAE;AAEhD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,yBAAyB,MAAM;AACvD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAED,SAAO;IACL,GAAG;IACH,YAAY,OAAO;IACnB,WAAW;IACX,gBAAgB;;AAEpB;;;ACdM,SAAU,aAId,aAAsD,CAAA,GAAE;AAExD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,yBAAyB,MAAM;AACvD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,WAAW;IACX,gBAAgB;;AAEpB;;;AClBM,SAAU,mBAId,aAA4D,CAAA,GAAE;AAE9D,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,+BAA+B,MAAM;AAC7D,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,iBAAiB;IACjB,sBAAsB;;AAE1B;;;ACzBM,SAAU,mBAId,aAA4D,CAAA,GAAE;AAE9D,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,+BAA+B,MAAM;AAC7D,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,iBAAiB;IACjB,sBAAsB;;AAE1B;;;AC7BM,SAAU,eACd,aAAgD,CAAA,GAAE;AAElD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,2BAA2B,MAAM;AACzD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAED,SAAO;IACL,GAAG;IACH,aAAa;IACb,kBAAkB;;AAEtB;;;AClBM,SAAU,iBACd,aAAkD,CAAA,GAAE;AAEpD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,6BAA6B,MAAM;AAC3D,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,eAAe;IACf,oBAAoB;;AAExB;;;ACHM,SAAU,oBAYd,aAOI,CAAA,GAAS;AASb,QAAM,EAAE,KAAK,SAAS,WAAW,cAAc,QAAQ,CAAA,EAAE,IAAK;AAE9D,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,EAAE,MAAM,gBAAe,IAAK,mBAAmB;IACnD;IACA;IACA,OAAO,EAAE,SAAS,WAAW,YAAY,OAAS;GACnD;AACD,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,6BAMd,QAAQ;IACR,GAAG;IACH,SAAS,WAAW,YAAW,mDAAiB;IAChD,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QACd,OAAO,WAAW,iBAAiB,MAAM,WAAW,KAAK;AAG3D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;AC9EM,SAAU,aAId,aAAyD,CAAA,GAAE;AAE3D,QAAM,EAAE,SAAS,MAAM,QAAQ,CAAA,EAAE,IAAK;AAEtC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,yBAAyB,QAAQ;IAC/C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,WAAW,SAAS,MAAM,WAAW,KAAK;AAElE,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACGM,SAAU,iBAId,aAA0D,CAAA,GAAE;AAE5D,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,6BAA6B,MAAM;AAC3D,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAED,SAAO;IACL,GAAG;IACH,YAAY,eAAe,EAAE,OAAM,CAAE,EAAE,IACrC,CAAC,eAAe,WAAW,SAAS;IAEtC,eAAe;IACf,oBAAoB;;AAExB;;;ACzBM,SAAU,eAId,aAAwD,CAAA,GAAE;AAE1D,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,2BAA2B,MAAM;AACzD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,QAAQ,UAAU,EAAE,OAAM,CAAE;IAC5B,aAAa;IACb,kBAAkB;;AAEtB;;;ACxCM,SAAU,SAId,aAAqD,CAAA,GAAE;AAEvD,QAAM,EAAE,SAAS,QAAQ,CAAA,EAAE,IAAK;AAEhC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,qBAAqB,QAAQ;IAC3C,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,YAAY,MAAM,WAAW,KAAK;AAE1D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACdM,SAAU,eAMd,aAAoE,CAAA,GAAE;AAEtE,QAAM,EAAE,WAAW,aAAa,UAAU,MAAM,QAAQ,CAAA,EAAE,IAAK;AAE/D,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,2BAA2B,QAAQ;IACjD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QACd,EAAE,aAAa,eAAe,YAAY,UAAU,MAAM,WAAW,KAAK;AAG5E,SAAOC,UAAS;IACd,GAAI;IACJ,GAAG;IACH;GACD;AACH;;;ACjCM,SAAU,4BAKd,aAII,CAAA,GAAS;AAEb,QAAM,EAAE,MAAM,oBAAoB,QAAQ,CAAA,EAAE,IAAK;AAEjD,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,wCAAwC,QAAQ;IAC9D,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QACd,EAAE,QAAQ,wBACP,QAAQ,wBACR,MAAM,WAAW,KAAK;AAG3B,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACzBM,SAAU,oBAId,aAAgE,CAAA,GAAE;AAElE,QAAM,EAAE,SAAS,QAAQ,CAAA,EAAE,IAAK;AAEhC,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,gCAAgC,QAAQ;IACtD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,YAAY,MAAM,WAAW,KAAK;AAE1D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACdM,SAAU,sBAMd,aAA2E,CAAA,GAAE;AAE7E,QAAM,EAAE,MAAM,QAAQ,CAAA,EAAE,IAAK;AAE7B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,kCAAkC,QAAQ;IACxD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,SAAS,MAAM,WAAW,KAAK;AAEvD,SAAOC,UAAS;IACd,GAAI;IACJ,GAAG;IACH;GACD;AACH;;;AC9BM,SAAU,iBAId,aAA6D,CAAA,GAAE;AAE/D,QAAM,EAAE,SAAS,SAAS,WAAW,QAAQ,CAAA,EAAE,IAAK;AAEpD,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,0BAA0B,QAAQ;IAChD,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QACd,WAAW,WAAW,cAAc,MAAM,WAAW,KAAK;AAG5D,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACpBM,SAAU,mBAMd,aAKI,CAAA,GAAS;AAEb,QAAM,EACJ,SACA,SACA,aACA,WACA,OACA,QAAQ,CAAA,EAAE,IACR;AAEJ,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,4BACd,QACA;IACE,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AAEH,QAAM,UAAU,QACd,WACE,WACA,eACA,aACA,UACC,MAAM,WAAW,KAAK;AAG3B,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;AC7DA,IAAAC,iBAAkC;AA4C5B,SAAU,gBAMd,aAAqE,CAAA,GAAE;AAEvE,QAAM,EAAE,QAAQ,CAAA,GAAI,GAAG,KAAI,IAAK;AAEhC,QAAM,SAAS,UAAU,IAAI;AAC7B,QAAM,cAAc,eAAc;AAClC,QAAM,EAAE,SAAS,WAAW,OAAM,IAAK,WAAW,EAAE,OAAM,CAAE;AAC5D,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AACrC,QAAM,kBAAkB,WAAW,aAAa;AAEhD,QAAM,EAAE,UAAU,GAAG,QAAO,IAAK,4BAC/B,QACA;IACE,GAAG;IACH,SAAS,WAAW,WAAW;IAC/B,WAAW,WAAW,aAAa;GACpC;AAEH,QAAM,UAAU,SACb,WAAW,eACT,WAAW,mBAAkB,mDAAiB,kBAC9C,MAAM,WAAW,KAAK;AAG3B,QAAM,iBAAa,uBAAO,OAAO;AAEjC,gCAAU,MAAK;AACb,UAAM,kBAAkB,WAAW;AACnC,QAAI,CAAC,WAAW,iBAAiB;AAE/B,kBAAY,cAAc,EAAE,SAAQ,CAAE;AACtC,iBAAW,UAAU;IACvB,WAAW,YAAY,iBAAiB;AAEtC,kBAAY,kBAAkB,EAAE,SAAQ,CAAE;AAC1C,iBAAW,UAAU;IACvB;EACF,GAAG,CAAC,SAAS,WAAW,CAAC;AAEzB,SAAOC,UAAS;IACd,GAAG;IACH,GAAG;IACH;IACA;IACA,WAAW,OAAO;GACZ;AACV;;;AC5EM,SAAU,sBAId,YAA+D;AAE/D,QAAM,EAAE,IAAI,QAAQ,CAAA,EAAE,IAAK;AAE3B,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,UAAU,+BAA+B,QAAQ,UAAU;AACjE,QAAM,UAAU,QAAQ,OAAO,MAAM,WAAW,KAAK;AAErD,SAAOC,UAAS,EAAE,GAAG,OAAO,GAAG,SAAS,QAAO,CAAE;AACnD;;;ACRM,SAAU,6BAMd,aAII,CAAA,GAAE;AAEN,QAAM,EAAE,MAAM,QAAQ,CAAA,EAAE,IAAK;AAE7B,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW,EAAE,OAAM,CAAE;AAErC,QAAM,UAAU,sCAAsC,QAAQ;IAC5D,GAAG;IACH,SAAS,WAAW,WAAW;GAChC;AACD,QAAM,UAAU,QAAQ,SAAS,MAAM,WAAW,KAAK;AAEvD,SAAOC,UAAS;IACd,GAAI;IACJ,GAAG;IACH;GACD;AACH;;;AC3BM,SAAU,cACd,aAA+C,CAAA,GAAE;AAEjD,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,0BAA0B,MAAM;AACxD,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAED,SAAO;IACL,GAAG;IACH,YAAY;IACZ,iBAAiB;;AAErB;;;ACvDA,IAAAC,iBAA0B;AAyBpB,SAAU,sBAQd,aAMI,CAAA,GAAS;AAEb,QAAM,EAAE,UAAU,MAAM,QAAQ,QAAQ,GAAG,GAAG,KAAI,IAAK;AAEvD,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAItC,gCAAU,MAAK;AACb,QAAI,CAAC;AAAS;AACd,QAAI,CAAC;AAAQ;AACb,WAAO,mBAAmB,QAAQ;MAChC,GAAI;MACJ;MACA;KACD;EACH,GAAG;IACD;IACA;IACA;IACA;;IAEA,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;GACN;AACH;;;AC3EA,IAAAC,iBAA0B;AAmBpB,SAAU,4BAKd,aAGI,CAAA,GAAS;AAEb,QAAM,EAAE,UAAU,MAAM,gBAAgB,QAAQ,GAAG,GAAG,KAAI,IAAK;AAE/D,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,gBAAgB,WAAW,EAAE,OAAM,CAAE;AAC3C,QAAM,UAAU,WAAW,WAAW;AAItC,gCAAU,MAAK;AACb,QAAI,CAAC;AAAS;AACd,QAAI,CAAC;AAAgB;AACrB,WAAO,yBAAyB,QAAQ;MACtC,GAAI;MACJ;MACA;KACD;EACH,GAAG;IACD;IACA;IACA;IACA;;IAEA,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;GACN;AACH;;;ACFM,SAAU,iBAId,aAA0D,CAAA,GAAE;AAE5D,QAAM,EAAE,SAAQ,IAAK;AAErB,QAAM,SAAS,UAAU,UAAU;AAEnC,QAAM,kBAAkB,6BAA6B,MAAM;AAC3D,QAAM,EAAE,QAAQ,aAAa,GAAG,OAAM,IAAK,YAAY;IACrD,GAAG;IACH,GAAG;GACJ;AAGD,SAAO;IACL,GAAG;IACH,eAAe;IACf,oBAAoB;;AAExB;", "names": ["useEffect", "useSyncExternalStore", "useRef", "useEffect", "useMemo", "import_react", "window", "connect", "disconnect", "provider", "error", "call", "result", "error", "hydrate", "createStore", "uid", "EventEmitter", "value", "index", "value", "deserialize", "serialize", "createStore", "connect", "getClient", "client", "chainId", "parameters", "version", "uid", "x", "disconnect", "connector", "fallback", "version", "version", "BaseError", "BaseError", "import_react", "import_react", "import_react", "isPlainObject", "structuralSharing", "index", "useQuery", "useInfiniteQuery", "import_react", "useQuery", "import_react", "useQuery", "import_react", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "import_react", "import_with_selector", "import_react", "import_react", "import_react", "import_react", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useInfiniteQuery", "useQuery", "useQuery", "import_with_selector", "useQuery", "import_react", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "useQuery", "import_react", "useQuery", "useQuery", "useQuery", "import_react", "import_react"]}