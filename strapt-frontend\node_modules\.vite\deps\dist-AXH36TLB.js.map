{"version": 3, "sources": ["../../@coinbase/wallet-sdk/node_modules/@noble/hashes/src/_u64.ts", "../../@coinbase/wallet-sdk/node_modules/@noble/hashes/src/crypto.ts", "../../@coinbase/wallet-sdk/node_modules/@noble/hashes/src/utils.ts", "../../@coinbase/wallet-sdk/node_modules/@noble/hashes/src/sha3.ts", "../../@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/util.cjs", "../../@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/abi.cjs", "../../@coinbase/wallet-sdk/dist/vendor-js/eth-eip712-util/index.cjs", "../../@coinbase/wallet-sdk/src/assets/wallet-logo.ts", "../../@coinbase/wallet-sdk/src/core/storage/ScopedLocalStorage.ts", "../../@coinbase/wallet-sdk/src/core/error/constants.ts", "../../@coinbase/wallet-sdk/src/core/error/utils.ts", "../../@coinbase/wallet-sdk/src/core/error/errors.ts", "../../@coinbase/wallet-sdk/src/core/type/index.ts", "../../@coinbase/wallet-sdk/src/core/type/util.ts", "../../@coinbase/wallet-sdk/src/util/cipher.ts", "../../@coinbase/wallet-sdk/src/sign/scw/SCWKeyManager.ts", "../../@coinbase/wallet-sdk/src/sdk-info.ts", "../../@coinbase/wallet-sdk/src/util/provider.ts", "../../@coinbase/wallet-sdk/src/sign/scw/SCWSigner.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/WalletLinkSigner.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/constants.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/type/Web3Response.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/connection/WalletLinkCipher.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/connection/WalletLinkHTTP.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/connection/WalletLinkWebSocket.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/connection/WalletLinkConnection.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/RelayEventManager.ts", "../../@coinbase/wallet-sdk/node_modules/@noble/hashes/src/crypto.ts", "../../@coinbase/wallet-sdk/node_modules/@noble/hashes/src/utils.ts", "../../@coinbase/wallet-sdk/node_modules/@noble/hashes/src/_md.ts", "../../@coinbase/wallet-sdk/node_modules/@noble/hashes/src/_u64.ts", "../../@coinbase/wallet-sdk/node_modules/@noble/hashes/src/sha2.ts", "../../@coinbase/wallet-sdk/node_modules/@noble/hashes/src/sha256.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/type/WalletLinkSession.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/ui/components/util.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/ui/components/cssReset/cssReset-css.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/ui/components/cssReset/cssReset.ts", "../../@coinbase/wallet-sdk/node_modules/clsx/dist/clsx.m.js", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/ui/components/Snackbar/Snackbar.tsx", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/ui/components/Snackbar/Snackbar-css.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/ui/WalletLinkRelayUI.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog.tsx", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/ui/components/RedirectDialog/RedirectDialog-css.ts", "../../@coinbase/wallet-sdk/src/core/constants.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/ui/WLMobileRelayUI.ts", "../../@coinbase/wallet-sdk/src/sign/walletlink/relay/WalletLinkRelay.ts", "../../@coinbase/wallet-sdk/src/sign/util.ts", "../../@coinbase/wallet-sdk/src/util/checkCrossOriginOpenerPolicy.ts", "../../@coinbase/wallet-sdk/src/util/web.ts", "../../@coinbase/wallet-sdk/src/core/communicator/Communicator.ts", "../../@coinbase/wallet-sdk/src/core/error/serialize.ts", "../../@coinbase/wallet-sdk/src/core/provider/interface.ts", "../../@coinbase/wallet-sdk/src/CoinbaseWalletProvider.ts", "../../@coinbase/wallet-sdk/src/util/validatePreferences.ts", "../../@coinbase/wallet-sdk/src/CoinbaseWalletSDK.ts", "../../@coinbase/wallet-sdk/src/createCoinbaseWalletProvider.ts", "../../@coinbase/wallet-sdk/src/createCoinbaseWalletSDK.ts", "../../@coinbase/wallet-sdk/src/index.ts"], "sourcesContent": ["/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n\nfunction fromBig(\n  n: bigint,\n  le = false\n): {\n  h: number;\n  l: number;\n} {\n  if (le) return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n  return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\n\nfunction split(lst: bigint[], le = false): Uint32Array[] {\n  const len = lst.length;\n  let Ah = new Uint32Array(len);\n  let Al = new Uint32Array(len);\n  for (let i = 0; i < len; i++) {\n    const { h, l } = fromBig(lst[i], le);\n    [Ah[i], Al[i]] = [h, l];\n  }\n  return [Ah, Al];\n}\n\nconst toBig = (h: number, l: number): bigint => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h: number, _l: number, s: number): number => h >>> s;\nconst shrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h: number, l: number, s: number): number => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h: number, l: number, s: number): number => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h: number, l: number, s: number): number => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h: number, l: number): number => l;\nconst rotr32L = (h: number, _l: number): number => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h: number, l: number, s: number): number => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h: number, l: number, s: number): number => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h: number, l: number, s: number): number => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h: number, l: number, s: number): number => (h << (s - 32)) | (l >>> (64 - s));\n\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(\n  Ah: number,\n  Al: number,\n  Bh: number,\n  Bl: number\n): {\n  h: number;\n  l: number;\n} {\n  const l = (Al >>> 0) + (Bl >>> 0);\n  return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al: number, Bl: number, Cl: number): number => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low: number, Ah: number, Bh: number, Ch: number): number =>\n  (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al: number, Bl: number, Cl: number, Dl: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number): number =>\n  (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al: number, Bl: number, Cl: number, Dl: number, El: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number): number =>\n  (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n\n// prettier-ignore\nexport {\n  add, add3H, add3L, add4H, add4L, add5H, add5L, fromBig, rotlBH, rotlBL, rotlSH, rotlSL, rotr32H, rotr32L, rotrBH, rotrBL, rotrSH, rotrSL, shrSH, shrSL, split, toBig\n};\n// prettier-ignore\nconst u64: { fromBig: typeof fromBig; split: typeof split; toBig: (h: number, l: number) => bigint; shrSH: (h: number, _l: number, s: number) => number; shrSL: (h: number, l: number, s: number) => number; rotrSH: (h: number, l: number, s: number) => number; rotrSL: (h: number, l: number, s: number) => number; rotrBH: (h: number, l: number, s: number) => number; rotrBL: (h: number, l: number, s: number) => number; rotr32H: (_h: number, l: number) => number; rotr32L: (h: number, _l: number) => number; rotlSH: (h: number, l: number, s: number) => number; rotlSL: (h: number, l: number, s: number) => number; rotlBH: (h: number, l: number, s: number) => number; rotlBL: (h: number, l: number, s: number) => number; add: typeof add; add3L: (Al: number, Bl: number, Cl: number) => number; add3H: (low: number, Ah: number, Bh: number, Ch: number) => number; add4L: (Al: number, Bl: number, Cl: number, Dl: number) => number; add4H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number) => number; add5H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number) => number; add5L: (Al: number, Bl: number, Cl: number, Dl: number, El: number) => number; } = {\n  fromBig, split, toBig,\n  shrSH, shrSL,\n  rotrSH, rotrSL, rotrBH, rotrBL,\n  rotr32H, rotr32L,\n  rotlSH, rotlSL, rotlBH, rotlBL,\n  add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n", "/**\n * Internal webcrypto alias.\n * We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n * See utils.ts for details.\n * @module\n */\ndeclare const globalThis: Record<string, any> | undefined;\nexport const crypto: any =\n  typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;\n", "/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\n\n/** Checks if something is Uint8Array. Be careful: nodejs <PERSON>uffer will return true. */\nexport function isBytes(a: unknown): a is Uint8Array {\n  return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n\n/** Asserts something is positive integer. */\nexport function anumber(n: number): void {\n  if (!Number.isSafeInteger(n) || n < 0) throw new Error('positive integer expected, got ' + n);\n}\n\n/** Asserts something is Uint8Array. */\nexport function abytes(b: Uint8Array | undefined, ...lengths: number[]): void {\n  if (!isBytes(b)) throw new Error('Uint8Array expected');\n  if (lengths.length > 0 && !lengths.includes(b.length))\n    throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n\n/** Asserts something is hash */\nexport function ahash(h: IHash): void {\n  if (typeof h !== 'function' || typeof h.create !== 'function')\n    throw new Error('Hash should be wrapped by utils.createHasher');\n  anumber(h.outputLen);\n  anumber(h.blockLen);\n}\n\n/** Asserts a hash instance has not been destroyed / finished */\nexport function aexists(instance: any, checkFinished = true): void {\n  if (instance.destroyed) throw new Error('Hash instance has been destroyed');\n  if (checkFinished && instance.finished) throw new Error('Hash#digest() has already been called');\n}\n\n/** Asserts output is properly-sized byte array */\nexport function aoutput(out: any, instance: any): void {\n  abytes(out);\n  const min = instance.outputLen;\n  if (out.length < min) {\n    throw new Error('digestInto() expects output buffer of length at least ' + min);\n  }\n}\n\n/** Generic type encompassing 8/16/32-byte arrays - but not 64-byte. */\n// prettier-ignore\nexport type TypedArray = Int8Array | Uint8ClampedArray | Uint8Array |\n  Uint16Array | Int16Array | Uint32Array | Int32Array;\n\n/** Cast u8 / u16 / u32 to u8. */\nexport function u8(arr: TypedArray): Uint8Array {\n  return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n\n/** Cast u8 / u16 / u32 to u32. */\nexport function u32(arr: TypedArray): Uint32Array {\n  return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nexport function clean(...arrays: TypedArray[]): void {\n  for (let i = 0; i < arrays.length; i++) {\n    arrays[i].fill(0);\n  }\n}\n\n/** Create DataView of an array for easy byte-level manipulation. */\nexport function createView(arr: TypedArray): DataView {\n  return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n\n/** The rotate right (circular right shift) operation for uint32 */\nexport function rotr(word: number, shift: number): number {\n  return (word << (32 - shift)) | (word >>> shift);\n}\n\n/** The rotate left (circular left shift) operation for uint32 */\nexport function rotl(word: number, shift: number): number {\n  return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nexport const isLE: boolean = /* @__PURE__ */ (() =>\n  new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n\n/** The byte swap operation for uint32 */\nexport function byteSwap(word: number): number {\n  return (\n    ((word << 24) & 0xff000000) |\n    ((word << 8) & 0xff0000) |\n    ((word >>> 8) & 0xff00) |\n    ((word >>> 24) & 0xff)\n  );\n}\n/** Conditionally byte swap if on a big-endian platform */\nexport const swap8IfBE: (n: number) => number = isLE\n  ? (n: number) => n\n  : (n: number) => byteSwap(n);\n\n/** @deprecated */\nexport const byteSwapIfBE: typeof swap8IfBE = swap8IfBE;\n/** In place byte swap for Uint32Array */\nexport function byteSwap32(arr: Uint32Array): Uint32Array {\n  for (let i = 0; i < arr.length; i++) {\n    arr[i] = byteSwap(arr[i]);\n  }\n  return arr;\n}\n\nexport const swap32IfBE: (u: Uint32Array) => Uint32Array = isLE\n  ? (u: Uint32Array) => u\n  : byteSwap32;\n\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin: boolean = /* @__PURE__ */ (() =>\n  // @ts-ignore\n  typeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) =>\n  i.toString(16).padStart(2, '0')\n);\n\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes: Uint8Array): string {\n  abytes(bytes);\n  // @ts-ignore\n  if (hasHexBuiltin) return bytes.toHex();\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 } as const;\nfunction asciiToBase16(ch: number): number | undefined {\n  if (ch >= asciis._0 && ch <= asciis._9) return ch - asciis._0; // '2' => 50-48\n  if (ch >= asciis.A && ch <= asciis.F) return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n  if (ch >= asciis.a && ch <= asciis.f) return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n  return;\n}\n\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex: string): Uint8Array {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  // @ts-ignore\n  if (hasHexBuiltin) return Uint8Array.fromHex(hex);\n  const hl = hex.length;\n  const al = hl / 2;\n  if (hl % 2) throw new Error('hex string expected, got unpadded hex of length ' + hl);\n  const array = new Uint8Array(al);\n  for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n    const n1 = asciiToBase16(hex.charCodeAt(hi));\n    const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n    if (n1 === undefined || n2 === undefined) {\n      const char = hex[hi] + hex[hi + 1];\n      throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n    }\n    array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n  }\n  return array;\n}\n\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nexport const nextTick = async (): Promise<void> => {};\n\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nexport async function asyncLoop(\n  iters: number,\n  tick: number,\n  cb: (i: number) => void\n): Promise<void> {\n  let ts = Date.now();\n  for (let i = 0; i < iters; i++) {\n    cb(i);\n    // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n    const diff = Date.now() - ts;\n    if (diff >= 0 && diff < tick) continue;\n    await nextTick();\n    ts += diff;\n  }\n}\n\n// Global symbols, but ts doesn't see them: https://github.com/microsoft/TypeScript/issues/31535\ndeclare const TextEncoder: any;\ndeclare const TextDecoder: any;\n\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nexport function utf8ToBytes(str: string): Uint8Array {\n  if (typeof str !== 'string') throw new Error('string expected');\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nexport function bytesToUtf8(bytes: Uint8Array): string {\n  return new TextDecoder().decode(bytes);\n}\n\n/** Accepted input of hash functions. Strings are converted to byte arrays. */\nexport type Input = string | Uint8Array;\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data: Input): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n\n/** KDFs can accept string or Uint8Array for user convenience. */\nexport type KDFInput = string | Uint8Array;\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nexport function kdfInputToBytes(data: KDFInput): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n\n/** Copies several Uint8Arrays into one. */\nexport function concatBytes(...arrays: Uint8Array[]): Uint8Array {\n  let sum = 0;\n  for (let i = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    abytes(a);\n    sum += a.length;\n  }\n  const res = new Uint8Array(sum);\n  for (let i = 0, pad = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    res.set(a, pad);\n    pad += a.length;\n  }\n  return res;\n}\n\ntype EmptyObj = {};\nexport function checkOpts<T1 extends EmptyObj, T2 extends EmptyObj>(\n  defaults: T1,\n  opts?: T2\n): T1 & T2 {\n  if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n    throw new Error('options should be object or undefined');\n  const merged = Object.assign(defaults, opts);\n  return merged as T1 & T2;\n}\n\n/** Hash interface. */\nexport type IHash = {\n  (data: Uint8Array): Uint8Array;\n  blockLen: number;\n  outputLen: number;\n  create: any;\n};\n\n/** For runtime check if class implements interface */\nexport abstract class Hash<T extends Hash<T>> {\n  abstract blockLen: number; // Bytes per block\n  abstract outputLen: number; // Bytes in output\n  abstract update(buf: Input): this;\n  // Writes digest into buf\n  abstract digestInto(buf: Uint8Array): void;\n  abstract digest(): Uint8Array;\n  /**\n   * Resets internal state. Makes Hash instance unusable.\n   * Reset is impossible for keyed hashes if key is consumed into state. If digest is not consumed\n   * by user, they will need to manually call `destroy()` when zeroing is necessary.\n   */\n  abstract destroy(): void;\n  /**\n   * Clones hash instance. Unsafe: doesn't check whether `to` is valid. Can be used as `clone()`\n   * when no options are passed.\n   * Reasons to use `_cloneInto` instead of clone: 1) performance 2) reuse instance => all internal\n   * buffers are overwritten => causes buffer overwrite which is used for digest in some cases.\n   * There are no guarantees for clean-up because it's impossible in JS.\n   */\n  abstract _cloneInto(to?: T): T;\n  // Safe version that clones internal state\n  abstract clone(): T;\n}\n\n/**\n * XOF: streaming API to read digest in chunks.\n * Same as 'squeeze' in keccak/k12 and 'seek' in blake3, but more generic name.\n * When hash used in XOF mode it is up to user to call '.destroy' afterwards, since we cannot\n * destroy state, next call can require more bytes.\n */\nexport type HashXOF<T extends Hash<T>> = Hash<T> & {\n  xof(bytes: number): Uint8Array; // Read 'bytes' bytes from digest stream\n  xofInto(buf: Uint8Array): Uint8Array; // read buf.length bytes from digest stream into buf\n};\n\n/** Hash function */\nexport type CHash = ReturnType<typeof createHasher>;\n/** Hash function with output */\nexport type CHashO = ReturnType<typeof createOptHasher>;\n/** XOF with output */\nexport type CHashXO = ReturnType<typeof createXOFer>;\n\n/** Wraps hash function, creating an interface on top of it */\nexport function createHasher<T extends Hash<T>>(\n  hashCons: () => Hash<T>\n): {\n  (msg: Input): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(): Hash<T>;\n} {\n  const hashC = (msg: Input): Uint8Array => hashCons().update(toBytes(msg)).digest();\n  const tmp = hashCons();\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = () => hashCons();\n  return hashC;\n}\n\nexport function createOptHasher<H extends Hash<H>, T extends Object>(\n  hashCons: (opts?: T) => Hash<H>\n): {\n  (msg: Input, opts?: T): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(opts?: T): Hash<H>;\n} {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts?: T) => hashCons(opts);\n  return hashC;\n}\n\nexport function createXOFer<H extends HashXOF<H>, T extends Object>(\n  hashCons: (opts?: T) => HashXOF<H>\n): {\n  (msg: Input, opts?: T): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(opts?: T): HashXOF<H>;\n} {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts?: T) => hashCons(opts);\n  return hashC;\n}\nexport const wrapConstructor: typeof createHasher = createHasher;\nexport const wrapConstructorWithOpts: typeof createOptHasher = createOptHasher;\nexport const wrapXOFConstructorWithOpts: typeof createXOFer = createXOFer;\n\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nexport function randomBytes(bytesLength = 32): Uint8Array {\n  if (crypto && typeof crypto.getRandomValues === 'function') {\n    return crypto.getRandomValues(new Uint8Array(bytesLength));\n  }\n  // Legacy Node.js compatibility\n  if (crypto && typeof crypto.randomBytes === 'function') {\n    return Uint8Array.from(crypto.randomBytes(bytesLength));\n  }\n  throw new Error('crypto.getRandomValues must be defined');\n}\n", "/**\n * SHA3 (keccak) hash function, based on a new \"Sponge function\" design.\n * Different from older hashes, the internal state is bigger than output size.\n *\n * Check out [FIPS-202](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.202.pdf),\n * [Website](https://keccak.team/keccak.html),\n * [the differences between SHA-3 and Keccak](https://crypto.stackexchange.com/questions/15727/what-are-the-key-differences-between-the-draft-sha-3-standard-and-the-keccak-sub).\n *\n * Check out `sha3-addons` module for cSHAKE, k12, and others.\n * @module\n */\nimport { rotlBH, rotlBL, rotlSH, rotlSL, split } from './_u64.ts';\n// prettier-ignore\nimport {\n  abytes, aexists, anumber, aoutput,\n  clean, createHasher, createXOFer, Hash,\n  swap32IfBE,\n  toBytes, u32,\n  type CHash, type CHashXO, type HashXOF, type Input\n} from './utils.ts';\n\n// No __PURE__ annotations in sha3 header:\n// EVERYTHING is in fact used on every export.\n// Various per round constants calculations\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst _7n = BigInt(7);\nconst _256n = BigInt(256);\nconst _0x71n = BigInt(0x71);\nconst SHA3_PI: number[] = [];\nconst SHA3_ROTL: number[] = [];\nconst _SHA3_IOTA: bigint[] = [];\nfor (let round = 0, R = _1n, x = 1, y = 0; round < 24; round++) {\n  // Pi\n  [x, y] = [y, (2 * x + 3 * y) % 5];\n  SHA3_PI.push(2 * (5 * y + x));\n  // Rotational\n  SHA3_ROTL.push((((round + 1) * (round + 2)) / 2) % 64);\n  // Iota\n  let t = _0n;\n  for (let j = 0; j < 7; j++) {\n    R = ((R << _1n) ^ ((R >> _7n) * _0x71n)) % _256n;\n    if (R & _2n) t ^= _1n << ((_1n << /* @__PURE__ */ BigInt(j)) - _1n);\n  }\n  _SHA3_IOTA.push(t);\n}\nconst IOTAS = split(_SHA3_IOTA, true);\nconst SHA3_IOTA_H = IOTAS[0];\nconst SHA3_IOTA_L = IOTAS[1];\n\n// Left rotation (without 0, 32, 64)\nconst rotlH = (h: number, l: number, s: number) => (s > 32 ? rotlBH(h, l, s) : rotlSH(h, l, s));\nconst rotlL = (h: number, l: number, s: number) => (s > 32 ? rotlBL(h, l, s) : rotlSL(h, l, s));\n\n/** `keccakf1600` internal function, additionally allows to adjust round count. */\nexport function keccakP(s: Uint32Array, rounds: number = 24): void {\n  const B = new Uint32Array(5 * 2);\n  // NOTE: all indices are x2 since we store state as u32 instead of u64 (bigints to slow in js)\n  for (let round = 24 - rounds; round < 24; round++) {\n    // Theta θ\n    for (let x = 0; x < 10; x++) B[x] = s[x] ^ s[x + 10] ^ s[x + 20] ^ s[x + 30] ^ s[x + 40];\n    for (let x = 0; x < 10; x += 2) {\n      const idx1 = (x + 8) % 10;\n      const idx0 = (x + 2) % 10;\n      const B0 = B[idx0];\n      const B1 = B[idx0 + 1];\n      const Th = rotlH(B0, B1, 1) ^ B[idx1];\n      const Tl = rotlL(B0, B1, 1) ^ B[idx1 + 1];\n      for (let y = 0; y < 50; y += 10) {\n        s[x + y] ^= Th;\n        s[x + y + 1] ^= Tl;\n      }\n    }\n    // Rho (ρ) and Pi (π)\n    let curH = s[2];\n    let curL = s[3];\n    for (let t = 0; t < 24; t++) {\n      const shift = SHA3_ROTL[t];\n      const Th = rotlH(curH, curL, shift);\n      const Tl = rotlL(curH, curL, shift);\n      const PI = SHA3_PI[t];\n      curH = s[PI];\n      curL = s[PI + 1];\n      s[PI] = Th;\n      s[PI + 1] = Tl;\n    }\n    // Chi (χ)\n    for (let y = 0; y < 50; y += 10) {\n      for (let x = 0; x < 10; x++) B[x] = s[y + x];\n      for (let x = 0; x < 10; x++) s[y + x] ^= ~B[(x + 2) % 10] & B[(x + 4) % 10];\n    }\n    // Iota (ι)\n    s[0] ^= SHA3_IOTA_H[round];\n    s[1] ^= SHA3_IOTA_L[round];\n  }\n  clean(B);\n}\n\n/** Keccak sponge function. */\nexport class Keccak extends Hash<Keccak> implements HashXOF<Keccak> {\n  protected state: Uint8Array;\n  protected pos = 0;\n  protected posOut = 0;\n  protected finished = false;\n  protected state32: Uint32Array;\n  protected destroyed = false;\n\n  public blockLen: number;\n  public suffix: number;\n  public outputLen: number;\n  protected enableXOF = false;\n  protected rounds: number;\n\n  // NOTE: we accept arguments in bytes instead of bits here.\n  constructor(\n    blockLen: number,\n    suffix: number,\n    outputLen: number,\n    enableXOF = false,\n    rounds: number = 24\n  ) {\n    super();\n    this.blockLen = blockLen;\n    this.suffix = suffix;\n    this.outputLen = outputLen;\n    this.enableXOF = enableXOF;\n    this.rounds = rounds;\n    // Can be passed from user as dkLen\n    anumber(outputLen);\n    // 1600 = 5x5 matrix of 64bit.  1600 bits === 200 bytes\n    // 0 < blockLen < 200\n    if (!(0 < blockLen && blockLen < 200))\n      throw new Error('only keccak-f1600 function is supported');\n    this.state = new Uint8Array(200);\n    this.state32 = u32(this.state);\n  }\n  clone(): Keccak {\n    return this._cloneInto();\n  }\n  protected keccak(): void {\n    swap32IfBE(this.state32);\n    keccakP(this.state32, this.rounds);\n    swap32IfBE(this.state32);\n    this.posOut = 0;\n    this.pos = 0;\n  }\n  update(data: Input): this {\n    aexists(this);\n    data = toBytes(data);\n    abytes(data);\n    const { blockLen, state } = this;\n    const len = data.length;\n    for (let pos = 0; pos < len; ) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      for (let i = 0; i < take; i++) state[this.pos++] ^= data[pos++];\n      if (this.pos === blockLen) this.keccak();\n    }\n    return this;\n  }\n  protected finish(): void {\n    if (this.finished) return;\n    this.finished = true;\n    const { state, suffix, pos, blockLen } = this;\n    // Do the padding\n    state[pos] ^= suffix;\n    if ((suffix & 0x80) !== 0 && pos === blockLen - 1) this.keccak();\n    state[blockLen - 1] ^= 0x80;\n    this.keccak();\n  }\n  protected writeInto(out: Uint8Array): Uint8Array {\n    aexists(this, false);\n    abytes(out);\n    this.finish();\n    const bufferOut = this.state;\n    const { blockLen } = this;\n    for (let pos = 0, len = out.length; pos < len; ) {\n      if (this.posOut >= blockLen) this.keccak();\n      const take = Math.min(blockLen - this.posOut, len - pos);\n      out.set(bufferOut.subarray(this.posOut, this.posOut + take), pos);\n      this.posOut += take;\n      pos += take;\n    }\n    return out;\n  }\n  xofInto(out: Uint8Array): Uint8Array {\n    // Sha3/Keccak usage with XOF is probably mistake, only SHAKE instances can do XOF\n    if (!this.enableXOF) throw new Error('XOF is not possible for this instance');\n    return this.writeInto(out);\n  }\n  xof(bytes: number): Uint8Array {\n    anumber(bytes);\n    return this.xofInto(new Uint8Array(bytes));\n  }\n  digestInto(out: Uint8Array): Uint8Array {\n    aoutput(out, this);\n    if (this.finished) throw new Error('digest() was already called');\n    this.writeInto(out);\n    this.destroy();\n    return out;\n  }\n  digest(): Uint8Array {\n    return this.digestInto(new Uint8Array(this.outputLen));\n  }\n  destroy(): void {\n    this.destroyed = true;\n    clean(this.state);\n  }\n  _cloneInto(to?: Keccak): Keccak {\n    const { blockLen, suffix, outputLen, rounds, enableXOF } = this;\n    to ||= new Keccak(blockLen, suffix, outputLen, enableXOF, rounds);\n    to.state32.set(this.state32);\n    to.pos = this.pos;\n    to.posOut = this.posOut;\n    to.finished = this.finished;\n    to.rounds = rounds;\n    // Suffix can change in cSHAKE\n    to.suffix = suffix;\n    to.outputLen = outputLen;\n    to.enableXOF = enableXOF;\n    to.destroyed = this.destroyed;\n    return to;\n  }\n}\n\nconst gen = (suffix: number, blockLen: number, outputLen: number) =>\n  createHasher(() => new Keccak(blockLen, suffix, outputLen));\n\n/** SHA3-224 hash function. */\nexport const sha3_224: CHash = /* @__PURE__ */ (() => gen(0x06, 144, 224 / 8))();\n/** SHA3-256 hash function. Different from keccak-256. */\nexport const sha3_256: CHash = /* @__PURE__ */ (() => gen(0x06, 136, 256 / 8))();\n/** SHA3-384 hash function. */\nexport const sha3_384: CHash = /* @__PURE__ */ (() => gen(0x06, 104, 384 / 8))();\n/** SHA3-512 hash function. */\nexport const sha3_512: CHash = /* @__PURE__ */ (() => gen(0x06, 72, 512 / 8))();\n\n/** keccak-224 hash function. */\nexport const keccak_224: CHash = /* @__PURE__ */ (() => gen(0x01, 144, 224 / 8))();\n/** keccak-256 hash function. Different from SHA3-256. */\nexport const keccak_256: CHash = /* @__PURE__ */ (() => gen(0x01, 136, 256 / 8))();\n/** keccak-384 hash function. */\nexport const keccak_384: CHash = /* @__PURE__ */ (() => gen(0x01, 104, 384 / 8))();\n/** keccak-512 hash function. */\nexport const keccak_512: CHash = /* @__PURE__ */ (() => gen(0x01, 72, 512 / 8))();\n\nexport type ShakeOpts = { dkLen?: number };\n\nconst genShake = (suffix: number, blockLen: number, outputLen: number) =>\n  createXOFer<HashXOF<Keccak>, ShakeOpts>(\n    (opts: ShakeOpts = {}) =>\n      new Keccak(blockLen, suffix, opts.dkLen === undefined ? outputLen : opts.dkLen, true)\n  );\n\n/** SHAKE128 XOF with 128-bit security. */\nexport const shake128: CHashXO = /* @__PURE__ */ (() => genShake(0x1f, 168, 128 / 8))();\n/** SHAKE256 XOF with 256-bit security. */\nexport const shake256: CHashXO = /* @__PURE__ */ (() => genShake(0x1f, 136, 256 / 8))();\n", "// Extracted from https://github.com/ethereumjs/ethereumjs-util and stripped out irrelevant code\n// Original code licensed under the Mozilla Public License Version 2.0\n\n/* eslint-disable */\n//prettier-ignore\nconst { keccak_256 } = require('@noble/hashes/sha3')\n\n/**\n * Returns a buffer filled with 0s\n * @method zeros\n * @param {Number} bytes  the number of bytes the buffer should be\n * @return {Buffer}\n */\nfunction zeros (bytes) {\n  return Buffer.allocUnsafe(bytes).fill(0)\n}\n\nfunction bitLengthFromBigInt (num) {\n  return num.toString(2).length\n}\n\nfunction bufferBEFromBigInt(num, length) {\n  let hex = num.toString(16);\n  // Ensure the hex string length is even\n  if (hex.length % 2 !== 0) hex = '0' + hex;\n  // Convert hex string to a byte array\n  const byteArray = hex.match(/.{1,2}/g).map(byte => parseInt(byte, 16));\n  // Ensure the byte array is of the specified length\n  while (byteArray.length < length) {\n    byteArray.unshift(0); // Prepend with zeroes if shorter than required length\n  }\n\n  return Buffer.from(byteArray);\n}\n\nfunction twosFromBigInt(value, width) {\n  const isNegative = value < 0n;\n  let result;\n  if (isNegative) {\n    // Prepare a mask for the specified width to perform NOT operation\n    const mask = (1n << BigInt(width)) - 1n;\n    // Invert bits (using NOT) and add one\n    result = (~value & mask) + 1n;\n  } else {\n    result = value;\n  }\n  // Ensure the result fits in the specified width\n  result &= (1n << BigInt(width)) - 1n;\n\n  return result;\n}\n\n/**\n * Left Pads an `Array` or `Buffer` with leading zeros till it has `length` bytes.\n * Or it truncates the beginning if it exceeds.\n * @method setLength\n * @param {Buffer|Array} msg the value to pad\n * @param {Number} length the number of bytes the output should be\n * @param {Boolean} [right=false] whether to start padding form the left or right\n * @return {Buffer|Array}\n */\nfunction setLength (msg, length, right) {\n  const buf = zeros(length)\n  msg = toBuffer(msg)\n  if (right) {\n    if (msg.length < length) {\n      msg.copy(buf)\n      return buf\n    }\n    return msg.slice(0, length)\n  } else {\n    if (msg.length < length) {\n      msg.copy(buf, length - msg.length)\n      return buf\n    }\n    return msg.slice(-length)\n  }\n}\n\n/**\n * Right Pads an `Array` or `Buffer` with leading zeros till it has `length` bytes.\n * Or it truncates the beginning if it exceeds.\n * @param {Buffer|Array} msg the value to pad\n * @param {Number} length the number of bytes the output should be\n * @return {Buffer|Array}\n */\nfunction setLengthRight (msg, length) {\n  return setLength(msg, length, true)\n}\n\n/**\n * Attempts to turn a value into a `Buffer`. As input it supports `Buffer`, `String`, `Number`, null/undefined, `BIgInt` and other objects with a `toArray()` method.\n * @param {*} v the value\n */\nfunction toBuffer (v) {\n  if (!Buffer.isBuffer(v)) {\n    if (Array.isArray(v)) {\n      v = Buffer.from(v)\n    } else if (typeof v === 'string') {\n      if (isHexString(v)) {\n        v = Buffer.from(padToEven(stripHexPrefix(v)), 'hex')\n      } else {\n        v = Buffer.from(v)\n      }\n    } else if (typeof v === 'number') {\n      v = intToBuffer(v)\n    } else if (v === null || v === undefined) {\n      v = Buffer.allocUnsafe(0)\n    } else if (typeof v === 'bigint') {\n      v = bufferBEFromBigInt(v)\n    } else if (v.toArray) {\n      // TODO: bigint should be handled above, may remove this duplicate\n      // converts a BigInt to a Buffer\n      v = Buffer.from(v.toArray())\n    } else {\n      throw new Error('invalid type')\n    }\n  }\n  return v\n}\n\n/**\n * Converts a `Buffer` into a hex `String`\n * @param {Buffer} buf\n * @return {String}\n */\nfunction bufferToHex (buf) {\n  buf = toBuffer(buf)\n  return '0x' + buf.toString('hex')\n}\n\n/**\n * Creates Keccak hash of the input\n * @param {Buffer|Array|String|Number} a the input data\n * @param {Number} [bits=256] the Keccak width\n * @return {Buffer}\n */\nfunction keccak (a, bits) {\n  a = toBuffer(a)\n  if (!bits) bits = 256\n  if (bits !== 256) {\n    throw new Error('unsupported')\n  }\n  return Buffer.from(keccak_256(new Uint8Array(a)))\n}\n\nfunction padToEven (str) {\n  return str.length % 2 ? '0' + str : str\n}\n\nfunction isHexString (str) {\n  return typeof str === 'string' && str.match(/^0x[0-9A-Fa-f]*$/)\n}\n\nfunction stripHexPrefix (str) {\n  if (typeof str === 'string' && str.startsWith('0x')) {\n    return str.slice(2)\n  }\n  return str\n}\n\nmodule.exports = {\n  zeros,\n  setLength,\n  setLengthRight,\n  isHexString,\n  stripHexPrefix,\n  toBuffer,\n  bufferToHex,\n  keccak,\n  bitLengthFromBigInt,\n  bufferBEFromBigInt,\n  twosFromBigInt\n}\n", "// Extracted from https://github.com/ethereumjs/ethereumjs-abi and stripped out irrelevant code\n// Original code licensed under the MIT License - Copyright (c) 2015 <PERSON>\n\n/* eslint-disable */\n//prettier-ignore\nconst util = require('./util.cjs')\n\n// Convert from short to canonical names\n// FIXME: optimise or make this nicer?\nfunction elementaryName (name) {\n  if (name.startsWith('int[')) {\n    return 'int256' + name.slice(3)\n  } else if (name === 'int') {\n    return 'int256'\n  } else if (name.startsWith('uint[')) {\n    return 'uint256' + name.slice(4)\n  } else if (name === 'uint') {\n    return 'uint256'\n  } else if (name.startsWith('fixed[')) {\n    return 'fixed128x128' + name.slice(5)\n  } else if (name === 'fixed') {\n    return 'fixed128x128'\n  } else if (name.startsWith('ufixed[')) {\n    return 'ufixed128x128' + name.slice(6)\n  } else if (name === 'ufixed') {\n    return 'ufixed128x128'\n  }\n  return name\n}\n\n// Parse N from type<N>\nfunction parseTypeN (type) {\n  return Number.parseInt(/^\\D+(\\d+)$/.exec(type)[1], 10)\n}\n\n// Parse N,M from type<N>x<M>\nfunction parseTypeNxM (type) {\n  var tmp = /^\\D+(\\d+)x(\\d+)$/.exec(type)\n  return [ Number.parseInt(tmp[1], 10), Number.parseInt(tmp[2], 10) ]\n}\n\n// Parse N in type[<N>] where \"type\" can itself be an array type.\nfunction parseTypeArray (type) {\n  var tmp = type.match(/(.*)\\[(.*?)\\]$/)\n  if (tmp) {\n    return tmp[2] === '' ? 'dynamic' : Number.parseInt(tmp[2], 10)\n  }\n  return null\n}\n\nfunction parseNumber (arg) {\n  var type = typeof arg\n  if (type === 'string' || type === 'number') {\n    return BigInt(arg)\n  } else if (type === 'bigint') {\n    return arg\n  } else {\n    throw new Error('Argument is not a number')\n  }\n}\n\n// Encodes a single item (can be dynamic array)\n// @returns: Buffer\nfunction encodeSingle (type, arg) {\n  var size, num, ret, i\n\n  if (type === 'address') {\n    return encodeSingle('uint160', parseNumber(arg))\n  } else if (type === 'bool') {\n    return encodeSingle('uint8', arg ? 1 : 0)\n  } else if (type === 'string') {\n    return encodeSingle('bytes', new Buffer(arg, 'utf8'))\n  } else if (isArray(type)) {\n    // this part handles fixed-length ([2]) and variable length ([]) arrays\n    // NOTE: we catch here all calls to arrays, that simplifies the rest\n    if (typeof arg.length === 'undefined') {\n      throw new Error('Not an array?')\n    }\n    size = parseTypeArray(type)\n    if (size !== 'dynamic' && size !== 0 && arg.length > size) {\n      throw new Error('Elements exceed array size: ' + size)\n    }\n    ret = []\n    type = type.slice(0, type.lastIndexOf('['))\n    if (typeof arg === 'string') {\n      arg = JSON.parse(arg)\n    }\n    for (i in arg) {\n      ret.push(encodeSingle(type, arg[i]))\n    }\n    if (size === 'dynamic') {\n      var length = encodeSingle('uint256', arg.length)\n      ret.unshift(length)\n    }\n    return Buffer.concat(ret)\n  } else if (type === 'bytes') {\n    arg = new Buffer(arg)\n\n    ret = Buffer.concat([ encodeSingle('uint256', arg.length), arg ])\n\n    if ((arg.length % 32) !== 0) {\n      ret = Buffer.concat([ ret, util.zeros(32 - (arg.length % 32)) ])\n    }\n\n    return ret\n  } else if (type.startsWith('bytes')) {\n    size = parseTypeN(type)\n    if (size < 1 || size > 32) {\n      throw new Error('Invalid bytes<N> width: ' + size)\n    }\n\n    return util.setLengthRight(arg, 32)\n  } else if (type.startsWith('uint')) {\n    size = parseTypeN(type)\n    if ((size % 8) || (size < 8) || (size > 256)) {\n      throw new Error('Invalid uint<N> width: ' + size)\n    }\n\n    num = parseNumber(arg)\n    const bitLength = util.bitLengthFromBigInt(num)\n    if (bitLength > size) {\n      throw new Error('Supplied uint exceeds width: ' + size + ' vs ' + bitLength)\n    }\n\n    if (num < 0) {\n      throw new Error('Supplied uint is negative')\n    }\n\n    return util.bufferBEFromBigInt(num, 32);\n  } else if (type.startsWith('int')) {\n    size = parseTypeN(type)\n    if ((size % 8) || (size < 8) || (size > 256)) {\n      throw new Error('Invalid int<N> width: ' + size)\n    }\n\n    num = parseNumber(arg)\n    const bitLength = util.bitLengthFromBigInt(num)\n    if (bitLength > size) {\n      throw new Error('Supplied int exceeds width: ' + size + ' vs ' + bitLength)\n    }\n\n    const twos = util.twosFromBigInt(num, 256);\n\n    return util.bufferBEFromBigInt(twos, 32);\n  } else if (type.startsWith('ufixed')) {\n    size = parseTypeNxM(type)\n\n    num = parseNumber(arg)\n\n    if (num < 0) {\n      throw new Error('Supplied ufixed is negative')\n    }\n\n    return encodeSingle('uint256', num * BigInt(2) ** BigInt(size[1]))\n  } else if (type.startsWith('fixed')) {\n    size = parseTypeNxM(type)\n\n    return encodeSingle('int256', parseNumber(arg) * BigInt(2) ** BigInt(size[1]))\n  }\n\n  throw new Error('Unsupported or invalid type: ' + type)\n}\n\n// Is a type dynamic?\nfunction isDynamic (type) {\n  // FIXME: handle all types? I don't think anything is missing now\n  return (type === 'string') || (type === 'bytes') || (parseTypeArray(type) === 'dynamic')\n}\n\n// Is a type an array?\nfunction isArray (type) {\n  return type.lastIndexOf(']') === type.length - 1\n}\n\n// Encode a method/event with arguments\n// @types an array of string type names\n// @args  an array of the appropriate values\nfunction rawEncode (types, values) {\n  var output = []\n  var data = []\n\n  var headLength = 32 * types.length\n\n  for (var i in types) {\n    var type = elementaryName(types[i])\n    var value = values[i]\n    var cur = encodeSingle(type, value)\n\n    // Use the head/tail method for storing dynamic data\n    if (isDynamic(type)) {\n      output.push(encodeSingle('uint256', headLength))\n      data.push(cur)\n      headLength += cur.length\n    } else {\n      output.push(cur)\n    }\n  }\n\n  return Buffer.concat(output.concat(data))\n}\n\nfunction solidityPack (types, values) {\n  if (types.length !== values.length) {\n    throw new Error('Number of types are not matching the values')\n  }\n\n  var size, num\n  var ret = []\n\n  for (var i = 0; i < types.length; i++) {\n    var type = elementaryName(types[i])\n    var value = values[i]\n\n    if (type === 'bytes') {\n      ret.push(value)\n    } else if (type === 'string') {\n      ret.push(new Buffer(value, 'utf8'))\n    } else if (type === 'bool') {\n      ret.push(new Buffer(value ? '01' : '00', 'hex'))\n    } else if (type === 'address') {\n      ret.push(util.setLength(value, 20))\n    } else if (type.startsWith('bytes')) {\n      size = parseTypeN(type)\n      if (size < 1 || size > 32) {\n        throw new Error('Invalid bytes<N> width: ' + size)\n      }\n\n      ret.push(util.setLengthRight(value, size))\n    } else if (type.startsWith('uint')) {\n      size = parseTypeN(type)\n      if ((size % 8) || (size < 8) || (size > 256)) {\n        throw new Error('Invalid uint<N> width: ' + size)\n      }\n\n      num = parseNumber(value)\n      const bitLength = util.bitLengthFromBigInt(num)\n      if (bitLength > size) {\n        throw new Error('Supplied uint exceeds width: ' + size + ' vs ' + bitLength)\n      }\n\n      ret.push(util.bufferBEFromBigInt(num, size / 8))\n    } else if (type.startsWith('int')) {\n      size = parseTypeN(type)\n      if ((size % 8) || (size < 8) || (size > 256)) {\n        throw new Error('Invalid int<N> width: ' + size)\n      }\n\n      num = parseNumber(value)\n      const bitLength = util.bitLengthFromBigInt(num)\n      if (bitLength > size) {\n        throw new Error('Supplied int exceeds width: ' + size + ' vs ' + bitLength)\n      }\n\n      const twos = util.twosFromBigInt(num, size);\n      ret.push(util.bufferBEFromBigInt(twos, size / 8))\n    } else {\n      // FIXME: support all other types\n      throw new Error('Unsupported or invalid type: ' + type)\n    }\n  }\n\n  return Buffer.concat(ret)\n}\n\nfunction soliditySHA3 (types, values) {\n  return util.keccak(solidityPack(types, values))\n}\n\nmodule.exports = {\n  rawEncode,\n  solidityPack,\n  soliditySHA3\n}\n", "/* eslint-disable */\n//prettier-ignore\n\nconst util = require('./util.cjs')\nconst abi = require('./abi.cjs')\n\nconst TYPED_MESSAGE_SCHEMA = {\n  type: 'object',\n  properties: {\n    types: {\n      type: 'object',\n      additionalProperties: {\n        type: 'array',\n        items: {\n          type: 'object',\n          properties: {\n            name: {type: 'string'},\n            type: {type: 'string'},\n          },\n          required: ['name', 'type'],\n        },\n      },\n    },\n    primaryType: {type: 'string'},\n    domain: {type: 'object'},\n    message: {type: 'object'},\n  },\n  required: ['types', 'primaryType', 'domain', 'message'],\n}\n\n/**\n * A collection of utility functions used for signing typed data\n */\nconst TypedDataUtils = {\n  /**\n   * Encodes an object by encoding and concatenating each of its members\n   *\n   * @param {string} primaryType - Root type\n   * @param {Object} data - Object to encode\n   * @param {Object} types - Type definitions\n   * @returns {string} - Encoded representation of an object\n   */\n  encodeData (primaryType, data, types, useV4 = true) {\n    const encodedTypes = ['bytes32']\n    const encodedValues = [this.hashType(primaryType, types)]\n\n    if(useV4) {\n      const encodeField = (name, type, value) => {\n        if (types[type] !== undefined) {\n          return ['bytes32', value == null ?\n            '0x0000000000000000000000000000000000000000000000000000000000000000' :\n            util.keccak(this.encodeData(type, value, types, useV4))]\n        }\n\n        if(value === undefined)\n          throw new Error(`missing value for field ${name} of type ${type}`)\n\n        if (type === 'bytes') {\n          return ['bytes32', util.keccak(value)]\n        }\n\n        if (type === 'string') {\n          // convert string to buffer - prevents ethUtil from interpreting strings like '0xabcd' as hex\n          if (typeof value === 'string') {\n            value = Buffer.from(value, 'utf8')\n          }\n          return ['bytes32', util.keccak(value)]\n        }\n\n        if (type.lastIndexOf(']') === type.length - 1) {\n          const parsedType = type.slice(0, type.lastIndexOf('['))\n          const typeValuePairs = value.map(item =>\n            encodeField(name, parsedType, item))\n          return ['bytes32', util.keccak(abi.rawEncode(\n            typeValuePairs.map(([type]) => type),\n            typeValuePairs.map(([, value]) => value),\n          ))]\n        }\n\n        return [type, value]\n      }\n\n      for (const field of types[primaryType]) {\n        const [type, value] = encodeField(field.name, field.type, data[field.name])\n        encodedTypes.push(type)\n        encodedValues.push(value)\n      }\n    } else {\n      for (const field of types[primaryType]) {\n        let value = data[field.name]\n        if (value !== undefined) {\n          if (field.type === 'bytes') {\n            encodedTypes.push('bytes32')\n            value = util.keccak(value)\n            encodedValues.push(value)\n          } else if (field.type === 'string') {\n            encodedTypes.push('bytes32')\n            // convert string to buffer - prevents ethUtil from interpreting strings like '0xabcd' as hex\n            if (typeof value === 'string') {\n              value = Buffer.from(value, 'utf8')\n            }\n            value = util.keccak(value)\n            encodedValues.push(value)\n          } else if (types[field.type] !== undefined) {\n            encodedTypes.push('bytes32')\n            value = util.keccak(this.encodeData(field.type, value, types, useV4))\n            encodedValues.push(value)\n          } else if (field.type.lastIndexOf(']') === field.type.length - 1) {\n            throw new Error('Arrays currently unimplemented in encodeData')\n          } else {\n            encodedTypes.push(field.type)\n            encodedValues.push(value)\n          }\n        }\n      }\n    }\n\n    return abi.rawEncode(encodedTypes, encodedValues)\n  },\n\n  /**\n   * Encodes the type of an object by encoding a comma delimited list of its members\n   *\n   * @param {string} primaryType - Root type to encode\n   * @param {Object} types - Type definitions\n   * @returns {string} - Encoded representation of the type of an object\n   */\n  encodeType (primaryType, types) {\n    let result = ''\n    let deps = this.findTypeDependencies(primaryType, types).filter(dep => dep !== primaryType)\n    deps = [primaryType].concat(deps.sort())\n    for (const type of deps) {\n      const children = types[type]\n      if (!children) {\n        throw new Error('No type definition specified: ' + type)\n      }\n      result += type + '(' + types[type].map(({ name, type }) => type + ' ' + name).join(',') + ')'\n    }\n    return result\n  },\n\n  /**\n   * Finds all types within a type definition object\n   *\n   * @param {string} primaryType - Root type\n   * @param {Object} types - Type definitions\n   * @param {Array} results - current set of accumulated types\n   * @returns {Array} - Set of all types found in the type definition\n   */\n  findTypeDependencies (primaryType, types, results = []) {\n    primaryType = primaryType.match(/^\\w*/)[0]\n    if (results.includes(primaryType) || types[primaryType] === undefined) { return results }\n    results.push(primaryType)\n    for (const field of types[primaryType]) {\n      for (const dep of this.findTypeDependencies(field.type, types, results)) {\n        !results.includes(dep) && results.push(dep)\n      }\n    }\n    return results\n  },\n\n  /**\n   * Hashes an object\n   *\n   * @param {string} primaryType - Root type\n   * @param {Object} data - Object to hash\n   * @param {Object} types - Type definitions\n   * @returns {Buffer} - Hash of an object\n   */\n  hashStruct (primaryType, data, types, useV4 = true) {\n    return util.keccak(this.encodeData(primaryType, data, types, useV4))\n  },\n\n  /**\n   * Hashes the type of an object\n   *\n   * @param {string} primaryType - Root type to hash\n   * @param {Object} types - Type definitions\n   * @returns {string} - Hash of an object\n   */\n  hashType (primaryType, types) {\n    return util.keccak(this.encodeType(primaryType, types))\n  },\n\n  /**\n   * Removes properties from a message object that are not defined per EIP-712\n   *\n   * @param {Object} data - typed message object\n   * @returns {Object} - typed message object with only allowed fields\n   */\n  sanitizeData (data) {\n    const sanitizedData = {}\n    for (const key in TYPED_MESSAGE_SCHEMA.properties) {\n      data[key] && (sanitizedData[key] = data[key])\n    }\n    if (sanitizedData.types) {\n      sanitizedData.types = Object.assign({ EIP712Domain: [] }, sanitizedData.types)\n    }\n    return sanitizedData\n  },\n\n  /**\n   * Returns the hash of a typed message as per EIP-712 for signing\n   *\n   * @param {Object} typedData - Types message data to sign\n   * @returns {string} - sha3 hash for signing\n   */\n  hash (typedData, useV4 = true) {\n    const sanitizedData = this.sanitizeData(typedData)\n    const parts = [Buffer.from('1901', 'hex')]\n    parts.push(this.hashStruct('EIP712Domain', sanitizedData.domain, sanitizedData.types, useV4))\n    if (sanitizedData.primaryType !== 'EIP712Domain') {\n      parts.push(this.hashStruct(sanitizedData.primaryType, sanitizedData.message, sanitizedData.types, useV4))\n    }\n    return util.keccak(Buffer.concat(parts))\n  },\n}\n\nmodule.exports = {\n  TYPED_MESSAGE_SCHEMA,\n  TypedDataUtils,\n\n  hashForSignTypedDataLegacy: function (msgParams) {\n    return typedSignatureHashLegacy(msgParams.data)\n  },\n\n  hashForSignTypedData_v3: function (msgParams) {\n    return TypedDataUtils.hash(msgParams.data, false)\n  },\n\n  hashForSignTypedData_v4: function (msgParams) {\n    return TypedDataUtils.hash(msgParams.data)\n  },\n}\n\n/**\n * @param typedData - Array of data along with types, as per EIP712.\n * @returns Buffer\n */\nfunction typedSignatureHashLegacy(typedData) {\n  const error = new Error('Expect argument to be non-empty array')\n  if (typeof typedData !== 'object' || !typedData.length) throw error\n\n  const data = typedData.map(function (e) {\n    return e.type === 'bytes' ? util.toBuffer(e.value) : e.value\n  })\n  const types = typedData.map(function (e) { return e.type })\n  const schema = typedData.map(function (e) {\n    if (!e.name) throw error\n    return e.type + ' ' + e.name\n  })\n\n  return abi.soliditySHA3(\n    ['bytes32', 'bytes32'],\n    [\n      abi.soliditySHA3(new Array(typedData.length).fill('string'), schema),\n      abi.soliditySHA3(types, data)\n    ]\n  )\n}", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "/**\n * Internal webcrypto alias.\n * We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n * See utils.ts for details.\n * @module\n */\ndeclare const globalThis: Record<string, any> | undefined;\nexport const crypto: any =\n  typeof globalThis === 'object' && 'crypto' in globalThis ? globalThis.crypto : undefined;\n", "/**\n * Utilities for hex, bytes, CSPRNG.\n * @module\n */\n/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated (2025-04-30), we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\n\n/** Checks if something is Uint8Array. Be careful: nodejs <PERSON>uffer will return true. */\nexport function isBytes(a: unknown): a is Uint8Array {\n  return a instanceof Uint8Array || (ArrayBuffer.isView(a) && a.constructor.name === 'Uint8Array');\n}\n\n/** Asserts something is positive integer. */\nexport function anumber(n: number): void {\n  if (!Number.isSafeInteger(n) || n < 0) throw new Error('positive integer expected, got ' + n);\n}\n\n/** Asserts something is Uint8Array. */\nexport function abytes(b: Uint8Array | undefined, ...lengths: number[]): void {\n  if (!isBytes(b)) throw new Error('Uint8Array expected');\n  if (lengths.length > 0 && !lengths.includes(b.length))\n    throw new Error('Uint8Array expected of length ' + lengths + ', got length=' + b.length);\n}\n\n/** Asserts something is hash */\nexport function ahash(h: IHash): void {\n  if (typeof h !== 'function' || typeof h.create !== 'function')\n    throw new Error('Hash should be wrapped by utils.createHasher');\n  anumber(h.outputLen);\n  anumber(h.blockLen);\n}\n\n/** Asserts a hash instance has not been destroyed / finished */\nexport function aexists(instance: any, checkFinished = true): void {\n  if (instance.destroyed) throw new Error('Hash instance has been destroyed');\n  if (checkFinished && instance.finished) throw new Error('Hash#digest() has already been called');\n}\n\n/** Asserts output is properly-sized byte array */\nexport function aoutput(out: any, instance: any): void {\n  abytes(out);\n  const min = instance.outputLen;\n  if (out.length < min) {\n    throw new Error('digestInto() expects output buffer of length at least ' + min);\n  }\n}\n\n/** Generic type encompassing 8/16/32-byte arrays - but not 64-byte. */\n// prettier-ignore\nexport type TypedArray = Int8Array | Uint8ClampedArray | Uint8Array |\n  Uint16Array | Int16Array | Uint32Array | Int32Array;\n\n/** Cast u8 / u16 / u32 to u8. */\nexport function u8(arr: TypedArray): Uint8Array {\n  return new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n\n/** Cast u8 / u16 / u32 to u32. */\nexport function u32(arr: TypedArray): Uint32Array {\n  return new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n}\n\n/** Zeroize a byte array. Warning: JS provides no guarantees. */\nexport function clean(...arrays: TypedArray[]): void {\n  for (let i = 0; i < arrays.length; i++) {\n    arrays[i].fill(0);\n  }\n}\n\n/** Create DataView of an array for easy byte-level manipulation. */\nexport function createView(arr: TypedArray): DataView {\n  return new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n}\n\n/** The rotate right (circular right shift) operation for uint32 */\nexport function rotr(word: number, shift: number): number {\n  return (word << (32 - shift)) | (word >>> shift);\n}\n\n/** The rotate left (circular left shift) operation for uint32 */\nexport function rotl(word: number, shift: number): number {\n  return (word << shift) | ((word >>> (32 - shift)) >>> 0);\n}\n\n/** Is current platform little-endian? Most are. Big-Endian platform: IBM */\nexport const isLE: boolean = /* @__PURE__ */ (() =>\n  new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44)();\n\n/** The byte swap operation for uint32 */\nexport function byteSwap(word: number): number {\n  return (\n    ((word << 24) & 0xff000000) |\n    ((word << 8) & 0xff0000) |\n    ((word >>> 8) & 0xff00) |\n    ((word >>> 24) & 0xff)\n  );\n}\n/** Conditionally byte swap if on a big-endian platform */\nexport const swap8IfBE: (n: number) => number = isLE\n  ? (n: number) => n\n  : (n: number) => byteSwap(n);\n\n/** @deprecated */\nexport const byteSwapIfBE: typeof swap8IfBE = swap8IfBE;\n/** In place byte swap for Uint32Array */\nexport function byteSwap32(arr: Uint32Array): Uint32Array {\n  for (let i = 0; i < arr.length; i++) {\n    arr[i] = byteSwap(arr[i]);\n  }\n  return arr;\n}\n\nexport const swap32IfBE: (u: Uint32Array) => Uint32Array = isLE\n  ? (u: Uint32Array) => u\n  : byteSwap32;\n\n// Built-in hex conversion https://caniuse.com/mdn-javascript_builtins_uint8array_fromhex\nconst hasHexBuiltin: boolean = /* @__PURE__ */ (() =>\n  // @ts-ignore\n  typeof Uint8Array.from([]).toHex === 'function' && typeof Uint8Array.fromHex === 'function')();\n\n// Array where index 0xf0 (240) is mapped to string 'f0'\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) =>\n  i.toString(16).padStart(2, '0')\n);\n\n/**\n * Convert byte array to hex string. Uses built-in function, when available.\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes: Uint8Array): string {\n  abytes(bytes);\n  // @ts-ignore\n  if (hasHexBuiltin) return bytes.toHex();\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n\n// We use optimized technique to convert hex string to byte array\nconst asciis = { _0: 48, _9: 57, A: 65, F: 70, a: 97, f: 102 } as const;\nfunction asciiToBase16(ch: number): number | undefined {\n  if (ch >= asciis._0 && ch <= asciis._9) return ch - asciis._0; // '2' => 50-48\n  if (ch >= asciis.A && ch <= asciis.F) return ch - (asciis.A - 10); // 'B' => 66-(65-10)\n  if (ch >= asciis.a && ch <= asciis.f) return ch - (asciis.a - 10); // 'b' => 98-(97-10)\n  return;\n}\n\n/**\n * Convert hex string to byte array. Uses built-in function, when available.\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex: string): Uint8Array {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  // @ts-ignore\n  if (hasHexBuiltin) return Uint8Array.fromHex(hex);\n  const hl = hex.length;\n  const al = hl / 2;\n  if (hl % 2) throw new Error('hex string expected, got unpadded hex of length ' + hl);\n  const array = new Uint8Array(al);\n  for (let ai = 0, hi = 0; ai < al; ai++, hi += 2) {\n    const n1 = asciiToBase16(hex.charCodeAt(hi));\n    const n2 = asciiToBase16(hex.charCodeAt(hi + 1));\n    if (n1 === undefined || n2 === undefined) {\n      const char = hex[hi] + hex[hi + 1];\n      throw new Error('hex string expected, got non-hex character \"' + char + '\" at index ' + hi);\n    }\n    array[ai] = n1 * 16 + n2; // multiply first octet, e.g. 'a3' => 10*16+3 => 160 + 3 => 163\n  }\n  return array;\n}\n\n/**\n * There is no setImmediate in browser and setTimeout is slow.\n * Call of async fn will return Promise, which will be fullfiled only on\n * next scheduler queue processing step and this is exactly what we need.\n */\nexport const nextTick = async (): Promise<void> => {};\n\n/** Returns control to thread each 'tick' ms to avoid blocking. */\nexport async function asyncLoop(\n  iters: number,\n  tick: number,\n  cb: (i: number) => void\n): Promise<void> {\n  let ts = Date.now();\n  for (let i = 0; i < iters; i++) {\n    cb(i);\n    // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n    const diff = Date.now() - ts;\n    if (diff >= 0 && diff < tick) continue;\n    await nextTick();\n    ts += diff;\n  }\n}\n\n// Global symbols, but ts doesn't see them: https://github.com/microsoft/TypeScript/issues/31535\ndeclare const TextEncoder: any;\ndeclare const TextDecoder: any;\n\n/**\n * Converts string to bytes using UTF8 encoding.\n * @example utf8ToBytes('abc') // Uint8Array.from([97, 98, 99])\n */\nexport function utf8ToBytes(str: string): Uint8Array {\n  if (typeof str !== 'string') throw new Error('string expected');\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n\n/**\n * Converts bytes to string using UTF8 encoding.\n * @example bytesToUtf8(Uint8Array.from([97, 98, 99])) // 'abc'\n */\nexport function bytesToUtf8(bytes: Uint8Array): string {\n  return new TextDecoder().decode(bytes);\n}\n\n/** Accepted input of hash functions. Strings are converted to byte arrays. */\nexport type Input = string | Uint8Array;\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data: Input): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n\n/** KDFs can accept string or Uint8Array for user convenience. */\nexport type KDFInput = string | Uint8Array;\n/**\n * Helper for KDFs: consumes uint8array or string.\n * When string is passed, does utf8 decoding, using TextDecoder.\n */\nexport function kdfInputToBytes(data: KDFInput): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  abytes(data);\n  return data;\n}\n\n/** Copies several Uint8Arrays into one. */\nexport function concatBytes(...arrays: Uint8Array[]): Uint8Array {\n  let sum = 0;\n  for (let i = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    abytes(a);\n    sum += a.length;\n  }\n  const res = new Uint8Array(sum);\n  for (let i = 0, pad = 0; i < arrays.length; i++) {\n    const a = arrays[i];\n    res.set(a, pad);\n    pad += a.length;\n  }\n  return res;\n}\n\ntype EmptyObj = {};\nexport function checkOpts<T1 extends EmptyObj, T2 extends EmptyObj>(\n  defaults: T1,\n  opts?: T2\n): T1 & T2 {\n  if (opts !== undefined && {}.toString.call(opts) !== '[object Object]')\n    throw new Error('options should be object or undefined');\n  const merged = Object.assign(defaults, opts);\n  return merged as T1 & T2;\n}\n\n/** Hash interface. */\nexport type IHash = {\n  (data: Uint8Array): Uint8Array;\n  blockLen: number;\n  outputLen: number;\n  create: any;\n};\n\n/** For runtime check if class implements interface */\nexport abstract class Hash<T extends Hash<T>> {\n  abstract blockLen: number; // Bytes per block\n  abstract outputLen: number; // Bytes in output\n  abstract update(buf: Input): this;\n  // Writes digest into buf\n  abstract digestInto(buf: Uint8Array): void;\n  abstract digest(): Uint8Array;\n  /**\n   * Resets internal state. Makes Hash instance unusable.\n   * Reset is impossible for keyed hashes if key is consumed into state. If digest is not consumed\n   * by user, they will need to manually call `destroy()` when zeroing is necessary.\n   */\n  abstract destroy(): void;\n  /**\n   * Clones hash instance. Unsafe: doesn't check whether `to` is valid. Can be used as `clone()`\n   * when no options are passed.\n   * Reasons to use `_cloneInto` instead of clone: 1) performance 2) reuse instance => all internal\n   * buffers are overwritten => causes buffer overwrite which is used for digest in some cases.\n   * There are no guarantees for clean-up because it's impossible in JS.\n   */\n  abstract _cloneInto(to?: T): T;\n  // Safe version that clones internal state\n  abstract clone(): T;\n}\n\n/**\n * XOF: streaming API to read digest in chunks.\n * Same as 'squeeze' in keccak/k12 and 'seek' in blake3, but more generic name.\n * When hash used in XOF mode it is up to user to call '.destroy' afterwards, since we cannot\n * destroy state, next call can require more bytes.\n */\nexport type HashXOF<T extends Hash<T>> = Hash<T> & {\n  xof(bytes: number): Uint8Array; // Read 'bytes' bytes from digest stream\n  xofInto(buf: Uint8Array): Uint8Array; // read buf.length bytes from digest stream into buf\n};\n\n/** Hash function */\nexport type CHash = ReturnType<typeof createHasher>;\n/** Hash function with output */\nexport type CHashO = ReturnType<typeof createOptHasher>;\n/** XOF with output */\nexport type CHashXO = ReturnType<typeof createXOFer>;\n\n/** Wraps hash function, creating an interface on top of it */\nexport function createHasher<T extends Hash<T>>(\n  hashCons: () => Hash<T>\n): {\n  (msg: Input): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(): Hash<T>;\n} {\n  const hashC = (msg: Input): Uint8Array => hashCons().update(toBytes(msg)).digest();\n  const tmp = hashCons();\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = () => hashCons();\n  return hashC;\n}\n\nexport function createOptHasher<H extends Hash<H>, T extends Object>(\n  hashCons: (opts?: T) => Hash<H>\n): {\n  (msg: Input, opts?: T): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(opts?: T): Hash<H>;\n} {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts?: T) => hashCons(opts);\n  return hashC;\n}\n\nexport function createXOFer<H extends HashXOF<H>, T extends Object>(\n  hashCons: (opts?: T) => HashXOF<H>\n): {\n  (msg: Input, opts?: T): Uint8Array;\n  outputLen: number;\n  blockLen: number;\n  create(opts?: T): HashXOF<H>;\n} {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts?: T) => hashCons(opts);\n  return hashC;\n}\nexport const wrapConstructor: typeof createHasher = createHasher;\nexport const wrapConstructorWithOpts: typeof createOptHasher = createOptHasher;\nexport const wrapXOFConstructorWithOpts: typeof createXOFer = createXOFer;\n\n/** Cryptographically secure PRNG. Uses internal OS-level `crypto.getRandomValues`. */\nexport function randomBytes(bytesLength = 32): Uint8Array {\n  if (crypto && typeof crypto.getRandomValues === 'function') {\n    return crypto.getRandomValues(new Uint8Array(bytesLength));\n  }\n  // Legacy Node.js compatibility\n  if (crypto && typeof crypto.randomBytes === 'function') {\n    return Uint8Array.from(crypto.randomBytes(bytesLength));\n  }\n  throw new Error('crypto.getRandomValues must be defined');\n}\n", "/**\n * Internal Merkle-<PERSON>gard hash utils.\n * @module\n */\nimport { type Input, Hash, abytes, aexists, aoutput, clean, createView, toBytes } from './utils.ts';\n\n/** Polyfill for Safari 14. https://caniuse.com/mdn-javascript_builtins_dataview_setbiguint64 */\nexport function setBigUint64(\n  view: DataView,\n  byteOffset: number,\n  value: bigint,\n  isLE: boolean\n): void {\n  if (typeof view.setBigUint64 === 'function') return view.setBigUint64(byteOffset, value, isLE);\n  const _32n = BigInt(32);\n  const _u32_max = BigInt(0xffffffff);\n  const wh = Number((value >> _32n) & _u32_max);\n  const wl = Number(value & _u32_max);\n  const h = isLE ? 4 : 0;\n  const l = isLE ? 0 : 4;\n  view.setUint32(byteOffset + h, wh, isLE);\n  view.setUint32(byteOffset + l, wl, isLE);\n}\n\n/** Choice: a ? b : c */\nexport function Chi(a: number, b: number, c: number): number {\n  return (a & b) ^ (~a & c);\n}\n\n/** Majority function, true if any two inputs is true. */\nexport function Maj(a: number, b: number, c: number): number {\n  return (a & b) ^ (a & c) ^ (b & c);\n}\n\n/**\n * Merkle-Damgard hash construction base class.\n * Could be used to create MD5, RIPEMD, SHA1, SHA2.\n */\nexport abstract class HashMD<T extends HashMD<T>> extends Hash<T> {\n  protected abstract process(buf: DataView, offset: number): void;\n  protected abstract get(): number[];\n  protected abstract set(...args: number[]): void;\n  abstract destroy(): void;\n  protected abstract roundClean(): void;\n\n  readonly blockLen: number;\n  readonly outputLen: number;\n  readonly padOffset: number;\n  readonly isLE: boolean;\n\n  // For partial updates less than block size\n  protected buffer: Uint8Array;\n  protected view: DataView;\n  protected finished = false;\n  protected length = 0;\n  protected pos = 0;\n  protected destroyed = false;\n\n  constructor(blockLen: number, outputLen: number, padOffset: number, isLE: boolean) {\n    super();\n    this.blockLen = blockLen;\n    this.outputLen = outputLen;\n    this.padOffset = padOffset;\n    this.isLE = isLE;\n    this.buffer = new Uint8Array(blockLen);\n    this.view = createView(this.buffer);\n  }\n  update(data: Input): this {\n    aexists(this);\n    data = toBytes(data);\n    abytes(data);\n    const { view, buffer, blockLen } = this;\n    const len = data.length;\n    for (let pos = 0; pos < len; ) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      // Fast path: we have at least one block in input, cast it to view and process\n      if (take === blockLen) {\n        const dataView = createView(data);\n        for (; blockLen <= len - pos; pos += blockLen) this.process(dataView, pos);\n        continue;\n      }\n      buffer.set(data.subarray(pos, pos + take), this.pos);\n      this.pos += take;\n      pos += take;\n      if (this.pos === blockLen) {\n        this.process(view, 0);\n        this.pos = 0;\n      }\n    }\n    this.length += data.length;\n    this.roundClean();\n    return this;\n  }\n  digestInto(out: Uint8Array): void {\n    aexists(this);\n    aoutput(out, this);\n    this.finished = true;\n    // Padding\n    // We can avoid allocation of buffer for padding completely if it\n    // was previously not allocated here. But it won't change performance.\n    const { buffer, view, blockLen, isLE } = this;\n    let { pos } = this;\n    // append the bit '1' to the message\n    buffer[pos++] = 0b10000000;\n    clean(this.buffer.subarray(pos));\n    // we have less than padOffset left in buffer, so we cannot put length in\n    // current block, need process it and pad again\n    if (this.padOffset > blockLen - pos) {\n      this.process(view, 0);\n      pos = 0;\n    }\n    // Pad until full block byte with zeros\n    for (let i = pos; i < blockLen; i++) buffer[i] = 0;\n    // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n    // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n    // So we just write lowest 64 bits of that value.\n    setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n    this.process(view, 0);\n    const oview = createView(out);\n    const len = this.outputLen;\n    // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n    if (len % 4) throw new Error('_sha2: outputLen should be aligned to 32bit');\n    const outLen = len / 4;\n    const state = this.get();\n    if (outLen > state.length) throw new Error('_sha2: outputLen bigger than state');\n    for (let i = 0; i < outLen; i++) oview.setUint32(4 * i, state[i], isLE);\n  }\n  digest(): Uint8Array {\n    const { buffer, outputLen } = this;\n    this.digestInto(buffer);\n    const res = buffer.slice(0, outputLen);\n    this.destroy();\n    return res;\n  }\n  _cloneInto(to?: T): T {\n    to ||= new (this.constructor as any)() as T;\n    to.set(...this.get());\n    const { blockLen, buffer, length, finished, destroyed, pos } = this;\n    to.destroyed = destroyed;\n    to.finished = finished;\n    to.length = length;\n    to.pos = pos;\n    if (length % blockLen) to.buffer.set(buffer);\n    return to;\n  }\n  clone(): T {\n    return this._cloneInto();\n  }\n}\n\n/**\n * Initial SHA-2 state: fractional parts of square roots of first 16 primes 2..53.\n * Check out `test/misc/sha2-gen-iv.js` for recomputation guide.\n */\n\n/** Initial SHA256 state. Bits 0..32 of frac part of sqrt of primes 2..19 */\nexport const SHA256_IV: Uint32Array = /* @__PURE__ */ Uint32Array.from([\n  0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19,\n]);\n\n/** Initial SHA224 state. Bits 32..64 of frac part of sqrt of primes 23..53 */\nexport const SHA224_IV: Uint32Array = /* @__PURE__ */ Uint32Array.from([\n  0xc1059ed8, 0x367cd507, 0x3070dd17, 0xf70e5939, 0xffc00b31, 0x68581511, 0x64f98fa7, 0xbefa4fa4,\n]);\n\n/** Initial SHA384 state. Bits 0..64 of frac part of sqrt of primes 23..53 */\nexport const SHA384_IV: Uint32Array = /* @__PURE__ */ Uint32Array.from([\n  0xcbbb9d5d, 0xc1059ed8, 0x629a292a, 0x367cd507, 0x9159015a, 0x3070dd17, 0x152fecd8, 0xf70e5939,\n  0x67332667, 0xffc00b31, 0x8eb44a87, 0x68581511, 0xdb0c2e0d, 0x64f98fa7, 0x47b5481d, 0xbefa4fa4,\n]);\n\n/** Initial SHA512 state. Bits 0..64 of frac part of sqrt of primes 2..19 */\nexport const SHA512_IV: Uint32Array = /* @__PURE__ */ Uint32Array.from([\n  0x6a09e667, 0xf3bcc908, 0xbb67ae85, 0x84caa73b, 0x3c6ef372, 0xfe94f82b, 0xa54ff53a, 0x5f1d36f1,\n  0x510e527f, 0xade682d1, 0x9b05688c, 0x2b3e6c1f, 0x1f83d9ab, 0xfb41bd6b, 0x5be0cd19, 0x137e2179,\n]);\n", "/**\n * Internal helpers for u64. BigUint64Array is too slow as per 2025, so we implement it using Uint32Array.\n * @todo re-check https://issues.chromium.org/issues/42212588\n * @module\n */\nconst U32_MASK64 = /* @__PURE__ */ BigInt(2 ** 32 - 1);\nconst _32n = /* @__PURE__ */ BigInt(32);\n\nfunction fromBig(\n  n: bigint,\n  le = false\n): {\n  h: number;\n  l: number;\n} {\n  if (le) return { h: Number(n & U32_MASK64), l: Number((n >> _32n) & U32_MASK64) };\n  return { h: Number((n >> _32n) & U32_MASK64) | 0, l: Number(n & U32_MASK64) | 0 };\n}\n\nfunction split(lst: bigint[], le = false): Uint32Array[] {\n  const len = lst.length;\n  let Ah = new Uint32Array(len);\n  let Al = new Uint32Array(len);\n  for (let i = 0; i < len; i++) {\n    const { h, l } = fromBig(lst[i], le);\n    [Ah[i], Al[i]] = [h, l];\n  }\n  return [Ah, Al];\n}\n\nconst toBig = (h: number, l: number): bigint => (BigInt(h >>> 0) << _32n) | BigInt(l >>> 0);\n// for Shift in [0, 32)\nconst shrSH = (h: number, _l: number, s: number): number => h >>> s;\nconst shrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in [1, 32)\nconst rotrSH = (h: number, l: number, s: number): number => (h >>> s) | (l << (32 - s));\nconst rotrSL = (h: number, l: number, s: number): number => (h << (32 - s)) | (l >>> s);\n// Right rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotrBH = (h: number, l: number, s: number): number => (h << (64 - s)) | (l >>> (s - 32));\nconst rotrBL = (h: number, l: number, s: number): number => (h >>> (s - 32)) | (l << (64 - s));\n// Right rotate for shift===32 (just swaps l&h)\nconst rotr32H = (_h: number, l: number): number => l;\nconst rotr32L = (h: number, _l: number): number => h;\n// Left rotate for Shift in [1, 32)\nconst rotlSH = (h: number, l: number, s: number): number => (h << s) | (l >>> (32 - s));\nconst rotlSL = (h: number, l: number, s: number): number => (l << s) | (h >>> (32 - s));\n// Left rotate for Shift in (32, 64), NOTE: 32 is special case.\nconst rotlBH = (h: number, l: number, s: number): number => (l << (s - 32)) | (h >>> (64 - s));\nconst rotlBL = (h: number, l: number, s: number): number => (h << (s - 32)) | (l >>> (64 - s));\n\n// JS uses 32-bit signed integers for bitwise operations which means we cannot\n// simple take carry out of low bit sum by shift, we need to use division.\nfunction add(\n  Ah: number,\n  Al: number,\n  Bh: number,\n  Bl: number\n): {\n  h: number;\n  l: number;\n} {\n  const l = (Al >>> 0) + (Bl >>> 0);\n  return { h: (Ah + Bh + ((l / 2 ** 32) | 0)) | 0, l: l | 0 };\n}\n// Addition with more than 2 elements\nconst add3L = (Al: number, Bl: number, Cl: number): number => (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0);\nconst add3H = (low: number, Ah: number, Bh: number, Ch: number): number =>\n  (Ah + Bh + Ch + ((low / 2 ** 32) | 0)) | 0;\nconst add4L = (Al: number, Bl: number, Cl: number, Dl: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0);\nconst add4H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number): number =>\n  (Ah + Bh + Ch + Dh + ((low / 2 ** 32) | 0)) | 0;\nconst add5L = (Al: number, Bl: number, Cl: number, Dl: number, El: number): number =>\n  (Al >>> 0) + (Bl >>> 0) + (Cl >>> 0) + (Dl >>> 0) + (El >>> 0);\nconst add5H = (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number): number =>\n  (Ah + Bh + Ch + Dh + Eh + ((low / 2 ** 32) | 0)) | 0;\n\n// prettier-ignore\nexport {\n  add, add3H, add3L, add4H, add4L, add5H, add5L, fromBig, rotlBH, rotlBL, rotlSH, rotlSL, rotr32H, rotr32L, rotrBH, rotrBL, rotrSH, rotrSL, shrSH, shrSL, split, toBig\n};\n// prettier-ignore\nconst u64: { fromBig: typeof fromBig; split: typeof split; toBig: (h: number, l: number) => bigint; shrSH: (h: number, _l: number, s: number) => number; shrSL: (h: number, l: number, s: number) => number; rotrSH: (h: number, l: number, s: number) => number; rotrSL: (h: number, l: number, s: number) => number; rotrBH: (h: number, l: number, s: number) => number; rotrBL: (h: number, l: number, s: number) => number; rotr32H: (_h: number, l: number) => number; rotr32L: (h: number, _l: number) => number; rotlSH: (h: number, l: number, s: number) => number; rotlSL: (h: number, l: number, s: number) => number; rotlBH: (h: number, l: number, s: number) => number; rotlBL: (h: number, l: number, s: number) => number; add: typeof add; add3L: (Al: number, Bl: number, Cl: number) => number; add3H: (low: number, Ah: number, Bh: number, Ch: number) => number; add4L: (Al: number, Bl: number, Cl: number, Dl: number) => number; add4H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number) => number; add5H: (low: number, Ah: number, Bh: number, Ch: number, Dh: number, Eh: number) => number; add5L: (Al: number, Bl: number, Cl: number, Dl: number, El: number) => number; } = {\n  fromBig, split, toBig,\n  shrSH, shrSL,\n  rotrSH, rotrSL, rotrBH, rotrBL,\n  rotr32H, rotr32L,\n  rotlSH, rotlSL, rotlBH, rotlBL,\n  add, add3L, add3H, add4L, add4H, add5H, add5L,\n};\nexport default u64;\n", "/**\n * SHA2 hash function. A.k.a. sha256, sha384, sha512, sha512_224, sha512_256.\n * SHA256 is the fastest hash implementable in JS, even faster than Blake3.\n * Check out [RFC 4634](https://datatracker.ietf.org/doc/html/rfc4634) and\n * [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n */\nimport { <PERSON>, HashMD, Maj, SHA224_IV, SHA256_IV, SHA384_IV, SHA512_IV } from './_md.ts';\nimport * as u64 from './_u64.ts';\nimport { type CHash, clean, createHasher, rotr } from './utils.ts';\n\n/**\n * Round constants:\n * First 32 bits of fractional parts of the cube roots of the first 64 primes 2..311)\n */\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */ Uint32Array.from([\n  0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n  0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n  0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n  0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n  0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n  0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n  0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n  0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n\n/** Reusable temporary buffer. \"W\" comes straight from spec. */\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nexport class SHA256 extends HashMD<SHA256> {\n  // We cannot use array here since array allows indexing by variable\n  // which means optimizer/compiler cannot use registers.\n  protected A: number = SHA256_IV[0] | 0;\n  protected B: number = SHA256_IV[1] | 0;\n  protected C: number = SHA256_IV[2] | 0;\n  protected D: number = SHA256_IV[3] | 0;\n  protected E: number = SHA256_IV[4] | 0;\n  protected F: number = SHA256_IV[5] | 0;\n  protected G: number = SHA256_IV[6] | 0;\n  protected H: number = SHA256_IV[7] | 0;\n\n  constructor(outputLen: number = 32) {\n    super(64, outputLen, 8, false);\n  }\n  protected get(): [number, number, number, number, number, number, number, number] {\n    const { A, B, C, D, E, F, G, H } = this;\n    return [A, B, C, D, E, F, G, H];\n  }\n  // prettier-ignore\n  protected set(\n    A: number, B: number, C: number, D: number, E: number, F: number, G: number, H: number\n  ): void {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n    this.E = E | 0;\n    this.F = F | 0;\n    this.G = G | 0;\n    this.H = H | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) SHA256_W[i] = view.getUint32(offset, false);\n    for (let i = 16; i < 64; i++) {\n      const W15 = SHA256_W[i - 15];\n      const W2 = SHA256_W[i - 2];\n      const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n      const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n      SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n    }\n    // Compression function main loop, 64 rounds\n    let { A, B, C, D, E, F, G, H } = this;\n    for (let i = 0; i < 64; i++) {\n      const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n      const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n      const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n      const T2 = (sigma0 + Maj(A, B, C)) | 0;\n      H = G;\n      G = F;\n      F = E;\n      E = (D + T1) | 0;\n      D = C;\n      C = B;\n      B = A;\n      A = (T1 + T2) | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    A = (A + this.A) | 0;\n    B = (B + this.B) | 0;\n    C = (C + this.C) | 0;\n    D = (D + this.D) | 0;\n    E = (E + this.E) | 0;\n    F = (F + this.F) | 0;\n    G = (G + this.G) | 0;\n    H = (H + this.H) | 0;\n    this.set(A, B, C, D, E, F, G, H);\n  }\n  protected roundClean(): void {\n    clean(SHA256_W);\n  }\n  destroy(): void {\n    this.set(0, 0, 0, 0, 0, 0, 0, 0);\n    clean(this.buffer);\n  }\n}\n\nexport class SHA224 extends SHA256 {\n  protected A: number = SHA224_IV[0] | 0;\n  protected B: number = SHA224_IV[1] | 0;\n  protected C: number = SHA224_IV[2] | 0;\n  protected D: number = SHA224_IV[3] | 0;\n  protected E: number = SHA224_IV[4] | 0;\n  protected F: number = SHA224_IV[5] | 0;\n  protected G: number = SHA224_IV[6] | 0;\n  protected H: number = SHA224_IV[7] | 0;\n  constructor() {\n    super(28);\n  }\n}\n\n// SHA2-512 is slower than sha256 in js because u64 operations are slow.\n\n// Round contants\n// First 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409\n// prettier-ignore\nconst K512 = /* @__PURE__ */ (() => u64.split([\n  '0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc',\n  '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118',\n  '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2',\n  '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694',\n  '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65',\n  '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5',\n  '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4',\n  '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70',\n  '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df',\n  '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b',\n  '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30',\n  '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8',\n  '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8',\n  '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3',\n  '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec',\n  '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b',\n  '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178',\n  '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b',\n  '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c',\n  '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'\n].map(n => BigInt(n))))();\nconst SHA512_Kh = /* @__PURE__ */ (() => K512[0])();\nconst SHA512_Kl = /* @__PURE__ */ (() => K512[1])();\n\n// Reusable temporary buffers\nconst SHA512_W_H = /* @__PURE__ */ new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */ new Uint32Array(80);\n\nexport class SHA512 extends HashMD<SHA512> {\n  // We cannot use array here since array allows indexing by variable\n  // which means optimizer/compiler cannot use registers.\n  // h -- high 32 bits, l -- low 32 bits\n  protected Ah: number = SHA512_IV[0] | 0;\n  protected Al: number = SHA512_IV[1] | 0;\n  protected Bh: number = SHA512_IV[2] | 0;\n  protected Bl: number = SHA512_IV[3] | 0;\n  protected Ch: number = SHA512_IV[4] | 0;\n  protected Cl: number = SHA512_IV[5] | 0;\n  protected Dh: number = SHA512_IV[6] | 0;\n  protected Dl: number = SHA512_IV[7] | 0;\n  protected Eh: number = SHA512_IV[8] | 0;\n  protected El: number = SHA512_IV[9] | 0;\n  protected Fh: number = SHA512_IV[10] | 0;\n  protected Fl: number = SHA512_IV[11] | 0;\n  protected Gh: number = SHA512_IV[12] | 0;\n  protected Gl: number = SHA512_IV[13] | 0;\n  protected Hh: number = SHA512_IV[14] | 0;\n  protected Hl: number = SHA512_IV[15] | 0;\n\n  constructor(outputLen: number = 64) {\n    super(128, outputLen, 16, false);\n  }\n  // prettier-ignore\n  protected get(): [\n    number, number, number, number, number, number, number, number,\n    number, number, number, number, number, number, number, number\n  ] {\n    const { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n    return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n  }\n  // prettier-ignore\n  protected set(\n    Ah: number, Al: number, Bh: number, Bl: number, Ch: number, Cl: number, Dh: number, Dl: number,\n    Eh: number, El: number, Fh: number, Fl: number, Gh: number, Gl: number, Hh: number, Hl: number\n  ): void {\n    this.Ah = Ah | 0;\n    this.Al = Al | 0;\n    this.Bh = Bh | 0;\n    this.Bl = Bl | 0;\n    this.Ch = Ch | 0;\n    this.Cl = Cl | 0;\n    this.Dh = Dh | 0;\n    this.Dl = Dl | 0;\n    this.Eh = Eh | 0;\n    this.El = El | 0;\n    this.Fh = Fh | 0;\n    this.Fl = Fl | 0;\n    this.Gh = Gh | 0;\n    this.Gl = Gl | 0;\n    this.Hh = Hh | 0;\n    this.Hl = Hl | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) {\n      SHA512_W_H[i] = view.getUint32(offset);\n      SHA512_W_L[i] = view.getUint32((offset += 4));\n    }\n    for (let i = 16; i < 80; i++) {\n      // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n      const W15h = SHA512_W_H[i - 15] | 0;\n      const W15l = SHA512_W_L[i - 15] | 0;\n      const s0h = u64.rotrSH(W15h, W15l, 1) ^ u64.rotrSH(W15h, W15l, 8) ^ u64.shrSH(W15h, W15l, 7);\n      const s0l = u64.rotrSL(W15h, W15l, 1) ^ u64.rotrSL(W15h, W15l, 8) ^ u64.shrSL(W15h, W15l, 7);\n      // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n      const W2h = SHA512_W_H[i - 2] | 0;\n      const W2l = SHA512_W_L[i - 2] | 0;\n      const s1h = u64.rotrSH(W2h, W2l, 19) ^ u64.rotrBH(W2h, W2l, 61) ^ u64.shrSH(W2h, W2l, 6);\n      const s1l = u64.rotrSL(W2h, W2l, 19) ^ u64.rotrBL(W2h, W2l, 61) ^ u64.shrSL(W2h, W2l, 6);\n      // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n      const SUMl = u64.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n      const SUMh = u64.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n      SHA512_W_H[i] = SUMh | 0;\n      SHA512_W_L[i] = SUMl | 0;\n    }\n    let { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n    // Compression function main loop, 80 rounds\n    for (let i = 0; i < 80; i++) {\n      // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n      const sigma1h = u64.rotrSH(Eh, El, 14) ^ u64.rotrSH(Eh, El, 18) ^ u64.rotrBH(Eh, El, 41);\n      const sigma1l = u64.rotrSL(Eh, El, 14) ^ u64.rotrSL(Eh, El, 18) ^ u64.rotrBL(Eh, El, 41);\n      //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n      const CHIh = (Eh & Fh) ^ (~Eh & Gh);\n      const CHIl = (El & Fl) ^ (~El & Gl);\n      // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n      // prettier-ignore\n      const T1ll = u64.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n      const T1h = u64.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n      const T1l = T1ll | 0;\n      // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n      const sigma0h = u64.rotrSH(Ah, Al, 28) ^ u64.rotrBH(Ah, Al, 34) ^ u64.rotrBH(Ah, Al, 39);\n      const sigma0l = u64.rotrSL(Ah, Al, 28) ^ u64.rotrBL(Ah, Al, 34) ^ u64.rotrBL(Ah, Al, 39);\n      const MAJh = (Ah & Bh) ^ (Ah & Ch) ^ (Bh & Ch);\n      const MAJl = (Al & Bl) ^ (Al & Cl) ^ (Bl & Cl);\n      Hh = Gh | 0;\n      Hl = Gl | 0;\n      Gh = Fh | 0;\n      Gl = Fl | 0;\n      Fh = Eh | 0;\n      Fl = El | 0;\n      ({ h: Eh, l: El } = u64.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n      Dh = Ch | 0;\n      Dl = Cl | 0;\n      Ch = Bh | 0;\n      Cl = Bl | 0;\n      Bh = Ah | 0;\n      Bl = Al | 0;\n      const All = u64.add3L(T1l, sigma0l, MAJl);\n      Ah = u64.add3H(All, T1h, sigma0h, MAJh);\n      Al = All | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    ({ h: Ah, l: Al } = u64.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n    ({ h: Bh, l: Bl } = u64.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n    ({ h: Ch, l: Cl } = u64.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n    ({ h: Dh, l: Dl } = u64.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n    ({ h: Eh, l: El } = u64.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n    ({ h: Fh, l: Fl } = u64.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n    ({ h: Gh, l: Gl } = u64.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n    ({ h: Hh, l: Hl } = u64.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n    this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n  }\n  protected roundClean(): void {\n    clean(SHA512_W_H, SHA512_W_L);\n  }\n  destroy(): void {\n    clean(this.buffer);\n    this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n  }\n}\n\nexport class SHA384 extends SHA512 {\n  protected Ah: number = SHA384_IV[0] | 0;\n  protected Al: number = SHA384_IV[1] | 0;\n  protected Bh: number = SHA384_IV[2] | 0;\n  protected Bl: number = SHA384_IV[3] | 0;\n  protected Ch: number = SHA384_IV[4] | 0;\n  protected Cl: number = SHA384_IV[5] | 0;\n  protected Dh: number = SHA384_IV[6] | 0;\n  protected Dl: number = SHA384_IV[7] | 0;\n  protected Eh: number = SHA384_IV[8] | 0;\n  protected El: number = SHA384_IV[9] | 0;\n  protected Fh: number = SHA384_IV[10] | 0;\n  protected Fl: number = SHA384_IV[11] | 0;\n  protected Gh: number = SHA384_IV[12] | 0;\n  protected Gl: number = SHA384_IV[13] | 0;\n  protected Hh: number = SHA384_IV[14] | 0;\n  protected Hl: number = SHA384_IV[15] | 0;\n\n  constructor() {\n    super(48);\n  }\n}\n\n/**\n * Truncated SHA512/256 and SHA512/224.\n * SHA512_IV is XORed with 0xa5a5a5a5a5a5a5a5, then used as \"intermediary\" IV of SHA512/t.\n * Then t hashes string to produce result IV.\n * See `test/misc/sha2-gen-iv.js`.\n */\n\n/** SHA512/224 IV */\nconst T224_IV = /* @__PURE__ */ Uint32Array.from([\n  0x8c3d37c8, 0x19544da2, 0x73e19966, 0x89dcd4d6, 0x1dfab7ae, 0x32ff9c82, 0x679dd514, 0x582f9fcf,\n  0x0f6d2b69, 0x7bd44da8, 0x77e36f73, 0x04c48942, 0x3f9d85a8, 0x6a1d36c8, 0x1112e6ad, 0x91d692a1,\n]);\n\n/** SHA512/256 IV */\nconst T256_IV = /* @__PURE__ */ Uint32Array.from([\n  0x22312194, 0xfc2bf72c, 0x9f555fa3, 0xc84c64c2, 0x2393b86b, 0x6f53b151, 0x96387719, 0x5940eabd,\n  0x96283ee2, 0xa88effe3, 0xbe5e1e25, 0x53863992, 0x2b0199fc, 0x2c85b8aa, 0x0eb72ddc, 0x81c52ca2,\n]);\n\nexport class SHA512_224 extends SHA512 {\n  protected Ah: number = T224_IV[0] | 0;\n  protected Al: number = T224_IV[1] | 0;\n  protected Bh: number = T224_IV[2] | 0;\n  protected Bl: number = T224_IV[3] | 0;\n  protected Ch: number = T224_IV[4] | 0;\n  protected Cl: number = T224_IV[5] | 0;\n  protected Dh: number = T224_IV[6] | 0;\n  protected Dl: number = T224_IV[7] | 0;\n  protected Eh: number = T224_IV[8] | 0;\n  protected El: number = T224_IV[9] | 0;\n  protected Fh: number = T224_IV[10] | 0;\n  protected Fl: number = T224_IV[11] | 0;\n  protected Gh: number = T224_IV[12] | 0;\n  protected Gl: number = T224_IV[13] | 0;\n  protected Hh: number = T224_IV[14] | 0;\n  protected Hl: number = T224_IV[15] | 0;\n\n  constructor() {\n    super(28);\n  }\n}\n\nexport class SHA512_256 extends SHA512 {\n  protected Ah: number = T256_IV[0] | 0;\n  protected Al: number = T256_IV[1] | 0;\n  protected Bh: number = T256_IV[2] | 0;\n  protected Bl: number = T256_IV[3] | 0;\n  protected Ch: number = T256_IV[4] | 0;\n  protected Cl: number = T256_IV[5] | 0;\n  protected Dh: number = T256_IV[6] | 0;\n  protected Dl: number = T256_IV[7] | 0;\n  protected Eh: number = T256_IV[8] | 0;\n  protected El: number = T256_IV[9] | 0;\n  protected Fh: number = T256_IV[10] | 0;\n  protected Fl: number = T256_IV[11] | 0;\n  protected Gh: number = T256_IV[12] | 0;\n  protected Gl: number = T256_IV[13] | 0;\n  protected Hh: number = T256_IV[14] | 0;\n  protected Hl: number = T256_IV[15] | 0;\n\n  constructor() {\n    super(32);\n  }\n}\n\n/**\n * SHA2-256 hash function from RFC 4634.\n *\n * It is the fastest JS hash, even faster than Blake3.\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n */\nexport const sha256: CHash = /* @__PURE__ */ createHasher(() => new SHA256());\n/** SHA2-224 hash function from RFC 4634 */\nexport const sha224: CHash = /* @__PURE__ */ createHasher(() => new SHA224());\n\n/** SHA2-512 hash function from RFC 4634. */\nexport const sha512: CHash = /* @__PURE__ */ createHasher(() => new SHA512());\n/** SHA2-384 hash function from RFC 4634. */\nexport const sha384: CHash = /* @__PURE__ */ createHasher(() => new SHA384());\n\n/**\n * SHA2-512/256 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nexport const sha512_256: CHash = /* @__PURE__ */ createHasher(() => new SHA512_256());\n/**\n * SHA2-512/224 \"truncated\" hash function, with improved resistance to length extension attacks.\n * See the paper on [truncated SHA512](https://eprint.iacr.org/2010/548.pdf).\n */\nexport const sha512_224: CHash = /* @__PURE__ */ createHasher(() => new SHA512_224());\n", "/**\n * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake3.\n *\n * To break sha256 using birthday attack, attackers need to try 2^128 hashes.\n * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.\n *\n * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).\n * @module\n * @deprecated\n */\nimport {\n  SHA224 as SHA224n,\n  sha224 as sha224n,\n  SHA256 as SHA256n,\n  sha256 as sha256n,\n} from './sha2.ts';\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const SHA256: typeof SHA256n = SHA256n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const sha256: typeof sha256n = sha256n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const SHA224: typeof SHA224n = SHA224n;\n/** @deprecated Use import from `noble/hashes/sha2` module */\nexport const sha224: typeof sha224n = sha224n;\n", null, null, null, null, "function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f);else for(t in e)e[t]&&(n&&(n+=\" \"),n+=t);return n}export function clsx(){for(var e,t,f=0,n=\"\";f<arguments.length;)(e=arguments[f++])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AA+EE,YAAA,MAAAA;AAA+C,YAAA,UAAAC;AAAyG,YAAA,QAAAC;AA1E1J,QAAMC,cAA6B,OAAO,KAAK,KAAK,CAAC;AACrD,QAAMC,QAAuB,OAAO,EAAE;AAEtC,aAASH,SACP,GACA,KAAK,OAAK;AAKV,UAAI;AAAI,eAAO,EAAE,GAAG,OAAO,IAAIE,WAAU,GAAG,GAAG,OAAQ,KAAKC,QAAQD,WAAU,EAAC;AAC/E,aAAO,EAAE,GAAG,OAAQ,KAAKC,QAAQD,WAAU,IAAI,GAAG,GAAG,OAAO,IAAIA,WAAU,IAAI,EAAC;IACjF;AAEA,aAASD,OAAM,KAAe,KAAK,OAAK;AACtC,YAAM,MAAM,IAAI;AAChB,UAAI,KAAK,IAAI,YAAY,GAAG;AAC5B,UAAI,KAAK,IAAI,YAAY,GAAG;AAC5B,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,cAAM,EAAE,GAAG,EAAC,IAAKD,SAAQ,IAAI,CAAC,GAAG,EAAE;AACnC,SAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;MACxB;AACA,aAAO,CAAC,IAAI,EAAE;IAChB;AAEA,QAAM,QAAQ,CAAC,GAAW,MAAuB,OAAO,MAAM,CAAC,KAAKG,QAAQ,OAAO,MAAM,CAAC;AAiDuE,YAAA,QAAA;AA/CjK,QAAMC,SAAQ,CAAC,GAAW,IAAY,MAAsB,MAAM;AA+C0E,YAAA,QAAAA;AA9C5I,QAAMC,SAAQ,CAAC,GAAW,GAAW,MAAuB,KAAM,KAAK,IAAO,MAAM;AA8C+D,YAAA,QAAAA;AA5CnJ,QAAMC,UAAS,CAAC,GAAW,GAAW,MAAuB,MAAM,IAAM,KAAM,KAAK;AA4CwC,YAAA,SAAAA;AA3C5H,QAAMC,UAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,KAAK,IAAO,MAAM;AA2C+C,YAAA,SAAAA;AAzCpI,QAAMC,UAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,KAAK,IAAO,MAAO,IAAI;AAyCkB,YAAA,SAAAA;AAxC5G,QAAMC,UAAS,CAAC,GAAW,GAAW,MAAuB,MAAO,IAAI,KAAQ,KAAM,KAAK;AAwCyB,YAAA,SAAAA;AAtCpH,QAAM,UAAU,CAAC,IAAY,MAAsB;AAsCuC,YAAA,UAAA;AArC1F,QAAM,UAAU,CAAC,GAAW,OAAuB;AAqCgD,YAAA,UAAA;AAnCnG,QAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAK,IAAM,MAAO,KAAK;AAmCV,YAAA,SAAA;AAlC1E,QAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAK,IAAM,MAAO,KAAK;AAkCF,YAAA,SAAA;AAhClF,QAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,IAAI,KAAQ,MAAO,KAAK;AAgCjC,YAAA,SAAA;AA/B1D,QAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,IAAI,KAAQ,MAAO,KAAK;AA+BzB,YAAA,SAAA;AA3BlE,aAASV,KACP,IACA,IACA,IACA,IAAU;AAKV,YAAM,KAAK,OAAO,MAAM,OAAO;AAC/B,aAAO,EAAE,GAAI,KAAK,MAAO,IAAI,KAAK,KAAM,KAAM,GAAG,GAAG,IAAI,EAAC;IAC3D;AAEA,QAAMW,SAAQ,CAAC,IAAY,IAAY,QAAwB,OAAO,MAAM,OAAO,MAAM,OAAO;AAclF,YAAA,QAAAA;AAbd,QAAMC,SAAQ,CAAC,KAAa,IAAY,IAAY,OACjD,KAAK,KAAK,MAAO,MAAM,KAAK,KAAM,KAAM;AAYpC,YAAA,QAAAA;AAXP,QAAMC,SAAQ,CAAC,IAAY,IAAY,IAAY,QAChD,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO;AAUrB,YAAA,QAAAA;AAT5B,QAAMC,SAAQ,CAAC,KAAa,IAAY,IAAY,IAAY,OAC7D,KAAK,KAAK,KAAK,MAAO,MAAM,KAAK,KAAM,KAAM;AAQ3B,YAAA,QAAAA;AAPrB,QAAMC,SAAQ,CAAC,IAAY,IAAY,IAAY,IAAY,QAC5D,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO;AAMpB,YAAA,QAAAA;AAL1C,QAAMC,SAAQ,CAAC,KAAa,IAAY,IAAY,IAAY,IAAY,OACzE,KAAK,KAAK,KAAK,KAAK,MAAO,MAAM,KAAK,KAAM,KAAM;AAIlB,YAAA,QAAAA;AAGnC,QAAM,MAAqpC;MACzpC,SAAAf;MAAS,OAAAC;MAAO;MAChB,OAAAG;MAAO,OAAAC;MACP,QAAAC;MAAQ,QAAAC;MAAQ,QAAAC;MAAQ,QAAAC;MACxB;MAAS;MACT;MAAQ;MAAQ;MAAQ;MACxB,KAAAV;MAAK,OAAAW;MAAO,OAAAC;MAAO,OAAAC;MAAO,OAAAC;MAAO,OAAAE;MAAO,OAAAD;;AAE1C,YAAA,UAAe;;;;;;;;;;ACnFF,YAAA,SACX,OAAO,eAAe,YAAY,YAAY,aAAa,WAAW,SAAS;;;;;;;;;;ACOjF,YAAA,UAAAE;AAKA,YAAA,UAAA;AAKA,YAAA,SAAAC;AAOA,YAAA,QAAA;AAQA,YAAA,UAAAC;AAMA,YAAA,UAAAC;AAcA,YAAA,KAAA;AAKA,YAAA,MAAA;AAKA,YAAA,QAAAC;AAOA,YAAA,aAAAC;AAKA,YAAA,OAAAC;AAKA,YAAA,OAAA;AASA,YAAA,WAAA;AAgBA,YAAA,aAAA;AAyBA,YAAA,aAAAC;AAyBA,YAAA,aAAA;AA4BA,YAAA,YAAA;AAwBA,YAAA,cAAAC;AASA,YAAA,cAAA;AAWA,YAAA,UAAAC;AAYA,YAAA,kBAAA;AAOA,YAAA,cAAA;AAiBA,YAAA,YAAA;AA+DA,YAAA,eAAAC;AAgBA,YAAA,kBAAA;AAgBA,YAAA,cAAA;AAoBA,YAAA,cAAA;AArXA,QAAA,WAAA;AAGA,aAAgBV,SAAQ,GAAU;AAChC,aAAO,aAAa,cAAe,YAAY,OAAO,CAAC,KAAK,EAAE,YAAY,SAAS;IACrF;AAGA,aAAgB,QAAQ,GAAS;AAC/B,UAAI,CAAC,OAAO,cAAc,CAAC,KAAK,IAAI;AAAG,cAAM,IAAI,MAAM,oCAAoC,CAAC;IAC9F;AAGA,aAAgBC,QAAO,MAA8B,SAAiB;AACpE,UAAI,CAACD,SAAQ,CAAC;AAAG,cAAM,IAAI,MAAM,qBAAqB;AACtD,UAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,MAAM;AAClD,cAAM,IAAI,MAAM,mCAAmC,UAAU,kBAAkB,EAAE,MAAM;IAC3F;AAGA,aAAgB,MAAM,GAAQ;AAC5B,UAAI,OAAO,MAAM,cAAc,OAAO,EAAE,WAAW;AACjD,cAAM,IAAI,MAAM,8CAA8C;AAChE,cAAQ,EAAE,SAAS;AACnB,cAAQ,EAAE,QAAQ;IACpB;AAGA,aAAgBE,SAAQ,UAAe,gBAAgB,MAAI;AACzD,UAAI,SAAS;AAAW,cAAM,IAAI,MAAM,kCAAkC;AAC1E,UAAI,iBAAiB,SAAS;AAAU,cAAM,IAAI,MAAM,uCAAuC;IACjG;AAGA,aAAgBC,SAAQ,KAAU,UAAa;AAC7C,MAAAF,QAAO,GAAG;AACV,YAAM,MAAM,SAAS;AACrB,UAAI,IAAI,SAAS,KAAK;AACpB,cAAM,IAAI,MAAM,2DAA2D,GAAG;MAChF;IACF;AAQA,aAAgB,GAAG,KAAe;AAChC,aAAO,IAAI,WAAW,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;IAClE;AAGA,aAAgB,IAAI,KAAe;AACjC,aAAO,IAAI,YAAY,IAAI,QAAQ,IAAI,YAAY,KAAK,MAAM,IAAI,aAAa,CAAC,CAAC;IACnF;AAGA,aAAgBG,UAAS,QAAoB;AAC3C,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,eAAO,CAAC,EAAE,KAAK,CAAC;MAClB;IACF;AAGA,aAAgBC,YAAW,KAAe;AACxC,aAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;IAChE;AAGA,aAAgBC,MAAK,MAAc,OAAa;AAC9C,aAAQ,QAAS,KAAK,QAAW,SAAS;IAC5C;AAGA,aAAgB,KAAK,MAAc,OAAa;AAC9C,aAAQ,QAAQ,QAAW,SAAU,KAAK,UAAY;IACxD;AAGa,YAAA,QAAiC,MAC5C,IAAI,WAAW,IAAI,YAAY,CAAC,SAAU,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,IAAK;AAGnE,aAAgB,SAAS,MAAY;AACnC,aACI,QAAQ,KAAM,aACd,QAAQ,IAAK,WACb,SAAS,IAAK,QACd,SAAS,KAAM;IAErB;AAEa,YAAA,YAAmC,QAAA,OAC5C,CAAC,MAAc,IACf,CAAC,MAAc,SAAS,CAAC;AAGhB,YAAA,eAAiC,QAAA;AAE9C,aAAgB,WAAW,KAAgB;AACzC,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC;MAC1B;AACA,aAAO;IACT;AAEa,YAAA,aAA8C,QAAA,OACvD,CAAC,MAAmB,IACpB;AAGJ,QAAMK,kBAA0C;;MAE9C,OAAO,WAAW,KAAK,CAAA,CAAE,EAAE,UAAU,cAAc,OAAO,WAAW,YAAY;OAAW;AAG9F,QAAMC,SAAwB,MAAM,KAAK,EAAE,QAAQ,IAAG,GAAI,CAACC,IAAG,MAC5D,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AAOjC,aAAgBN,YAAW,OAAiB;AAC1C,MAAAN,QAAO,KAAK;AAEZ,UAAIU;AAAe,eAAO,MAAM,MAAK;AAErC,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,eAAOC,OAAM,MAAM,CAAC,CAAC;MACvB;AACA,aAAO;IACT;AAGA,QAAM,SAAS,EAAE,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAG;AAC5D,aAAS,cAAc,IAAU;AAC/B,UAAI,MAAM,OAAO,MAAM,MAAM,OAAO;AAAI,eAAO,KAAK,OAAO;AAC3D,UAAI,MAAM,OAAO,KAAK,MAAM,OAAO;AAAG,eAAO,MAAM,OAAO,IAAI;AAC9D,UAAI,MAAM,OAAO,KAAK,MAAM,OAAO;AAAG,eAAO,MAAM,OAAO,IAAI;AAC9D;IACF;AAMA,aAAgB,WAAW,KAAW;AACpC,UAAI,OAAO,QAAQ;AAAU,cAAM,IAAI,MAAM,8BAA8B,OAAO,GAAG;AAErF,UAAID;AAAe,eAAO,WAAW,QAAQ,GAAG;AAChD,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,KAAK;AAChB,UAAI,KAAK;AAAG,cAAM,IAAI,MAAM,qDAAqD,EAAE;AACnF,YAAM,QAAQ,IAAI,WAAW,EAAE;AAC/B,eAAS,KAAK,GAAG,KAAK,GAAG,KAAK,IAAI,MAAM,MAAM,GAAG;AAC/C,cAAM,KAAK,cAAc,IAAI,WAAW,EAAE,CAAC;AAC3C,cAAM,KAAK,cAAc,IAAI,WAAW,KAAK,CAAC,CAAC;AAC/C,YAAI,OAAO,UAAa,OAAO,QAAW;AACxC,gBAAM,OAAO,IAAI,EAAE,IAAI,IAAI,KAAK,CAAC;AACjC,gBAAM,IAAI,MAAM,iDAAiD,OAAO,gBAAgB,EAAE;QAC5F;AACA,cAAM,EAAE,IAAI,KAAK,KAAK;MACxB;AACA,aAAO;IACT;AAOO,QAAM,WAAW,YAA0B;IAAE;AAAvC,YAAA,WAAQ;AAGd,mBAAe,UACpB,OACA,MACA,IAAuB;AAEvB,UAAI,KAAK,KAAK,IAAG;AACjB,eAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,WAAG,CAAC;AAEJ,cAAM,OAAO,KAAK,IAAG,IAAK;AAC1B,YAAI,QAAQ,KAAK,OAAO;AAAM;AAC9B,eAAM,GAAA,QAAA,UAAQ;AACd,cAAM;MACR;IACF;AAUA,aAAgBH,aAAY,KAAW;AACrC,UAAI,OAAO,QAAQ;AAAU,cAAM,IAAI,MAAM,iBAAiB;AAC9D,aAAO,IAAI,WAAW,IAAI,YAAW,EAAG,OAAO,GAAG,CAAC;IACrD;AAMA,aAAgB,YAAY,OAAiB;AAC3C,aAAO,IAAI,YAAW,EAAG,OAAO,KAAK;IACvC;AASA,aAAgBC,SAAQ,MAAW;AACjC,UAAI,OAAO,SAAS;AAAU,eAAOD,aAAY,IAAI;AACrD,MAAAP,QAAO,IAAI;AACX,aAAO;IACT;AAQA,aAAgB,gBAAgB,MAAc;AAC5C,UAAI,OAAO,SAAS;AAAU,eAAOO,aAAY,IAAI;AACrD,MAAAP,QAAO,IAAI;AACX,aAAO;IACT;AAGA,aAAgB,eAAe,QAAoB;AACjD,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAM,IAAI,OAAO,CAAC;AAClB,QAAAA,QAAO,CAAC;AACR,eAAO,EAAE;MACX;AACA,YAAM,MAAM,IAAI,WAAW,GAAG;AAC9B,eAAS,IAAI,GAAG,MAAM,GAAG,IAAI,OAAO,QAAQ,KAAK;AAC/C,cAAM,IAAI,OAAO,CAAC;AAClB,YAAI,IAAI,GAAG,GAAG;AACd,eAAO,EAAE;MACX;AACA,aAAO;IACT;AAGA,aAAgB,UACd,UACA,MAAS;AAET,UAAI,SAAS,UAAa,CAAA,EAAG,SAAS,KAAK,IAAI,MAAM;AACnD,cAAM,IAAI,MAAM,uCAAuC;AACzD,YAAM,SAAS,OAAO,OAAO,UAAU,IAAI;AAC3C,aAAO;IACT;AAWA,QAAsBa,QAAtB,MAA0B;;AAA1B,YAAA,OAAAA;AA4CA,aAAgBJ,cACd,UAAuB;AAOvB,YAAM,QAAQ,CAAC,QAA2B,SAAQ,EAAG,OAAOD,SAAQ,GAAG,CAAC,EAAE,OAAM;AAChF,YAAM,MAAM,SAAQ;AACpB,YAAM,YAAY,IAAI;AACtB,YAAM,WAAW,IAAI;AACrB,YAAM,SAAS,MAAM,SAAQ;AAC7B,aAAO;IACT;AAEA,aAAgB,gBACd,UAA+B;AAO/B,YAAM,QAAQ,CAAC,KAAY,SAAyB,SAAS,IAAI,EAAE,OAAOA,SAAQ,GAAG,CAAC,EAAE,OAAM;AAC9F,YAAM,MAAM,SAAS,CAAA,CAAO;AAC5B,YAAM,YAAY,IAAI;AACtB,YAAM,WAAW,IAAI;AACrB,YAAM,SAAS,CAAC,SAAa,SAAS,IAAI;AAC1C,aAAO;IACT;AAEA,aAAgB,YACd,UAAkC;AAOlC,YAAM,QAAQ,CAAC,KAAY,SAAyB,SAAS,IAAI,EAAE,OAAOA,SAAQ,GAAG,CAAC,EAAE,OAAM;AAC9F,YAAM,MAAM,SAAS,CAAA,CAAO;AAC5B,YAAM,YAAY,IAAI;AACtB,YAAM,WAAW,IAAI;AACrB,YAAM,SAAS,CAAC,SAAa,SAAS,IAAI;AAC1C,aAAO;IACT;AACa,YAAA,kBAAuCC;AACvC,YAAA,0BAAkD;AAClD,YAAA,6BAAiD;AAG9D,aAAgB,YAAY,cAAc,IAAE;AAC1C,UAAI,SAAA,UAAU,OAAO,SAAA,OAAO,oBAAoB,YAAY;AAC1D,eAAO,SAAA,OAAO,gBAAgB,IAAI,WAAW,WAAW,CAAC;MAC3D;AAEA,UAAI,SAAA,UAAU,OAAO,SAAA,OAAO,gBAAgB,YAAY;AACtD,eAAO,WAAW,KAAK,SAAA,OAAO,YAAY,WAAW,CAAC;MACxD;AACA,YAAM,IAAI,MAAM,wCAAwC;IAC1D;;;;;;;;;;AClVA,YAAA,UAAA;AA7CA,QAAA,YAAA;AAEA,QAAA,aAAA;AAWA,QAAM,MAAM,OAAO,CAAC;AACpB,QAAM,MAAM,OAAO,CAAC;AACpB,QAAM,MAAM,OAAO,CAAC;AACpB,QAAM,MAAM,OAAO,CAAC;AACpB,QAAM,QAAQ,OAAO,GAAG;AACxB,QAAM,SAAS,OAAO,GAAI;AAC1B,QAAM,UAAoB,CAAA;AAC1B,QAAM,YAAsB,CAAA;AAC5B,QAAM,aAAuB,CAAA;AAC7B,aAAS,QAAQ,GAAG,IAAI,KAAK,IAAI,GAAGK,KAAI,GAAG,QAAQ,IAAI,SAAS;AAE9D,OAAC,GAAGA,EAAC,IAAI,CAACA,KAAI,IAAI,IAAI,IAAIA,MAAK,CAAC;AAChC,cAAQ,KAAK,KAAK,IAAIA,KAAI,EAAE;AAE5B,gBAAU,MAAQ,QAAQ,MAAM,QAAQ,KAAM,IAAK,EAAE;AAErD,UAAI,IAAI;AACR,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,aAAM,KAAK,OAAS,KAAK,OAAO,UAAW;AAC3C,YAAI,IAAI;AAAK,eAAK,QAAS,OAAuB,OAAO,CAAC,KAAK;MACjE;AACA,iBAAW,KAAK,CAAC;IACnB;AACA,QAAM,SAAQ,GAAA,UAAA,OAAM,YAAY,IAAI;AACpC,QAAM,cAAc,MAAM,CAAC;AAC3B,QAAM,cAAc,MAAM,CAAC;AAG3B,QAAM,QAAQ,CAAC,GAAW,GAAW,MAAe,IAAI,MAAK,GAAA,UAAA,QAAO,GAAG,GAAG,CAAC,KAAI,GAAA,UAAA,QAAO,GAAG,GAAG,CAAC;AAC7F,QAAM,QAAQ,CAAC,GAAW,GAAW,MAAe,IAAI,MAAK,GAAA,UAAA,QAAO,GAAG,GAAG,CAAC,KAAI,GAAA,UAAA,QAAO,GAAG,GAAG,CAAC;AAG7F,aAAgB,QAAQ,GAAgB,SAAiB,IAAE;AACzD,YAAM,IAAI,IAAI,YAAY,IAAI,CAAC;AAE/B,eAAS,QAAQ,KAAK,QAAQ,QAAQ,IAAI,SAAS;AAEjD,iBAAS,IAAI,GAAG,IAAI,IAAI;AAAK,YAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AACvF,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAC9B,gBAAM,QAAQ,IAAI,KAAK;AACvB,gBAAM,QAAQ,IAAI,KAAK;AACvB,gBAAM,KAAK,EAAE,IAAI;AACjB,gBAAM,KAAK,EAAE,OAAO,CAAC;AACrB,gBAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI;AACpC,gBAAM,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AACxC,mBAASA,KAAI,GAAGA,KAAI,IAAIA,MAAK,IAAI;AAC/B,cAAE,IAAIA,EAAC,KAAK;AACZ,cAAE,IAAIA,KAAI,CAAC,KAAK;UAClB;QACF;AAEA,YAAI,OAAO,EAAE,CAAC;AACd,YAAI,OAAO,EAAE,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,gBAAM,QAAQ,UAAU,CAAC;AACzB,gBAAM,KAAK,MAAM,MAAM,MAAM,KAAK;AAClC,gBAAM,KAAK,MAAM,MAAM,MAAM,KAAK;AAClC,gBAAM,KAAK,QAAQ,CAAC;AACpB,iBAAO,EAAE,EAAE;AACX,iBAAO,EAAE,KAAK,CAAC;AACf,YAAE,EAAE,IAAI;AACR,YAAE,KAAK,CAAC,IAAI;QACd;AAEA,iBAASA,KAAI,GAAGA,KAAI,IAAIA,MAAK,IAAI;AAC/B,mBAAS,IAAI,GAAG,IAAI,IAAI;AAAK,cAAE,CAAC,IAAI,EAAEA,KAAI,CAAC;AAC3C,mBAAS,IAAI,GAAG,IAAI,IAAI;AAAK,cAAEA,KAAI,CAAC,KAAK,CAAC,GAAG,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,KAAK,EAAE;QAC5E;AAEA,UAAE,CAAC,KAAK,YAAY,KAAK;AACzB,UAAE,CAAC,KAAK,YAAY,KAAK;MAC3B;AACA,OAAA,GAAA,WAAA,OAAM,CAAC;IACT;AAGA,QAAa,SAAb,MAAa,gBAAe,WAAA,KAAY;;MAetC,YACE,UACA,QACA,WACA,YAAY,OACZ,SAAiB,IAAE;AAEnB,cAAK;AApBG,aAAA,MAAM;AACN,aAAA,SAAS;AACT,aAAA,WAAW;AAEX,aAAA,YAAY;AAKZ,aAAA,YAAY;AAYpB,aAAK,WAAW;AAChB,aAAK,SAAS;AACd,aAAK,YAAY;AACjB,aAAK,YAAY;AACjB,aAAK,SAAS;AAEd,SAAA,GAAA,WAAA,SAAQ,SAAS;AAGjB,YAAI,EAAE,IAAI,YAAY,WAAW;AAC/B,gBAAM,IAAI,MAAM,yCAAyC;AAC3D,aAAK,QAAQ,IAAI,WAAW,GAAG;AAC/B,aAAK,WAAU,GAAA,WAAA,KAAI,KAAK,KAAK;MAC/B;MACA,QAAK;AACH,eAAO,KAAK,WAAU;MACxB;MACU,SAAM;AACd,SAAA,GAAA,WAAA,YAAW,KAAK,OAAO;AACvB,gBAAQ,KAAK,SAAS,KAAK,MAAM;AACjC,SAAA,GAAA,WAAA,YAAW,KAAK,OAAO;AACvB,aAAK,SAAS;AACd,aAAK,MAAM;MACb;MACA,OAAO,MAAW;AAChB,SAAA,GAAA,WAAA,SAAQ,IAAI;AACZ,gBAAO,GAAA,WAAA,SAAQ,IAAI;AACnB,SAAA,GAAA,WAAA,QAAO,IAAI;AACX,cAAM,EAAE,UAAU,MAAK,IAAK;AAC5B,cAAM,MAAM,KAAK;AACjB,iBAAS,MAAM,GAAG,MAAM,OAAO;AAC7B,gBAAM,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,GAAG;AACpD,mBAAS,IAAI,GAAG,IAAI,MAAM;AAAK,kBAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAC9D,cAAI,KAAK,QAAQ;AAAU,iBAAK,OAAM;QACxC;AACA,eAAO;MACT;MACU,SAAM;AACd,YAAI,KAAK;AAAU;AACnB,aAAK,WAAW;AAChB,cAAM,EAAE,OAAO,QAAQ,KAAK,SAAQ,IAAK;AAEzC,cAAM,GAAG,KAAK;AACd,aAAK,SAAS,SAAU,KAAK,QAAQ,WAAW;AAAG,eAAK,OAAM;AAC9D,cAAM,WAAW,CAAC,KAAK;AACvB,aAAK,OAAM;MACb;MACU,UAAU,KAAe;AACjC,SAAA,GAAA,WAAA,SAAQ,MAAM,KAAK;AACnB,SAAA,GAAA,WAAA,QAAO,GAAG;AACV,aAAK,OAAM;AACX,cAAM,YAAY,KAAK;AACvB,cAAM,EAAE,SAAQ,IAAK;AACrB,iBAAS,MAAM,GAAG,MAAM,IAAI,QAAQ,MAAM,OAAO;AAC/C,cAAI,KAAK,UAAU;AAAU,iBAAK,OAAM;AACxC,gBAAM,OAAO,KAAK,IAAI,WAAW,KAAK,QAAQ,MAAM,GAAG;AACvD,cAAI,IAAI,UAAU,SAAS,KAAK,QAAQ,KAAK,SAAS,IAAI,GAAG,GAAG;AAChE,eAAK,UAAU;AACf,iBAAO;QACT;AACA,eAAO;MACT;MACA,QAAQ,KAAe;AAErB,YAAI,CAAC,KAAK;AAAW,gBAAM,IAAI,MAAM,uCAAuC;AAC5E,eAAO,KAAK,UAAU,GAAG;MAC3B;MACA,IAAI,OAAa;AACf,SAAA,GAAA,WAAA,SAAQ,KAAK;AACb,eAAO,KAAK,QAAQ,IAAI,WAAW,KAAK,CAAC;MAC3C;MACA,WAAW,KAAe;AACxB,SAAA,GAAA,WAAA,SAAQ,KAAK,IAAI;AACjB,YAAI,KAAK;AAAU,gBAAM,IAAI,MAAM,6BAA6B;AAChE,aAAK,UAAU,GAAG;AAClB,aAAK,QAAO;AACZ,eAAO;MACT;MACA,SAAM;AACJ,eAAO,KAAK,WAAW,IAAI,WAAW,KAAK,SAAS,CAAC;MACvD;MACA,UAAO;AACL,aAAK,YAAY;AACjB,SAAA,GAAA,WAAA,OAAM,KAAK,KAAK;MAClB;MACA,WAAW,IAAW;AACpB,cAAM,EAAE,UAAU,QAAQ,WAAW,QAAQ,UAAS,IAAK;AAC3D,eAAA,KAAO,IAAI,QAAO,UAAU,QAAQ,WAAW,WAAW,MAAM;AAChE,WAAG,QAAQ,IAAI,KAAK,OAAO;AAC3B,WAAG,MAAM,KAAK;AACd,WAAG,SAAS,KAAK;AACjB,WAAG,WAAW,KAAK;AACnB,WAAG,SAAS;AAEZ,WAAG,SAAS;AACZ,WAAG,YAAY;AACf,WAAG,YAAY;AACf,WAAG,YAAY,KAAK;AACpB,eAAO;MACT;;AA1HF,YAAA,SAAA;AA6HA,QAAM,MAAM,CAAC,QAAgB,UAAkB,eAC7C,GAAA,WAAA,cAAa,MAAM,IAAI,OAAO,UAAU,QAAQ,SAAS,CAAC;AAG/C,YAAA,YAAmC,MAAM,IAAI,GAAM,KAAK,MAAM,CAAC,GAAE;AAEjE,YAAA,YAAmC,MAAM,IAAI,GAAM,KAAK,MAAM,CAAC,GAAE;AAEjE,YAAA,YAAmC,MAAM,IAAI,GAAM,KAAK,MAAM,CAAC,GAAE;AAEjE,YAAA,YAAmC,MAAM,IAAI,GAAM,IAAI,MAAM,CAAC,GAAE;AAGhE,YAAA,cAAqC,MAAM,IAAI,GAAM,KAAK,MAAM,CAAC,GAAE;AAEnE,YAAA,cAAqC,MAAM,IAAI,GAAM,KAAK,MAAM,CAAC,GAAE;AAEnE,YAAA,cAAqC,MAAM,IAAI,GAAM,KAAK,MAAM,CAAC,GAAE;AAEnE,YAAA,cAAqC,MAAM,IAAI,GAAM,IAAI,MAAM,CAAC,GAAE;AAI/E,QAAM,WAAW,CAAC,QAAgB,UAAkB,eAClD,GAAA,WAAA,aACE,CAAC,OAAkB,CAAA,MACjB,IAAI,OAAO,UAAU,QAAQ,KAAK,UAAU,SAAY,YAAY,KAAK,OAAO,IAAI,CAAC;AAI9E,YAAA,YAAqC,MAAM,SAAS,IAAM,KAAK,MAAM,CAAC,GAAE;AAExE,YAAA,YAAqC,MAAM,SAAS,IAAM,KAAK,MAAM,CAAC,GAAE;;;;;ACjQrF;AAAA;AAKA,QAAM,EAAE,WAAW,IAAI;AAQvB,aAAS,MAAO,OAAO;AACrB,aAAO,OAAO,YAAY,KAAK,EAAE,KAAK,CAAC;AAAA,IACzC;AAEA,aAAS,oBAAqB,KAAK;AACjC,aAAO,IAAI,SAAS,CAAC,EAAE;AAAA,IACzB;AAEA,aAAS,mBAAmB,KAAK,QAAQ;AACvC,UAAI,MAAM,IAAI,SAAS,EAAE;AAEzB,UAAI,IAAI,SAAS,MAAM,EAAG,OAAM,MAAM;AAEtC,YAAM,YAAY,IAAI,MAAM,SAAS,EAAE,IAAI,UAAQ,SAAS,MAAM,EAAE,CAAC;AAErE,aAAO,UAAU,SAAS,QAAQ;AAChC,kBAAU,QAAQ,CAAC;AAAA,MACrB;AAEA,aAAO,OAAO,KAAK,SAAS;AAAA,IAC9B;AAEA,aAAS,eAAe,OAAO,OAAO;AACpC,YAAM,aAAa,QAAQ;AAC3B,UAAI;AACJ,UAAI,YAAY;AAEd,cAAM,QAAQ,MAAM,OAAO,KAAK,KAAK;AAErC,kBAAU,CAAC,QAAQ,QAAQ;AAAA,MAC7B,OAAO;AACL,iBAAS;AAAA,MACX;AAEA,iBAAW,MAAM,OAAO,KAAK,KAAK;AAElC,aAAO;AAAA,IACT;AAWA,aAAS,UAAW,KAAK,QAAQ,OAAO;AACtC,YAAM,MAAM,MAAM,MAAM;AACxB,YAAM,SAAS,GAAG;AAClB,UAAI,OAAO;AACT,YAAI,IAAI,SAAS,QAAQ;AACvB,cAAI,KAAK,GAAG;AACZ,iBAAO;AAAA,QACT;AACA,eAAO,IAAI,MAAM,GAAG,MAAM;AAAA,MAC5B,OAAO;AACL,YAAI,IAAI,SAAS,QAAQ;AACvB,cAAI,KAAK,KAAK,SAAS,IAAI,MAAM;AACjC,iBAAO;AAAA,QACT;AACA,eAAO,IAAI,MAAM,CAAC,MAAM;AAAA,MAC1B;AAAA,IACF;AASA,aAAS,eAAgB,KAAK,QAAQ;AACpC,aAAO,UAAU,KAAK,QAAQ,IAAI;AAAA,IACpC;AAMA,aAAS,SAAU,GAAG;AACpB,UAAI,CAAC,OAAO,SAAS,CAAC,GAAG;AACvB,YAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,cAAI,OAAO,KAAK,CAAC;AAAA,QACnB,WAAW,OAAO,MAAM,UAAU;AAChC,cAAIC,aAAY,CAAC,GAAG;AAClB,gBAAI,OAAO,KAAK,UAAU,eAAe,CAAC,CAAC,GAAG,KAAK;AAAA,UACrD,OAAO;AACL,gBAAI,OAAO,KAAK,CAAC;AAAA,UACnB;AAAA,QACF,WAAW,OAAO,MAAM,UAAU;AAChC,cAAI,YAAY,CAAC;AAAA,QACnB,WAAW,MAAM,QAAQ,MAAM,QAAW;AACxC,cAAI,OAAO,YAAY,CAAC;AAAA,QAC1B,WAAW,OAAO,MAAM,UAAU;AAChC,cAAI,mBAAmB,CAAC;AAAA,QAC1B,WAAW,EAAE,SAAS;AAGpB,cAAI,OAAO,KAAK,EAAE,QAAQ,CAAC;AAAA,QAC7B,OAAO;AACL,gBAAM,IAAI,MAAM,cAAc;AAAA,QAChC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAOA,aAAS,YAAa,KAAK;AACzB,YAAM,SAAS,GAAG;AAClB,aAAO,OAAO,IAAI,SAAS,KAAK;AAAA,IAClC;AAQA,aAAS,OAAQ,GAAG,MAAM;AACxB,UAAI,SAAS,CAAC;AACd,UAAI,CAAC,KAAM,QAAO;AAClB,UAAI,SAAS,KAAK;AAChB,cAAM,IAAI,MAAM,aAAa;AAAA,MAC/B;AACA,aAAO,OAAO,KAAK,WAAW,IAAI,WAAW,CAAC,CAAC,CAAC;AAAA,IAClD;AAEA,aAAS,UAAW,KAAK;AACvB,aAAO,IAAI,SAAS,IAAI,MAAM,MAAM;AAAA,IACtC;AAEA,aAASA,aAAa,KAAK;AACzB,aAAO,OAAO,QAAQ,YAAY,IAAI,MAAM,kBAAkB;AAAA,IAChE;AAEA,aAAS,eAAgB,KAAK;AAC5B,UAAI,OAAO,QAAQ,YAAY,IAAI,WAAW,IAAI,GAAG;AACnD,eAAO,IAAI,MAAM,CAAC;AAAA,MACpB;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AC7KA;AAAA;AAKA,QAAM,OAAO;AAIb,aAAS,eAAgB,MAAM;AAC7B,UAAI,KAAK,WAAW,MAAM,GAAG;AAC3B,eAAO,WAAW,KAAK,MAAM,CAAC;AAAA,MAChC,WAAW,SAAS,OAAO;AACzB,eAAO;AAAA,MACT,WAAW,KAAK,WAAW,OAAO,GAAG;AACnC,eAAO,YAAY,KAAK,MAAM,CAAC;AAAA,MACjC,WAAW,SAAS,QAAQ;AAC1B,eAAO;AAAA,MACT,WAAW,KAAK,WAAW,QAAQ,GAAG;AACpC,eAAO,iBAAiB,KAAK,MAAM,CAAC;AAAA,MACtC,WAAW,SAAS,SAAS;AAC3B,eAAO;AAAA,MACT,WAAW,KAAK,WAAW,SAAS,GAAG;AACrC,eAAO,kBAAkB,KAAK,MAAM,CAAC;AAAA,MACvC,WAAW,SAAS,UAAU;AAC5B,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAGA,aAAS,WAAY,MAAM;AACzB,aAAO,OAAO,SAAS,aAAa,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE;AAAA,IACvD;AAGA,aAAS,aAAc,MAAM;AAC3B,UAAI,MAAM,mBAAmB,KAAK,IAAI;AACtC,aAAO,CAAE,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,CAAE;AAAA,IACpE;AAGA,aAAS,eAAgB,MAAM;AAC7B,UAAI,MAAM,KAAK,MAAM,gBAAgB;AACrC,UAAI,KAAK;AACP,eAAO,IAAI,CAAC,MAAM,KAAK,YAAY,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE;AAAA,MAC/D;AACA,aAAO;AAAA,IACT;AAEA,aAAS,YAAa,KAAK;AACzB,UAAI,OAAO,OAAO;AAClB,UAAI,SAAS,YAAY,SAAS,UAAU;AAC1C,eAAO,OAAO,GAAG;AAAA,MACnB,WAAW,SAAS,UAAU;AAC5B,eAAO;AAAA,MACT,OAAO;AACL,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC5C;AAAA,IACF;AAIA,aAAS,aAAc,MAAM,KAAK;AAChC,UAAI,MAAM,KAAK,KAAK;AAEpB,UAAI,SAAS,WAAW;AACtB,eAAO,aAAa,WAAW,YAAY,GAAG,CAAC;AAAA,MACjD,WAAW,SAAS,QAAQ;AAC1B,eAAO,aAAa,SAAS,MAAM,IAAI,CAAC;AAAA,MAC1C,WAAW,SAAS,UAAU;AAC5B,eAAO,aAAa,SAAS,IAAI,OAAO,KAAK,MAAM,CAAC;AAAA,MACtD,WAAW,QAAQ,IAAI,GAAG;AAGxB,YAAI,OAAO,IAAI,WAAW,aAAa;AACrC,gBAAM,IAAI,MAAM,eAAe;AAAA,QACjC;AACA,eAAO,eAAe,IAAI;AAC1B,YAAI,SAAS,aAAa,SAAS,KAAK,IAAI,SAAS,MAAM;AACzD,gBAAM,IAAI,MAAM,iCAAiC,IAAI;AAAA,QACvD;AACA,cAAM,CAAC;AACP,eAAO,KAAK,MAAM,GAAG,KAAK,YAAY,GAAG,CAAC;AAC1C,YAAI,OAAO,QAAQ,UAAU;AAC3B,gBAAM,KAAK,MAAM,GAAG;AAAA,QACtB;AACA,aAAK,KAAK,KAAK;AACb,cAAI,KAAK,aAAa,MAAM,IAAI,CAAC,CAAC,CAAC;AAAA,QACrC;AACA,YAAI,SAAS,WAAW;AACtB,cAAI,SAAS,aAAa,WAAW,IAAI,MAAM;AAC/C,cAAI,QAAQ,MAAM;AAAA,QACpB;AACA,eAAO,OAAO,OAAO,GAAG;AAAA,MAC1B,WAAW,SAAS,SAAS;AAC3B,cAAM,IAAI,OAAO,GAAG;AAEpB,cAAM,OAAO,OAAO,CAAE,aAAa,WAAW,IAAI,MAAM,GAAG,GAAI,CAAC;AAEhE,YAAK,IAAI,SAAS,OAAQ,GAAG;AAC3B,gBAAM,OAAO,OAAO,CAAE,KAAK,KAAK,MAAM,KAAM,IAAI,SAAS,EAAG,CAAE,CAAC;AAAA,QACjE;AAEA,eAAO;AAAA,MACT,WAAW,KAAK,WAAW,OAAO,GAAG;AACnC,eAAO,WAAW,IAAI;AACtB,YAAI,OAAO,KAAK,OAAO,IAAI;AACzB,gBAAM,IAAI,MAAM,6BAA6B,IAAI;AAAA,QACnD;AAEA,eAAO,KAAK,eAAe,KAAK,EAAE;AAAA,MACpC,WAAW,KAAK,WAAW,MAAM,GAAG;AAClC,eAAO,WAAW,IAAI;AACtB,YAAK,OAAO,KAAO,OAAO,KAAO,OAAO,KAAM;AAC5C,gBAAM,IAAI,MAAM,4BAA4B,IAAI;AAAA,QAClD;AAEA,cAAM,YAAY,GAAG;AACrB,cAAM,YAAY,KAAK,oBAAoB,GAAG;AAC9C,YAAI,YAAY,MAAM;AACpB,gBAAM,IAAI,MAAM,kCAAkC,OAAO,SAAS,SAAS;AAAA,QAC7E;AAEA,YAAI,MAAM,GAAG;AACX,gBAAM,IAAI,MAAM,2BAA2B;AAAA,QAC7C;AAEA,eAAO,KAAK,mBAAmB,KAAK,EAAE;AAAA,MACxC,WAAW,KAAK,WAAW,KAAK,GAAG;AACjC,eAAO,WAAW,IAAI;AACtB,YAAK,OAAO,KAAO,OAAO,KAAO,OAAO,KAAM;AAC5C,gBAAM,IAAI,MAAM,2BAA2B,IAAI;AAAA,QACjD;AAEA,cAAM,YAAY,GAAG;AACrB,cAAM,YAAY,KAAK,oBAAoB,GAAG;AAC9C,YAAI,YAAY,MAAM;AACpB,gBAAM,IAAI,MAAM,iCAAiC,OAAO,SAAS,SAAS;AAAA,QAC5E;AAEA,cAAM,OAAO,KAAK,eAAe,KAAK,GAAG;AAEzC,eAAO,KAAK,mBAAmB,MAAM,EAAE;AAAA,MACzC,WAAW,KAAK,WAAW,QAAQ,GAAG;AACpC,eAAO,aAAa,IAAI;AAExB,cAAM,YAAY,GAAG;AAErB,YAAI,MAAM,GAAG;AACX,gBAAM,IAAI,MAAM,6BAA6B;AAAA,QAC/C;AAEA,eAAO,aAAa,WAAW,MAAM,OAAO,CAAC,KAAK,OAAO,KAAK,CAAC,CAAC,CAAC;AAAA,MACnE,WAAW,KAAK,WAAW,OAAO,GAAG;AACnC,eAAO,aAAa,IAAI;AAExB,eAAO,aAAa,UAAU,YAAY,GAAG,IAAI,OAAO,CAAC,KAAK,OAAO,KAAK,CAAC,CAAC,CAAC;AAAA,MAC/E;AAEA,YAAM,IAAI,MAAM,kCAAkC,IAAI;AAAA,IACxD;AAGA,aAAS,UAAW,MAAM;AAExB,aAAQ,SAAS,YAAc,SAAS,WAAa,eAAe,IAAI,MAAM;AAAA,IAChF;AAGA,aAAS,QAAS,MAAM;AACtB,aAAO,KAAK,YAAY,GAAG,MAAM,KAAK,SAAS;AAAA,IACjD;AAKA,aAAS,UAAW,OAAO,QAAQ;AACjC,UAAI,SAAS,CAAC;AACd,UAAI,OAAO,CAAC;AAEZ,UAAI,aAAa,KAAK,MAAM;AAE5B,eAAS,KAAK,OAAO;AACnB,YAAI,OAAO,eAAe,MAAM,CAAC,CAAC;AAClC,YAAI,QAAQ,OAAO,CAAC;AACpB,YAAI,MAAM,aAAa,MAAM,KAAK;AAGlC,YAAI,UAAU,IAAI,GAAG;AACnB,iBAAO,KAAK,aAAa,WAAW,UAAU,CAAC;AAC/C,eAAK,KAAK,GAAG;AACb,wBAAc,IAAI;AAAA,QACpB,OAAO;AACL,iBAAO,KAAK,GAAG;AAAA,QACjB;AAAA,MACF;AAEA,aAAO,OAAO,OAAO,OAAO,OAAO,IAAI,CAAC;AAAA,IAC1C;AAEA,aAAS,aAAc,OAAO,QAAQ;AACpC,UAAI,MAAM,WAAW,OAAO,QAAQ;AAClC,cAAM,IAAI,MAAM,6CAA6C;AAAA,MAC/D;AAEA,UAAI,MAAM;AACV,UAAI,MAAM,CAAC;AAEX,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,OAAO,eAAe,MAAM,CAAC,CAAC;AAClC,YAAI,QAAQ,OAAO,CAAC;AAEpB,YAAI,SAAS,SAAS;AACpB,cAAI,KAAK,KAAK;AAAA,QAChB,WAAW,SAAS,UAAU;AAC5B,cAAI,KAAK,IAAI,OAAO,OAAO,MAAM,CAAC;AAAA,QACpC,WAAW,SAAS,QAAQ;AAC1B,cAAI,KAAK,IAAI,OAAO,QAAQ,OAAO,MAAM,KAAK,CAAC;AAAA,QACjD,WAAW,SAAS,WAAW;AAC7B,cAAI,KAAK,KAAK,UAAU,OAAO,EAAE,CAAC;AAAA,QACpC,WAAW,KAAK,WAAW,OAAO,GAAG;AACnC,iBAAO,WAAW,IAAI;AACtB,cAAI,OAAO,KAAK,OAAO,IAAI;AACzB,kBAAM,IAAI,MAAM,6BAA6B,IAAI;AAAA,UACnD;AAEA,cAAI,KAAK,KAAK,eAAe,OAAO,IAAI,CAAC;AAAA,QAC3C,WAAW,KAAK,WAAW,MAAM,GAAG;AAClC,iBAAO,WAAW,IAAI;AACtB,cAAK,OAAO,KAAO,OAAO,KAAO,OAAO,KAAM;AAC5C,kBAAM,IAAI,MAAM,4BAA4B,IAAI;AAAA,UAClD;AAEA,gBAAM,YAAY,KAAK;AACvB,gBAAM,YAAY,KAAK,oBAAoB,GAAG;AAC9C,cAAI,YAAY,MAAM;AACpB,kBAAM,IAAI,MAAM,kCAAkC,OAAO,SAAS,SAAS;AAAA,UAC7E;AAEA,cAAI,KAAK,KAAK,mBAAmB,KAAK,OAAO,CAAC,CAAC;AAAA,QACjD,WAAW,KAAK,WAAW,KAAK,GAAG;AACjC,iBAAO,WAAW,IAAI;AACtB,cAAK,OAAO,KAAO,OAAO,KAAO,OAAO,KAAM;AAC5C,kBAAM,IAAI,MAAM,2BAA2B,IAAI;AAAA,UACjD;AAEA,gBAAM,YAAY,KAAK;AACvB,gBAAM,YAAY,KAAK,oBAAoB,GAAG;AAC9C,cAAI,YAAY,MAAM;AACpB,kBAAM,IAAI,MAAM,iCAAiC,OAAO,SAAS,SAAS;AAAA,UAC5E;AAEA,gBAAM,OAAO,KAAK,eAAe,KAAK,IAAI;AAC1C,cAAI,KAAK,KAAK,mBAAmB,MAAM,OAAO,CAAC,CAAC;AAAA,QAClD,OAAO;AAEL,gBAAM,IAAI,MAAM,kCAAkC,IAAI;AAAA,QACxD;AAAA,MACF;AAEA,aAAO,OAAO,OAAO,GAAG;AAAA,IAC1B;AAEA,aAAS,aAAc,OAAO,QAAQ;AACpC,aAAO,KAAK,OAAO,aAAa,OAAO,MAAM,CAAC;AAAA,IAChD;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;AChRA;AAAA;AAGA,QAAM,OAAO;AACb,QAAM,MAAM;AAEZ,QAAM,uBAAuB;AAAA,MAC3B,MAAM;AAAA,MACN,YAAY;AAAA,QACV,OAAO;AAAA,UACL,MAAM;AAAA,UACN,sBAAsB;AAAA,YACpB,MAAM;AAAA,YACN,OAAO;AAAA,cACL,MAAM;AAAA,cACN,YAAY;AAAA,gBACV,MAAM,EAAC,MAAM,SAAQ;AAAA,gBACrB,MAAM,EAAC,MAAM,SAAQ;AAAA,cACvB;AAAA,cACA,UAAU,CAAC,QAAQ,MAAM;AAAA,YAC3B;AAAA,UACF;AAAA,QACF;AAAA,QACA,aAAa,EAAC,MAAM,SAAQ;AAAA,QAC5B,QAAQ,EAAC,MAAM,SAAQ;AAAA,QACvB,SAAS,EAAC,MAAM,SAAQ;AAAA,MAC1B;AAAA,MACA,UAAU,CAAC,SAAS,eAAe,UAAU,SAAS;AAAA,IACxD;AAKA,QAAM,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASrB,WAAY,aAAa,MAAM,OAAO,QAAQ,MAAM;AAClD,cAAM,eAAe,CAAC,SAAS;AAC/B,cAAM,gBAAgB,CAAC,KAAK,SAAS,aAAa,KAAK,CAAC;AAExD,YAAG,OAAO;AACR,gBAAM,cAAc,CAAC,MAAM,MAAM,UAAU;AACzC,gBAAI,MAAM,IAAI,MAAM,QAAW;AAC7B,qBAAO,CAAC,WAAW,SAAS,OAC1B,uEACA,KAAK,OAAO,KAAK,WAAW,MAAM,OAAO,OAAO,KAAK,CAAC,CAAC;AAAA,YAC3D;AAEA,gBAAG,UAAU;AACX,oBAAM,IAAI,MAAM,2BAA2B,IAAI,YAAY,IAAI,EAAE;AAEnE,gBAAI,SAAS,SAAS;AACpB,qBAAO,CAAC,WAAW,KAAK,OAAO,KAAK,CAAC;AAAA,YACvC;AAEA,gBAAI,SAAS,UAAU;AAErB,kBAAI,OAAO,UAAU,UAAU;AAC7B,wBAAQ,OAAO,KAAK,OAAO,MAAM;AAAA,cACnC;AACA,qBAAO,CAAC,WAAW,KAAK,OAAO,KAAK,CAAC;AAAA,YACvC;AAEA,gBAAI,KAAK,YAAY,GAAG,MAAM,KAAK,SAAS,GAAG;AAC7C,oBAAM,aAAa,KAAK,MAAM,GAAG,KAAK,YAAY,GAAG,CAAC;AACtD,oBAAM,iBAAiB,MAAM,IAAI,UAC/B,YAAY,MAAM,YAAY,IAAI,CAAC;AACrC,qBAAO,CAAC,WAAW,KAAK,OAAO,IAAI;AAAA,gBACjC,eAAe,IAAI,CAAC,CAACC,KAAI,MAAMA,KAAI;AAAA,gBACnC,eAAe,IAAI,CAAC,CAAC,EAAEC,MAAK,MAAMA,MAAK;AAAA,cACzC,CAAC,CAAC;AAAA,YACJ;AAEA,mBAAO,CAAC,MAAM,KAAK;AAAA,UACrB;AAEA,qBAAW,SAAS,MAAM,WAAW,GAAG;AACtC,kBAAM,CAAC,MAAM,KAAK,IAAI,YAAY,MAAM,MAAM,MAAM,MAAM,KAAK,MAAM,IAAI,CAAC;AAC1E,yBAAa,KAAK,IAAI;AACtB,0BAAc,KAAK,KAAK;AAAA,UAC1B;AAAA,QACF,OAAO;AACL,qBAAW,SAAS,MAAM,WAAW,GAAG;AACtC,gBAAI,QAAQ,KAAK,MAAM,IAAI;AAC3B,gBAAI,UAAU,QAAW;AACvB,kBAAI,MAAM,SAAS,SAAS;AAC1B,6BAAa,KAAK,SAAS;AAC3B,wBAAQ,KAAK,OAAO,KAAK;AACzB,8BAAc,KAAK,KAAK;AAAA,cAC1B,WAAW,MAAM,SAAS,UAAU;AAClC,6BAAa,KAAK,SAAS;AAE3B,oBAAI,OAAO,UAAU,UAAU;AAC7B,0BAAQ,OAAO,KAAK,OAAO,MAAM;AAAA,gBACnC;AACA,wBAAQ,KAAK,OAAO,KAAK;AACzB,8BAAc,KAAK,KAAK;AAAA,cAC1B,WAAW,MAAM,MAAM,IAAI,MAAM,QAAW;AAC1C,6BAAa,KAAK,SAAS;AAC3B,wBAAQ,KAAK,OAAO,KAAK,WAAW,MAAM,MAAM,OAAO,OAAO,KAAK,CAAC;AACpE,8BAAc,KAAK,KAAK;AAAA,cAC1B,WAAW,MAAM,KAAK,YAAY,GAAG,MAAM,MAAM,KAAK,SAAS,GAAG;AAChE,sBAAM,IAAI,MAAM,8CAA8C;AAAA,cAChE,OAAO;AACL,6BAAa,KAAK,MAAM,IAAI;AAC5B,8BAAc,KAAK,KAAK;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,eAAO,IAAI,UAAU,cAAc,aAAa;AAAA,MAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,WAAY,aAAa,OAAO;AAC9B,YAAI,SAAS;AACb,YAAI,OAAO,KAAK,qBAAqB,aAAa,KAAK,EAAE,OAAO,SAAO,QAAQ,WAAW;AAC1F,eAAO,CAAC,WAAW,EAAE,OAAO,KAAK,KAAK,CAAC;AACvC,mBAAW,QAAQ,MAAM;AACvB,gBAAM,WAAW,MAAM,IAAI;AAC3B,cAAI,CAAC,UAAU;AACb,kBAAM,IAAI,MAAM,mCAAmC,IAAI;AAAA,UACzD;AACA,oBAAU,OAAO,MAAM,MAAM,IAAI,EAAE,IAAI,CAAC,EAAE,MAAM,MAAAD,MAAK,MAAMA,QAAO,MAAM,IAAI,EAAE,KAAK,GAAG,IAAI;AAAA,QAC5F;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,qBAAsB,aAAa,OAAO,UAAU,CAAC,GAAG;AACtD,sBAAc,YAAY,MAAM,MAAM,EAAE,CAAC;AACzC,YAAI,QAAQ,SAAS,WAAW,KAAK,MAAM,WAAW,MAAM,QAAW;AAAE,iBAAO;AAAA,QAAQ;AACxF,gBAAQ,KAAK,WAAW;AACxB,mBAAW,SAAS,MAAM,WAAW,GAAG;AACtC,qBAAW,OAAO,KAAK,qBAAqB,MAAM,MAAM,OAAO,OAAO,GAAG;AACvE,aAAC,QAAQ,SAAS,GAAG,KAAK,QAAQ,KAAK,GAAG;AAAA,UAC5C;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,WAAY,aAAa,MAAM,OAAO,QAAQ,MAAM;AAClD,eAAO,KAAK,OAAO,KAAK,WAAW,aAAa,MAAM,OAAO,KAAK,CAAC;AAAA,MACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,SAAU,aAAa,OAAO;AAC5B,eAAO,KAAK,OAAO,KAAK,WAAW,aAAa,KAAK,CAAC;AAAA,MACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,aAAc,MAAM;AAClB,cAAM,gBAAgB,CAAC;AACvB,mBAAW,OAAO,qBAAqB,YAAY;AACjD,eAAK,GAAG,MAAM,cAAc,GAAG,IAAI,KAAK,GAAG;AAAA,QAC7C;AACA,YAAI,cAAc,OAAO;AACvB,wBAAc,QAAQ,OAAO,OAAO,EAAE,cAAc,CAAC,EAAE,GAAG,cAAc,KAAK;AAAA,QAC/E;AACA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,KAAM,WAAW,QAAQ,MAAM;AAC7B,cAAM,gBAAgB,KAAK,aAAa,SAAS;AACjD,cAAM,QAAQ,CAAC,OAAO,KAAK,QAAQ,KAAK,CAAC;AACzC,cAAM,KAAK,KAAK,WAAW,gBAAgB,cAAc,QAAQ,cAAc,OAAO,KAAK,CAAC;AAC5F,YAAI,cAAc,gBAAgB,gBAAgB;AAChD,gBAAM,KAAK,KAAK,WAAW,cAAc,aAAa,cAAc,SAAS,cAAc,OAAO,KAAK,CAAC;AAAA,QAC1G;AACA,eAAO,KAAK,OAAO,OAAO,OAAO,KAAK,CAAC;AAAA,MACzC;AAAA,IACF;AAEA,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MAEA,4BAA4B,SAAU,WAAW;AAC/C,eAAO,yBAAyB,UAAU,IAAI;AAAA,MAChD;AAAA,MAEA,yBAAyB,SAAU,WAAW;AAC5C,eAAO,eAAe,KAAK,UAAU,MAAM,KAAK;AAAA,MAClD;AAAA,MAEA,yBAAyB,SAAU,WAAW;AAC5C,eAAO,eAAe,KAAK,UAAU,IAAI;AAAA,MAC3C;AAAA,IACF;AAMA,aAAS,yBAAyB,WAAW;AAC3C,YAAM,QAAQ,IAAI,MAAM,uCAAuC;AAC/D,UAAI,OAAO,cAAc,YAAY,CAAC,UAAU,OAAQ,OAAM;AAE9D,YAAM,OAAO,UAAU,IAAI,SAAU,GAAG;AACtC,eAAO,EAAE,SAAS,UAAU,KAAK,SAAS,EAAE,KAAK,IAAI,EAAE;AAAA,MACzD,CAAC;AACD,YAAM,QAAQ,UAAU,IAAI,SAAU,GAAG;AAAE,eAAO,EAAE;AAAA,MAAK,CAAC;AAC1D,YAAM,SAAS,UAAU,IAAI,SAAU,GAAG;AACxC,YAAI,CAAC,EAAE,KAAM,OAAM;AACnB,eAAO,EAAE,OAAO,MAAM,EAAE;AAAA,MAC1B,CAAC;AAED,aAAO,IAAI;AAAA,QACT,CAAC,WAAW,SAAS;AAAA,QACrB;AAAA,UACE,IAAI,aAAa,IAAI,MAAM,UAAU,MAAM,EAAE,KAAK,QAAQ,GAAG,MAAM;AAAA,UACnE,IAAI,aAAa,OAAO,IAAI;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC3PO,IAAM,aAAa,CAAC,MAAgB,UAAiB;AAC1D,MAAI;AACJ,UAAQ,MAAM;IACZ,KAAK;AACH,eAAS;AACT,aAAO,oCAAoC,KAAK,aAAa,MAAM;IACrE,KAAK;AACH,eAAS;AACT,aAAO,uEAAuE,KAAK,aAAa,MAAM;IACxG,KAAK;AACH,gBAAU,MAAM,OAAO,QAAQ,CAAC;AAChC,aAAO,oCAAoC,KAAK,aAAa,MAAM;IACrE,KAAK;AACH,gBAAU,OAAO,OAAO,QAAQ,CAAC;AACjC,aAAO,oCAAoC,KAAK,aAAa,MAAM;IACrE,KAAK;AACH,gBAAU,MAAM,OAAO,QAAQ,CAAC;AAChC,aAAO,oCAAoC,KAAK,aAAa,MAAM;IACrE,KAAK;AACH,gBAAU,OAAO,OAAO,QAAQ,CAAC;AACjC,aAAO,oCAAoC,KAAK,aAAa,MAAM;IACrE;AACE,eAAS;AACT,aAAO,oCAAoC,KAAK,aAAa,MAAM;EACvE;AACF;;;AC9BM,IAAO,qBAAP,MAAO,oBAAkB;EAC7B,YACU,OACA,QAAe;AADf,SAAA,QAAA;AACA,SAAA,SAAA;EACP;EAEH,YAAe,KAAa,MAAO;AACjC,SAAK,QAAQ,KAAK,KAAK,UAAU,IAAI,CAAC;EACxC;EAEA,WAAc,KAAW;AACvB,UAAM,OAAO,KAAK,QAAQ,GAAG;AAC7B,WAAO,OAAO,KAAK,MAAM,IAAI,IAAI;EACnC;EAEO,QAAQ,KAAa,OAAa;AACvC,iBAAa,QAAQ,KAAK,UAAU,GAAG,GAAG,KAAK;EACjD;EAEO,QAAQ,KAAW;AACxB,WAAO,aAAa,QAAQ,KAAK,UAAU,GAAG,CAAC;EACjD;EAEO,WAAW,KAAW;AAC3B,iBAAa,WAAW,KAAK,UAAU,GAAG,CAAC;EAC7C;EAEO,QAAK;AACV,UAAM,SAAS,KAAK,UAAU,EAAE;AAChC,UAAM,eAAyB,CAAA;AAC/B,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,YAAM,MAAM,aAAa,IAAI,CAAC;AAC9B,UAAI,OAAO,QAAQ,YAAY,IAAI,WAAW,MAAM,GAAG;AACrD,qBAAa,KAAK,GAAG;MACvB;IACF;AACA,iBAAa,QAAQ,CAAC,QAAQ,aAAa,WAAW,GAAG,CAAC;EAC5D;EAEA,UAAU,KAAW;AACnB,WAAO,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,IAAI,KAAK,MAAM,KAAK,EAAE,IAAI,GAAG;EACrE;EAEA,OAAO,WAAQ;AACb,QAAI,oBAAmB,QAAQ,EAAE,MAAK;AACtC,QAAI,oBAAmB,YAAY,EAAE,MAAK;EAC5C;;;;ACzBK,IAAM,qBAAiC;EAC5C,KAAK;IACH,cAAc;IACd,kBAAkB;IAClB,qBAAqB;IACrB,qBAAqB;IACrB,oBAAoB;IACpB,eAAe;IACf,OAAO;IACP,gBAAgB;IAChB,gBAAgB;IAChB,eAAe;IACf,UAAU;;EAEZ,UAAU;IACR,qBAAqB;IACrB,cAAc;IACd,mBAAmB;IACnB,cAAc;IACd,mBAAmB;IACnB,kBAAkB;;;AAIf,IAAM,cAAc;EACzB,UAAU;IACR,UAAU;IACV,SACE;;EAEJ,UAAU;IACR,UAAU;IACV,SAAS;;EAEX,UAAU;IACR,UAAU;IACV,SAAS;;EAEX,UAAU;IACR,UAAU;IACV,SAAS;;EAEX,UAAU;IACR,UAAU;IACV,SAAS;;EAEX,UAAU;IACR,UAAU;IACV,SAAS;;EAEX,UAAU;IACR,UAAU;IACV,SAAS;;EAEX,UAAU;IACR,UAAU;IACV,SAAS;;EAEX,UAAU;IACR,UAAU;IACV,SAAS;;EAEX,UAAU;IACR,UAAU;IACV,SAAS;;EAEX,UAAU;IACR,UAAU;IACV,SAAS;;EAEX,QAAQ;IACN,UAAU;IACV,SAAS;;EAEX,QAAQ;IACN,UAAU;IACV,SAAS;;EAEX,QAAQ;IACN,UAAU;IACV,SAAS;;EAEX,QAAQ;IACN,UAAU;IACV,SAAS;;EAEX,QAAQ;IACN,UAAU;IACV,SAAS;;EAEX,QAAQ;IACN,UAAU;IACV,SAAS;;;;;AClHb,IAAM,mBAAmB;AAElB,IAAM,gCAAgC;AAQvC,SAAU,mBACd,MACA,kBAA0B,kBAAgB;AAE1C,MAAI,QAAQ,OAAO,UAAU,IAAI,GAAG;AAClC,UAAM,aAAa,KAAK,SAAQ;AAEhC,QAAI,OAAO,aAAa,UAAU,GAAG;AACnC,aAAO,YAAY,UAA2B,EAAE;IAClD;AACA,QAAI,qBAAqB,IAAI,GAAG;AAC9B,aAAO;IACT;EACF;AACA,SAAO;AACT;AAMM,SAAU,YAAY,MAAY;AACtC,MAAI,CAAC,OAAO,UAAU,IAAI,GAAG;AAC3B,WAAO;EACT;AAEA,QAAM,aAAa,KAAK,SAAQ;AAChC,MAAI,YAAY,UAA2B,GAAG;AAC5C,WAAO;EACT;AAEA,MAAI,qBAAqB,IAAI,GAAG;AAC9B,WAAO;EACT;AACA,SAAO;AACT;AA2CM,SAAU,UACd,OACA,EAAE,qBAAqB,MAAK,IAAK,CAAA,GAAE;AAEnC,QAAM,aAAkD,CAAA;AAExD,MACE,SACA,OAAO,UAAU,YACjB,CAAC,MAAM,QAAQ,KAAK,KACpB,OAAO,OAAkC,MAAM,KAC/C,YAAa,MAAqC,IAAI,GACtD;AACA,UAAM,SAAS;AACf,eAAW,OAAO,OAAO;AAEzB,QAAI,OAAO,WAAW,OAAO,OAAO,YAAY,UAAU;AACxD,iBAAW,UAAU,OAAO;AAE5B,UAAI,OAAO,QAAQ,MAAM,GAAG;AAC1B,mBAAW,OAAO,OAAO;MAC3B;IACF,OAAO;AACL,iBAAW,UAAU,mBAAoB,WAA0C,IAAI;AAEvF,iBAAW,OAAO,EAAE,eAAe,oBAAoB,KAAK,EAAC;IAC/D;EACF,OAAO;AACL,eAAW,OAAO,mBAAmB,IAAI;AAEzC,eAAW,UAAU,kBAAkB,OAAO,SAAS,IAAI,MAAM,UAAU;AAC3E,eAAW,OAAO,EAAE,eAAe,oBAAoB,KAAK,EAAC;EAC/D;AAEA,MAAI,oBAAoB;AACtB,eAAW,QAAQ,kBAAkB,OAAO,OAAO,IAAI,MAAM,QAAQ;EACvE;AACA,SAAO;AACT;AAIA,SAAS,qBAAqB,MAAY;AACxC,SAAO,QAAQ,UAAU,QAAQ;AACnC;AAEA,SAAS,oBAAoB,OAAc;AACzC,MAAI,SAAS,OAAO,UAAU,YAAY,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC/D,WAAO,OAAO,OAAO,CAAA,GAAI,KAAK;EAChC;AACA,SAAO;AACT;AAEA,SAAS,OAAO,KAA8B,KAAW;AACvD,SAAO,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG;AACtD;AAEA,SAAS,kBAAqB,KAAc,MAAa;AACvD,SACE,OAAO,QAAQ,YAAY,QAAQ,QAAQ,QAAQ,OAAO,OAAQ,IAAU,IAAI,MAAM;AAE1F;;;ACpJO,IAAM,iBAAiB;EAC5B,KAAK;IACH,OAAO,CAAI,QAA0B,mBAAmB,mBAAmB,IAAI,OAAO,GAAG;IAEzF,gBAAgB,CAAI,QAClB,mBAAmB,mBAAmB,IAAI,gBAAgB,GAAG;IAE/D,eAAe,CAAI,QACjB,mBAAmB,mBAAmB,IAAI,eAAe,GAAG;IAE9D,gBAAgB,CAAI,QAClB,mBAAmB,mBAAmB,IAAI,gBAAgB,GAAG;IAE/D,UAAU,CAAI,QACZ,mBAAmB,mBAAmB,IAAI,UAAU,GAAG;IAEzD,QAAQ,CAAI,SAA+B;AACzC,UAAI,CAAC,QAAQ,OAAO,SAAS,YAAY,MAAM,QAAQ,IAAI,GAAG;AAC5D,cAAM,IAAI,MAAM,iEAAiE;MACnF;AACA,YAAM,EAAE,KAAI,IAAK;AACjB,UAAI,CAAC,OAAO,UAAU,IAAI,KAAK,OAAO,UAAU,OAAO,QAAQ;AAC7D,cAAM,IAAI,MAAM,+DAA+D;MACjF;AACA,aAAO,mBAAmB,MAAM,IAAI;IACtC;IAEA,cAAc,CAAI,QAChB,mBAAmB,mBAAmB,IAAI,cAAc,GAAG;IAE7D,kBAAkB,CAAI,QACpB,mBAAmB,mBAAmB,IAAI,kBAAkB,GAAG;IAEjE,qBAAqB,CAAI,QACvB,mBAAmB,mBAAmB,IAAI,qBAAqB,GAAG;IAEpE,qBAAqB,CAAI,QACvB,mBAAmB,mBAAmB,IAAI,qBAAqB,GAAG;IAEpE,oBAAoB,CAAI,QACtB,mBAAmB,mBAAmB,IAAI,oBAAoB,GAAG;IAEnE,eAAe,CAAI,QACjB,mBAAmB,mBAAmB,IAAI,eAAe,GAAG;;EAGhE,UAAU;IACR,qBAAqB,CAAI,QAAyB;AAChD,aAAO,oBAAoB,mBAAmB,SAAS,qBAAqB,GAAG;IACjF;IAEA,cAAc,CAAI,QAAyB;AACzC,aAAO,oBAAoB,mBAAmB,SAAS,cAAc,GAAG;IAC1E;IAEA,mBAAmB,CAAI,QAAyB;AAC9C,aAAO,oBAAoB,mBAAmB,SAAS,mBAAmB,GAAG;IAC/E;IAEA,cAAc,CAAI,QAAyB;AACzC,aAAO,oBAAoB,mBAAmB,SAAS,cAAc,GAAG;IAC1E;IAEA,mBAAmB,CAAI,QAAyB;AAC9C,aAAO,oBAAoB,mBAAmB,SAAS,mBAAmB,GAAG;IAC/E;IAEA,kBAAkB,CAAI,QAAyB;AAC7C,aAAO,oBAAoB,mBAAmB,SAAS,kBAAkB,GAAG;IAC9E;IAEA,QAAQ,CAAI,SAA2B;AACrC,UAAI,CAAC,QAAQ,OAAO,SAAS,YAAY,MAAM,QAAQ,IAAI,GAAG;AAC5D,cAAM,IAAI,MAAM,sEAAsE;MACxF;AAEA,YAAM,EAAE,MAAM,SAAS,KAAI,IAAK;AAEhC,UAAI,CAAC,WAAW,OAAO,YAAY,UAAU;AAC3C,cAAM,IAAI,MAAM,qCAAqC;MACvD;AACA,aAAO,IAAI,sBAAsB,MAAM,SAAS,IAAI;IACtD;;;AAMJ,SAAS,mBAAsB,MAAc,KAAqB;AAChE,QAAM,CAAC,SAAS,IAAI,IAAI,UAAU,GAAG;AACrC,SAAO,IAAI,iBAAiB,MAAM,WAAW,mBAAmB,IAAI,GAAG,IAAI;AAC7E;AAEA,SAAS,oBAAuB,MAAc,KAAqB;AACjE,QAAM,CAAC,SAAS,IAAI,IAAI,UAAU,GAAG;AACrC,SAAO,IAAI,sBAAsB,MAAM,WAAW,mBAAmB,IAAI,GAAG,IAAI;AAClF;AAEA,SAAS,UAAa,KAAqB;AACzC,MAAI,KAAK;AACP,QAAI,OAAO,QAAQ,UAAU;AAC3B,aAAO,CAAC,GAAG;IACb,WAAW,OAAO,QAAQ,YAAY,CAAC,MAAM,QAAQ,GAAG,GAAG;AACzD,YAAM,EAAE,SAAS,KAAI,IAAK;AAE1B,UAAI,WAAW,OAAO,YAAY,UAAU;AAC1C,cAAM,IAAI,MAAM,8BAA8B;MAChD;AACA,aAAO,CAAC,WAAW,QAAW,IAAI;IACpC;EACF;AACA,SAAO,CAAA;AACT;AAeA,IAAM,mBAAN,cAAkC,MAAK;EAKrC,YAAY,MAAc,SAAiB,MAAQ;AACjD,QAAI,CAAC,OAAO,UAAU,IAAI,GAAG;AAC3B,YAAM,IAAI,MAAM,4BAA4B;IAC9C;AACA,QAAI,CAAC,WAAW,OAAO,YAAY,UAAU;AAC3C,YAAM,IAAI,MAAM,sCAAsC;IACxD;AAEA,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,QAAI,SAAS,QAAW;AACtB,WAAK,OAAO;IACd;EACF;;AAGF,IAAM,wBAAN,cAAuC,iBAAmB;;;;;EAKxD,YAAY,MAAc,SAAiB,MAAQ;AACjD,QAAI,CAAC,uBAAuB,IAAI,GAAG;AACjC,YAAM,IAAI,MAAM,2DAA2D;IAC7E;AAEA,UAAM,MAAM,SAAS,IAAI;EAC3B;;AAGF,SAAS,uBAAuB,MAAY;AAC1C,SAAO,OAAO,UAAU,IAAI,KAAK,QAAQ,OAAQ,QAAQ;AAC3D;;;AC/JM,SAAU,aAAU;AACxB,SAAO,CAAC,UAAyD;AACnE;AAGO,IAAM,YAAY,WAAU;AAG5B,IAAM,gBAAgB,WAAU;AAGhC,IAAM,eAAe,WAAU;AAGhC,SAAU,UAAU,KAAW;AACnC,SAAO,KAAK,MAAM,GAAG;AACvB;AAGO,IAAM,eAAe,WAAU;;;ACrBtC,IAAM,mBAAmB;AACzB,IAAM,2BAA2B;AAK3B,SAAU,eAAe,QAAc;AAC3C,SAAO,gBAAgB,OAAO,gBAAgB,IAAI,WAAW,MAAM,CAAC,CAAC;AACvE;AAEM,SAAU,gBAAgB,OAAiB;AAC/C,SAAO,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE;AACvE;AAEM,SAAU,sBAAsB,WAAiB;AACrD,SAAO,IAAI,WAAW,UAAU,MAAM,SAAS,EAAG,IAAI,CAAC,SAAS,OAAO,SAAS,MAAM,EAAE,CAAC,CAAC;AAC5F;AAEM,SAAU,oBAAoB,KAAa,gBAAgB,OAAK;AACpE,QAAM,MAAM,IAAI,SAAS,KAAK;AAC9B,SAAO,UAAU,gBAAgB,KAAK,GAAG,KAAK,GAAG;AACnD;AAEM,SAAU,kBAAkB,KAAY;AAC5C,SAAO,oBAAoB,aAAa,GAAG,GAAG,IAAI;AACpD;AAEM,SAAU,uBAAuB,IAAU;AAC/C,SAAO,aAAa,GAAG,SAAS,EAAE,CAAC;AACrC;AAMM,SAAU,oBAAoB,KAAW;AAC7C,SAAO,UAAU,KAAK,OAAO,GAAG,EAAE,SAAS,EAAE,CAAC,EAAE;AAClD;AAEM,SAAU,YAAY,KAAW;AACrC,SAAO,IAAI,WAAW,IAAI,KAAK,IAAI,WAAW,IAAI;AACpD;AAEM,SAAU,QAAQ,KAAW;AACjC,MAAI,YAAY,GAAG,GAAG;AACpB,WAAO,IAAI,MAAM,CAAC;EACpB;AACA,SAAO;AACT;AAEM,SAAU,UAAU,KAAW;AACnC,MAAI,YAAY,GAAG,GAAG;AACpB,WAAO,KAAK,IAAI,MAAM,CAAC,CAAC;EAC1B;AACA,SAAO,KAAK,GAAG;AACjB;AAEM,SAAU,YAAY,KAAY;AACtC,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO;EACT;AACA,QAAM,IAAI,QAAQ,GAAG,EAAE,YAAW;AAClC,SAAO,yBAAyB,KAAK,CAAC;AACxC;AAEM,SAAU,gBAAgB,KAAc,gBAAgB,OAAK;AACjE,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,QAAQ,GAAG,EAAE,YAAW;AAClC,QAAI,yBAAyB,KAAK,CAAC,GAAG;AACpC,aAAO,UAAU,gBAAgB,KAAK,CAAC,KAAK,CAAC;IAC/C;EACF;AACA,QAAM,eAAe,IAAI,cAAc,IAAI,OAAO,GAAG,CAAC,+BAA+B;AACvF;AAEM,SAAU,0BAA0B,KAAc,gBAAgB,OAAK;AAC3E,MAAI,IAAI,gBAAgB,KAAK,KAAK;AAClC,MAAI,EAAE,SAAS,MAAM,GAAG;AACtB,QAAI,UAAU,IAAI,CAAC,EAAE;EACvB;AACA,SAAO,gBAAgB,UAAU,KAAK,CAAC,EAAE,IAAI;AAC/C;AAEM,SAAU,oBAAoB,KAAY;AAC9C,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,IAAI,QAAQ,GAAG,EAAE,YAAW;AAClC,QAAI,YAAY,CAAC,KAAK,EAAE,WAAW,IAAI;AACrC,aAAO,cAAc,UAAU,CAAC,CAAC;IACnC;EACF;AACA,QAAM,eAAe,IAAI,cAAc,6BAA6B,OAAO,GAAG,CAAC,EAAE;AACnF;AAEM,SAAU,aAAa,KAAY;AACvC,MAAI,OAAO,SAAS,GAAG,GAAG;AACxB,WAAO;EACT;AACA,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI,YAAY,GAAG,GAAG;AACpB,YAAM,IAAI,0BAA0B,KAAK,KAAK;AAC9C,aAAO,OAAO,KAAK,GAAG,KAAK;IAC7B;AACA,WAAO,OAAO,KAAK,KAAK,MAAM;EAChC;AACA,QAAM,eAAe,IAAI,cAAc,oBAAoB,OAAO,GAAG,CAAC,EAAE;AAC1E;AAEM,SAAU,gBAAgB,KAAY;AAC1C,MAAI,OAAO,QAAQ,YAAY,OAAO,UAAU,GAAG,GAAG;AACpD,WAAO,UAAU,GAAG;EACtB;AACA,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI,iBAAiB,KAAK,GAAG,GAAG;AAC9B,aAAO,UAAU,OAAO,GAAG,CAAC;IAC9B;AACA,QAAI,YAAY,GAAG,GAAG;AACpB,aAAO,UAAU,OAAO,OAAO,0BAA0B,KAAK,IAAI,CAAC,CAAC,CAAC;IACvE;EACF;AACA,QAAM,eAAe,IAAI,cAAc,mBAAmB,OAAO,GAAG,CAAC,EAAE;AACzE;AASM,SAAU,aAAa,KAAY;AACvC,MAAI,QAAQ,SAAS,OAAO,QAAQ,YAAY,YAAY,GAAG,IAAI;AACjE,WAAO,OAAQ,IAAY,SAAS,EAAE,CAAC;EACzC;AACA,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,OAAO,gBAAgB,GAAG,CAAC;EACpC;AACA,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI,iBAAiB,KAAK,GAAG,GAAG;AAC9B,aAAO,OAAO,GAAG;IACnB;AACA,QAAI,YAAY,GAAG,GAAG;AACpB,aAAO,OAAO,0BAA0B,KAAK,IAAI,CAAC;IACpD;EACF;AACA,QAAM,eAAe,IAAI,cAAc,mBAAmB,OAAO,GAAG,CAAC,EAAE;AACzE;AAEM,SAAU,uBAAyC,KAAY;AACnE,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO,KAAK,MAAM,GAAG;EACvB;AAEA,MAAI,OAAO,QAAQ,UAAU;AAC3B,WAAO;EACT;AAEA,QAAM,eAAe,IAAI,cAAc,mCAAmC,OAAO,GAAG,CAAC,EAAE;AACzF;AAEM,SAAU,YAAY,KAAY;AACtC,MAAI,OAAO,QAAQ,OAAQ,IAAY,gBAAgB,YAAY;AACjE,WAAO;EACT;AACA,QAAM,EAAE,YAAW,IAAK;AACxB,SAAO,OAAO,YAAY,WAAW,cAAc,OAAO,YAAY,WAAW;AACnF;AAMM,SAAU,aAAU;AACxB,QAAM,KACJ,SAAS,cAAc,uBAAuB,KAC9C,SAAS,cAAc,uBAAuB,KAC9C,SAAS,cAAc,kBAAkB,KACzC,SAAS,cAAc,2BAA2B;AAEpD,QAAM,EAAE,UAAU,KAAI,IAAK,SAAS;AACpC,QAAM,OAAO,KAAK,GAAG,aAAa,MAAM,IAAI;AAC5C,MAAI,CAAC,QAAQ,KAAK,WAAW,aAAa,KAAK,KAAK,WAAW,WAAW,GAAG;AAC3E,WAAO,GAAG,QAAQ,KAAK,IAAI;EAC7B;AACA,MAAI,KAAK,WAAW,SAAS,KAAK,KAAK,WAAW,UAAU,KAAK,KAAK,WAAW,OAAO,GAAG;AACzF,WAAO;EACT;AACA,MAAI,KAAK,WAAW,IAAI,GAAG;AACzB,WAAO,WAAW;EACpB;AACA,SAAO,GAAG,QAAQ,KAAK,IAAI,GAAG,IAAI;AACpC;;;AC/LA,eAAsB,kBAAe;AACnC,SAAO,OAAO,OAAO,YACnB;IACE,MAAM;IACN,YAAY;KAEd,MACA,CAAC,WAAW,CAAC;AAEjB;AAEA,eAAsB,mBACpB,eACA,eAAwB;AAExB,SAAO,OAAO,OAAO,UACnB;IACE,MAAM;IACN,QAAQ;KAEV,eACA;IACE,MAAM;IACN,QAAQ;KAEV,OACA,CAAC,WAAW,SAAS,CAAC;AAE1B;AAEA,eAAsB,QAAQ,cAAyB,WAAiB;AACtE,QAAM,KAAK,OAAO,gBAAgB,IAAI,WAAW,EAAE,CAAC;AACpD,QAAM,aAAa,MAAM,OAAO,OAAO,QACrC;IACE,MAAM;IACN;KAEF,cACA,IAAI,YAAW,EAAG,OAAO,SAAS,CAAC;AAGrC,SAAO,EAAE,IAAI,WAAU;AACzB;AAEA,eAAsB,QACpB,cACA,EAAE,IAAI,WAAU,GAAiB;AAEjC,QAAM,YAAY,MAAM,OAAO,OAAO,QACpC;IACE,MAAM;IACN;KAEF,cACA,UAAU;AAGZ,SAAO,IAAI,YAAW,EAAG,OAAO,SAAS;AAC3C;AAEA,SAAS,UAAU,SAA6B;AAC9C,UAAQ,SAAS;IACf,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;EACX;AACF;AAEA,eAAsB,qBACpB,MACA,KAAc;AAEd,QAAM,SAAS,UAAU,IAAI;AAC7B,QAAM,WAAW,MAAM,OAAO,OAAO,UAAU,QAAQ,GAAG;AAC1D,SAAO,gBAAgB,IAAI,WAAW,QAAQ,CAAC;AACjD;AAEA,eAAsB,uBACpB,MACA,WAAiB;AAEjB,QAAM,SAAS,UAAU,IAAI;AAC7B,QAAM,cAAc,sBAAsB,SAAS,EAAE;AACrD,SAAO,MAAM,OAAO,OAAO,UACzB,QACA,IAAI,WAAW,WAAW,GAC1B;IACE,MAAM;IACN,YAAY;KAEd,MACA,SAAS,YAAY,CAAC,WAAW,IAAI,CAAA,CAAE;AAE3C;AAEA,eAAsB,eACpB,SACA,cAAuB;AAEvB,QAAM,aAAa,KAAK,UAAU,SAAS,CAACE,IAAG,UAAS;AACtD,QAAI,EAAE,iBAAiB;AAAQ,aAAO;AAEtC,UAAM,QAAQ;AACd,WAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACM,MAAM,OAAO,EAAE,MAAM,MAAM,KAAI,IAAK,CAAA,CAAG,GAAA,EAC3C,SAAS,MAAM,QAAO,CAAA;EAE1B,CAAC;AACD,SAAO,QAAQ,cAAc,UAAU;AACzC;AAEA,eAAsB,eACpB,eACA,cAAuB;AAEvB,SAAO,KAAK,MAAM,MAAM,QAAQ,cAAc,aAAa,CAAC;AAC9D;;;AC9GA,IAAM,kBAAkB;EACtB,YAAY;EACZ,SAAS;;AAEX,IAAM,iBAAiB;EACrB,YAAY;EACZ,SAAS;;AAEX,IAAM,kBAAkB;EACtB,YAAY;EACZ,SAAS;;AAGL,IAAO,gBAAP,MAAoB;EAA1B,cAAA;AACmB,SAAA,UAAU,IAAI,mBAAmB,UAAU,eAAe;AACnE,SAAA,gBAAkC;AAClC,SAAA,eAAiC;AACjC,SAAA,gBAAkC;AAClC,SAAA,eAAiC;EA2E3C;EAzEE,MAAM,kBAAe;AACnB,UAAM,KAAK,iBAAgB;AAC3B,WAAO,KAAK;EACd;;EAGA,MAAM,kBAAe;AACnB,UAAM,KAAK,iBAAgB;AAC3B,WAAO,KAAK;EACd;EAEA,MAAM,iBAAiB,KAAc;AACnC,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,UAAM,KAAK,SAAS,iBAAiB,GAAG;AACxC,UAAM,KAAK,iBAAgB;EAC7B;EAEA,MAAM,QAAK;AACT,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,eAAe;AAEpB,SAAK,QAAQ,WAAW,eAAe,UAAU;AACjD,SAAK,QAAQ,WAAW,gBAAgB,UAAU;AAClD,SAAK,QAAQ,WAAW,gBAAgB,UAAU;EACpD;EAEQ,MAAM,kBAAe;AAC3B,UAAM,aAAa,MAAM,gBAAe;AACxC,SAAK,gBAAgB,WAAW;AAChC,SAAK,eAAe,WAAW;AAC/B,UAAM,KAAK,SAAS,iBAAiB,WAAW,UAAU;AAC1D,UAAM,KAAK,SAAS,gBAAgB,WAAW,SAAS;EAC1D;EAEQ,MAAM,mBAAgB;AAC5B,QAAI,KAAK,kBAAkB,MAAM;AAC/B,WAAK,gBAAgB,MAAM,KAAK,QAAQ,eAAe;IACzD;AAEA,QAAI,KAAK,iBAAiB,MAAM;AAC9B,WAAK,eAAe,MAAM,KAAK,QAAQ,cAAc;IACvD;AAEA,QAAI,KAAK,kBAAkB,QAAQ,KAAK,iBAAiB,MAAM;AAC7D,YAAM,KAAK,gBAAe;IAC5B;AAEA,QAAI,KAAK,kBAAkB,MAAM;AAC/B,WAAK,gBAAgB,MAAM,KAAK,QAAQ,eAAe;IACzD;AAEA,QAAI,KAAK,iBAAiB,MAAM;AAC9B,UAAI,KAAK,kBAAkB,QAAQ,KAAK,kBAAkB;AAAM;AAChE,WAAK,eAAe,MAAM,mBAAmB,KAAK,eAAe,KAAK,aAAa;IACrF;EACF;;EAIQ,MAAM,QAAQ,MAAiB;AACrC,UAAM,MAAM,KAAK,QAAQ,QAAQ,KAAK,UAAU;AAChD,QAAI,CAAC;AAAK,aAAO;AAEjB,WAAO,uBAAuB,KAAK,SAAS,GAAG;EACjD;EAEQ,MAAM,SAAS,MAAmB,KAAc;AACtD,UAAM,YAAY,MAAM,qBAAqB,KAAK,SAAS,GAAG;AAC9D,SAAK,QAAQ,QAAQ,KAAK,YAAY,SAAS;EACjD;;;;ACxGK,IAAM,UAAU;AAChB,IAAM,OAAO;;;ACOpB,eAAsB,gBAAgB,SAA2B,QAAc;AAC7E,QAAM,cAAW,OAAA,OAAA,OAAA,OAAA,CAAA,GACZ,OAAO,GAAA,EACV,SAAS,OACT,IAAI,OAAO,WAAU,EAAE,CAAA;AAEzB,QAAM,MAAM,MAAM,OAAO,MAAM,QAAQ;IACrC,QAAQ;IACR,MAAM,KAAK,UAAU,WAAW;IAChC,MAAM;IACN,SAAS;MACP,gBAAgB;MAChB,qBAAqB;MACrB,sBAAsB;;GAEzB;AACD,QAAM,EAAE,QAAQ,MAAK,IAAK,MAAM,IAAI,KAAI;AACxC,MAAI;AAAO,UAAM;AACjB,SAAO;AACT;AAaA,SAAS,oCAAiC;AACxC,QAAMC,UAAS;AACf,SAAOA,QAAO;AAChB;AAEA,SAAS,sBAAmB;;AAC1B,MAAI;AACF,UAAMA,UAAS;AACf,YAAO,KAAAA,QAAO,cAAQ,QAAA,OAAA,SAAA,MAAI,KAAAA,QAAO,SAAG,QAAA,OAAA,SAAA,SAAA,GAAE;EACxC,SAAE,IAAM;AACN,WAAO;EACT;AACF;AAEM,SAAU,4BAA4B,EAC1C,UACA,WAAU,GACmB;;AAC7B,QAAM,EAAE,SAAS,YAAY,YAAW,IAAK;AAE7C,MAAI,WAAW,YAAY,mBAAmB;AAC5C,UAAM,YAAY,kCAAiC;AACnD,QAAI,WAAW;AACb,OAAA,KAAA,UAAU,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,WAAG,SAAS,YAAY,aAAa,UAAU;AACnE,aAAO;IACT;EACF;AAEA,QAAM,WAAW,oBAAmB;AACpC,MAAI,aAAQ,QAAR,aAAQ,SAAA,SAAR,SAAU,mBAAmB;AAC/B,KAAA,KAAA,SAAS,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,UAAG,SAAS,YAAY,aAAa,UAAU;AAClE,WAAO;EACT;AAEA,SAAO;AACT;AAQM,SAAU,gCAAgC,MAAa;AAC3D,MAAI,CAAC,QAAQ,OAAO,SAAS,YAAY,MAAM,QAAQ,IAAI,GAAG;AAC5D,UAAM,eAAe,IAAI,cAAc;MACrC,SAAS;MACT,MAAM;KACP;EACH;AAEA,QAAM,EAAE,QAAQ,OAAM,IAAK;AAE3B,MAAI,OAAO,WAAW,YAAY,OAAO,WAAW,GAAG;AACrD,UAAM,eAAe,IAAI,cAAc;MACrC,SAAS;MACT,MAAM;KACP;EACH;AAEA,MACE,WAAW,UACX,CAAC,MAAM,QAAQ,MAAM,MACpB,OAAO,WAAW,YAAY,WAAW,OAC1C;AACA,UAAM,eAAe,IAAI,cAAc;MACrC,SAAS;MACT,MAAM;KACP;EACH;AAEA,UAAQ,QAAQ;IACd,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACH,YAAM,eAAe,SAAS,kBAAiB;EACnD;AACF;;;ACpGA,IAAM,eAAe;AACrB,IAAM,2BAA2B;AACjC,IAAM,+BAA+B;AACrC,IAAM,kCAAkC;AAalC,IAAO,YAAP,MAAgB;EAUpB,YAAY,QAA0B;;AACpC,SAAK,WAAW,OAAO;AACvB,SAAK,eAAe,OAAO;AAC3B,SAAK,WAAW,OAAO;AACvB,SAAK,aAAa,IAAI,cAAa;AACnC,SAAK,UAAU,IAAI,mBAAmB,UAAU,iBAAiB;AAEjE,SAAK,YAAW,KAAA,KAAK,QAAQ,WAAW,YAAY,OAAC,QAAA,OAAA,SAAA,KAAI,CAAA;AACzD,SAAK,QAAQ,KAAK,QAAQ,WAAW,wBAAwB,KAAK;MAChE,KAAI,MAAA,KAAA,OAAO,SAAS,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAG,CAAC,OAAC,QAAA,OAAA,SAAA,KAAI;;AAG1C,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,UAAU,KAAK,QAAQ,KAAK,IAAI;AACrC,SAAK,uBAAuB,KAAK,qBAAqB,KAAK,IAAI;AAC/D,SAAK,yBAAyB,KAAK,uBAAuB,KAAK,IAAI;EACrE;EAEA,MAAM,UAAU,MAAsB;;AAGpC,YAAM,MAAA,KAAA,KAAK,cAAa,wBAAkB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;AAE1C,UAAM,mBAAmB,MAAM,KAAK,qBAAqB;MACvD,WAAW;QACT,QAAQ,KAAK;QACb,QAAQ,OAAO,OAAO,CAAA,GAAI,KAAK,WAAU,KAAA,KAAK,YAAM,QAAA,OAAA,SAAA,KAAI,CAAA,CAAE;;KAE7D;AACD,UAAM,WACJ,MAAM,KAAK,aAAa,8BAA8B,gBAAgB;AAGxE,QAAI,aAAa,SAAS;AAAS,YAAM,SAAS,QAAQ;AAC1D,UAAM,gBAAgB,MAAM,uBAAuB,UAAU,SAAS,MAAM;AAC5E,UAAM,KAAK,WAAW,iBAAiB,aAAa;AAEpD,UAAM,YAAY,MAAM,KAAK,uBAAuB,QAAQ;AAE5D,UAAM,SAAS,UAAU;AACzB,QAAI,WAAW;AAAQ,YAAM,OAAO;AAEpC,YAAQ,KAAK,QAAQ;MACnB,KAAK,uBAAuB;AAC1B,cAAM,WAAW,OAAO;AACxB,aAAK,WAAW;AAChB,aAAK,QAAQ,YAAY,cAAc,QAAQ;AAC/C,SAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,mBAAmB,QAAQ;AAC3C;MACF;IACF;EACF;EAEA,MAAM,QAAQ,SAAyB;;AACrC,QAAI,KAAK,SAAS,WAAW,GAAG;AAC9B,cAAQ,QAAQ,QAAQ;QACtB,KAAK;AACH,iBAAO,KAAK,mBAAmB,OAAO;QACxC;AACE,gBAAM,eAAe,SAAS,aAAY;MAC9C;IACF;AAEA,YAAQ,QAAQ,QAAQ;MACtB,KAAK;AACH,SAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,WAAW,EAAE,SAAS,oBAAoB,KAAK,MAAM,EAAE,EAAC,CAAE;AAC1E,eAAO,KAAK;MACd,KAAK;AACH,eAAO,KAAK;MACd,KAAK;AACH,eAAO,KAAK,SAAS,CAAC;MACxB,KAAK;AACH,eAAO,KAAK,MAAM;MACpB,KAAK;AACH,eAAO,oBAAoB,KAAK,MAAM,EAAE;MAC1C,KAAK;AACH,eAAO,KAAK,QAAQ,WAAW,+BAA+B;MAChE,KAAK;AACH,eAAO,KAAK,yBAAyB,OAAO;MAC9C,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;AACH,eAAO,KAAK,mBAAmB,OAAO;MACxC;AACE,YAAI,CAAC,KAAK,MAAM;AAAQ,gBAAM,eAAe,IAAI,SAAS,0BAA0B;AACpF,eAAO,gBAAgB,SAAS,KAAK,MAAM,MAAM;IACrD;EACF;EAEQ,MAAM,mBAAmB,SAAyB;;AAGxD,YAAM,MAAA,KAAA,KAAK,cAAa,wBAAkB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA;AAE1C,UAAM,WAAW,MAAM,KAAK,qBAAqB,OAAO;AACxD,UAAM,YAAY,MAAM,KAAK,uBAAuB,QAAQ;AAE5D,UAAM,SAAS,UAAU;AACzB,QAAI,WAAW;AAAQ,YAAM,OAAO;AAEpC,WAAO,OAAO;EAChB;EAEA,MAAM,UAAO;;AACX,SAAK,QAAQ,MAAK;AAClB,UAAM,KAAK,WAAW,MAAK;AAC3B,SAAK,WAAW,CAAA;AAChB,SAAK,QAAQ;MACX,KAAI,MAAA,KAAA,KAAK,SAAS,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAG,CAAC,OAAC,QAAA,OAAA,SAAA,KAAI;;EAE1C;;;;;EAMQ,MAAM,yBAAyB,SAAyB;;AAC9D,UAAM,SAAS,QAAQ;AAKvB,QAAI,CAAC,UAAU,GAAC,KAAA,OAAO,CAAC,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE,UAAS;AAClC,YAAM,eAAe,IAAI,cAAa;IACxC;AACA,UAAM,UAAU,gBAAgB,OAAO,CAAC,EAAE,OAAO;AAEjD,UAAM,cAAc,KAAK,YAAY,OAAO;AAC5C,QAAI;AAAa,aAAO;AAExB,UAAM,cAAc,MAAM,KAAK,mBAAmB,OAAO;AACzD,QAAI,gBAAgB,MAAM;AACxB,WAAK,YAAY,OAAO;IAC1B;AACA,WAAO;EACT;EAEQ,MAAM,qBAAqB,SAAyB;AAC1D,UAAM,eAAe,MAAM,KAAK,WAAW,gBAAe;AAC1D,QAAI,CAAC,cAAc;AACjB,YAAM,eAAe,SAAS,aAC5B,kEAAkE;IAEtE;AAEA,UAAM,YAAY,MAAM,eACtB;MACE,QAAQ;MACR,SAAS,KAAK,MAAM;OAEtB,YAAY;AAEd,UAAM,UAAU,MAAM,KAAK,qBAAqB,EAAE,UAAS,CAAE;AAE7D,WAAO,KAAK,aAAa,8BAA8B,OAAO;EAChE;EAEQ,MAAM,qBACZ,SAAqC;AAErC,UAAM,YAAY,MAAM,qBAAqB,UAAU,MAAM,KAAK,WAAW,gBAAe,CAAE;AAC9F,WAAO;MACL,IAAI,OAAO,WAAU;MACrB,QAAQ;MACR;MACA,WAAW,oBAAI,KAAI;;EAEvB;EAEQ,MAAM,uBAAuB,SAA2B;;AAC9D,UAAM,UAAU,QAAQ;AAGxB,QAAI,aAAa,SAAS;AACxB,YAAM,QAAQ;IAChB;AAEA,UAAM,eAAe,MAAM,KAAK,WAAW,gBAAe;AAC1D,QAAI,CAAC,cAAc;AACjB,YAAM,eAAe,SAAS,aAAa,iBAAiB;IAC9D;AAEA,UAAM,WAAwB,MAAM,eAAe,QAAQ,WAAW,YAAY;AAElF,UAAM,mBAAkB,KAAA,SAAS,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE;AACvC,QAAI,iBAAiB;AACnB,YAAM,SAAS,OAAO,QAAQ,eAAe,EAAE,IAAI,CAAC,CAAC,IAAI,MAAM,OAAO;QACpE,IAAI,OAAO,EAAE;QACb;QACA;AACF,WAAK,QAAQ,YAAY,8BAA8B,MAAM;AAC7D,WAAK,YAAY,KAAK,MAAM,IAAI,MAAM;IACxC;AAEA,UAAM,sBAAqB,KAAA,SAAS,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE;AAC1C,QAAI,oBAAoB;AACtB,WAAK,QAAQ,YAAY,iCAAiC,kBAAkB;IAC9E;AAEA,WAAO;EACT;EAEQ,YAAY,SAAiB,oBAA4B;;AAC/D,UAAM,SACJ,uBAAkB,QAAlB,uBAAkB,SAAlB,qBAAsB,KAAK,QAAQ,WAAoB,4BAA4B;AACrF,UAAM,QAAQ,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,KAAK,CAACC,WAAUA,OAAM,OAAO,OAAO;AAC1D,QAAI,CAAC;AAAO,aAAO;AAEnB,QAAI,UAAU,KAAK,OAAO;AACxB,WAAK,QAAQ;AACb,WAAK,QAAQ,YAAY,0BAA0B,KAAK;AACxD,OAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,gBAAgB,oBAAoB,MAAM,EAAE,CAAC;IAC/D;AACA,WAAO;EACT;;;;AC1QF,6BAAmB;;;ACJZ,IAAM,uBAAuB;AAC7B,IAAM,8BAA8B;AACpC,IAAM,kBAAkB;;;ACazB,SAAU,gBAAgB,UAAiB;AAC/C,SAAQ,SAA2B,iBAAiB;AACtD;;;ACbM,IAAO,mBAAP,MAAuB;;EAE3B,YAA6B,QAAc;AAAd,SAAA,SAAA;EAAiB;;;;;;;;EAS9C,MAAM,QAAQ,WAAiB;AAC7B,UAAM,SAAS,KAAK;AACpB,QAAI,OAAO,WAAW;AAAI,YAAM,MAAM,yBAAyB;AAC/D,UAAM,UAAU,OAAO,gBAAgB,IAAI,WAAW,EAAE,CAAC;AACzD,UAAM,YAAuB,MAAM,OAAO,OAAO,UAC/C,OACA,sBAAsB,MAAM,GAC5B,EAAE,MAAM,UAAS,GACjB,OACA,CAAC,WAAW,SAAS,CAAC;AAGxB,UAAM,MAAM,IAAI,YAAW;AAG3B,UAAM,kBAA+B,MAAM,OAAO,OAAO,OAAO,QAC9D;MACE,MAAM;MACN,IAAI;OAEN,WACA,IAAI,OAAO,SAAS,CAAC;AAGvB,UAAM,YAAY;AAClB,UAAM,UAAuB,gBAAgB,MAAM,gBAAgB,aAAa,SAAS;AACzF,UAAM,qBAAqB,gBAAgB,MAAM,GAAG,gBAAgB,aAAa,SAAS;AAE1F,UAAM,eAAe,IAAI,WAAW,OAAO;AAC3C,UAAM,0BAA0B,IAAI,WAAW,kBAAkB;AACjE,UAAM,YAAY,IAAI,WAAW,CAAC,GAAG,SAAS,GAAG,cAAc,GAAG,uBAAuB,CAAC;AAC1F,WAAO,gBAAgB,SAAS;EAClC;;;;;;EAOA,MAAM,QAAQ,YAAkB;AAC9B,UAAM,SAAS,KAAK;AACpB,QAAI,OAAO,WAAW;AAAI,YAAM,MAAM,yBAAyB;AAC/D,WAAO,IAAI,QAAgB,CAAC,SAAS,WAAU;AAC7C,WAAM,iBAAK;AACT,cAAM,YAAuB,MAAM,OAAO,OAAO,UAC/C,OACA,sBAAsB,MAAM,GAC5B,EAAE,MAAM,UAAS,GACjB,OACA,CAAC,WAAW,SAAS,CAAC;AAGxB,cAAM,YAAwB,sBAAsB,UAAU;AAE9D,cAAM,UAAU,UAAU,MAAM,GAAG,EAAE;AACrC,cAAM,eAAe,UAAU,MAAM,IAAI,EAAE;AAC3C,cAAM,0BAA0B,UAAU,MAAM,EAAE;AAClD,cAAM,iBAAiB,IAAI,WAAW,CAAC,GAAG,yBAAyB,GAAG,YAAY,CAAC;AACnF,cAAM,OAAO;UACX,MAAM;UACN,IAAI,IAAI,WAAW,OAAO;;AAE5B,YAAI;AACF,gBAAM,YAAY,MAAM,OAAO,OAAO,OAAO,QAAQ,MAAM,WAAW,cAAc;AACpF,gBAAM,UAAU,IAAI,YAAW;AAC/B,kBAAQ,QAAQ,OAAO,SAAS,CAAC;QACnC,SAAS,KAAK;AACZ,iBAAO,GAAG;QACZ;MACF,EAAE;IACJ,CAAC;EACH;;;;ACpFI,IAAO,iBAAP,MAAqB;EAGzB,YACmB,YACA,WACjB,YAAkB;AAFD,SAAA,aAAA;AACA,SAAA,YAAA;AAGjB,UAAM,cAAc,GAAG,SAAS,IAAI,UAAU;AAC9C,SAAK,OAAO,SAAS,KAAK,WAAW,CAAC;EACxC;;EAGQ,MAAM,uBAAuB,QAAgC;AACnE,WAAO,QAAQ,IACb,OAAO,IAAI,CAAC,MACV,MAAM,GAAG,KAAK,UAAU,WAAW,EAAE,OAAO,SAAS;MACnD,QAAQ;MACR,SAAS;QACP,eAAe,KAAK;;KAEvB,CAAC,CACH,EACD,MAAM,CAAC,UAAU,QAAQ,MAAM,oCAAoC,KAAK,CAAC;EAC7E;EAEA,MAAM,oBAAiB;;AACrB,UAAM,WAAW,MAAM,MAAM,GAAG,KAAK,UAAU,uBAAuB;MACpE,SAAS;QACP,eAAe,KAAK;;KAEvB;AAED,QAAI,SAAS,IAAI;AACf,YAAM,EAAE,QAAQ,MAAK,IAAM,MAAM,SAAS,KAAI;AAU9C,UAAI,OAAO;AACT,cAAM,IAAI,MAAM,+BAA+B,KAAK,EAAE;MACxD;AAEA,YAAM,kBACJ,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OACI,OAAO,CAAC,MAAM,EAAE,UAAU,cAAc,EACzC,IAAI,CAAC,OAAO;QACX,MAAM;QACN,WAAW,KAAK;QAChB,SAAS,EAAE;QACX,OAAO,EAAE;QACT,MAAM,EAAE;QACR,OAAC,QAAA,OAAA,SAAA,KAAI,CAAA;AAEX,WAAK,uBAAuB,cAAc;AAE1C,aAAO;IACT;AACA,UAAM,IAAI,MAAM,+BAA+B,SAAS,MAAM,EAAE;EAClE;;;;AC9DF,IAAY;CAAZ,SAAYC,kBAAe;AACzB,EAAAA,iBAAAA,iBAAA,cAAA,IAAA,CAAA,IAAA;AACA,EAAAA,iBAAAA,iBAAA,YAAA,IAAA,CAAA,IAAA;AACA,EAAAA,iBAAAA,iBAAA,WAAA,IAAA,CAAA,IAAA;AACF,GAJY,oBAAA,kBAAe,CAAA,EAAA;AAMrB,IAAO,sBAAP,MAA0B;EAM9B,2BAA2B,UAAsC;AAC/D,SAAK,0BAA0B;EACjC;EAGA,wBAAwB,UAAoC;AAC1D,SAAK,uBAAuB;EAC9B;;;;;;EAOA,YACE,KACiB,iBAAmC,WAAS;AAA5C,SAAA,iBAAA;AApBX,SAAA,YAA8B;AAC9B,SAAA,cAAwB,CAAA;AAqB9B,SAAK,MAAM,IAAI,QAAQ,SAAS,IAAI;EACtC;;;;;EAMO,MAAM,UAAO;AAClB,QAAI,KAAK,WAAW;AAClB,YAAM,IAAI,MAAM,8BAA8B;IAChD;AACA,WAAO,IAAI,QAAc,CAAC,SAAS,WAAU;;AAC3C,UAAI;AACJ,UAAI;AACF,aAAK,YAAY,YAAY,IAAI,KAAK,eAAe,KAAK,GAAG;MAC/D,SAAS,KAAK;AACZ,eAAO,GAAG;AACV;MACF;AACA,OAAA,KAAA,KAAK,6BAAuB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,gBAAgB,UAAU;AACzD,gBAAU,UAAU,CAAC,QAAO;;AAC1B,aAAK,eAAc;AACnB,eAAO,IAAI,MAAM,mBAAmB,IAAI,IAAI,KAAK,IAAI,MAAM,EAAE,CAAC;AAC9D,SAAAC,MAAA,KAAK,6BAAuB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,MAAG,gBAAgB,YAAY;MAC7D;AACA,gBAAU,SAAS,CAACC,OAAK;;AACvB,gBAAO;AACP,SAAAD,MAAA,KAAK,6BAAuB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,MAAG,gBAAgB,SAAS;AAExD,YAAI,KAAK,YAAY,SAAS,GAAG;AAC/B,gBAAM,UAAU,CAAC,GAAG,KAAK,WAAW;AACpC,kBAAQ,QAAQ,CAAC,SAAS,KAAK,SAAS,IAAI,CAAC;AAC7C,eAAK,cAAc,CAAA;QACrB;MACF;AACA,gBAAU,YAAY,CAAC,QAAO;;AAC5B,YAAI,IAAI,SAAS,KAAK;AACpB,WAAAA,MAAA,KAAK,0BAAoB,QAAAA,QAAA,SAAA,SAAAA,IAAA,KAAA,MAAG;YAC1B,MAAM;WACP;QACH,OAAO;AACL,cAAI;AACF,kBAAM,UAAU,KAAK,MAAM,IAAI,IAAI;AACnC,aAAA,KAAA,KAAK,0BAAoB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,OAAO;UACrC,SAAE,IAAM;UAER;QACF;MACF;IACF,CAAC;EACH;;;;EAKO,aAAU;;AACf,UAAM,EAAE,UAAS,IAAK;AACtB,QAAI,CAAC,WAAW;AACd;IACF;AACA,SAAK,eAAc;AAEnB,KAAA,KAAA,KAAK,6BAAuB,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,gBAAgB,YAAY;AAC3D,SAAK,0BAA0B;AAC/B,SAAK,uBAAuB;AAE5B,QAAI;AACF,gBAAU,MAAK;IACjB,SAAE,IAAM;IAER;EACF;;;;;EAMO,SAAS,MAAY;AAC1B,UAAM,EAAE,UAAS,IAAK;AACtB,QAAI,CAAC,WAAW;AACd,WAAK,YAAY,KAAK,IAAI;AAC1B,WAAK,QAAO;AACZ;IACF;AACA,cAAU,KAAK,IAAI;EACrB;EAEQ,iBAAc;AACpB,UAAM,EAAE,UAAS,IAAK;AACtB,QAAI,CAAC,WAAW;AACd;IACF;AACA,SAAK,YAAY;AACjB,cAAU,UAAU;AACpB,cAAU,UAAU;AACpB,cAAU,YAAY;AACtB,cAAU,SAAS;EACrB;;;;ACtHF,IAAM,qBAAqB;AAC3B,IAAM,kBAAkB;AAoBlB,IAAO,uBAAP,MAA2B;;;;;;;;EAmB/B,YAAY,EAAE,SAAS,YAAY,SAAQ,GAA8B;AAlBjE,SAAA,YAAY;AACZ,SAAA,wBAAwB;AACxB,SAAA,YAAY,UAAU,CAAC;AAmJvB,SAAA,aAAa;AAWb,SAAA,UAAU;AAyCV,SAAA,mCAAmC;AAqFnC,SAAA,qBAAqB,oBAAI,IAAG;AAmD5B,SAAA,+BAA+B,CAAC,aAAqC;AAC3E,UAAI,CAAC;AAAU;AAGf,YAAM,WAAW,oBAAI,IAAqC;QACxD,CAAC,eAAe,KAAK,eAAe;QACpC,CAAC,mBAAmB,KAAK,oBAAoB;QAC7C,CAAC,kBAAkB,KAAK,2BAA2B;QACnD,CAAC,cAAc,KAAK,uBAAuB;QAC3C;UACE;;UACA,CAAC,MAAc,SAAS,cAAc,KAAK,mBAAmB,GAAG,SAAS,UAAU;;OAEvF;AAGD,eAAS,QAAQ,CAAC,SAAS,QAAO;AAChC,cAAM,QAAQ,SAAS,GAAG;AAC1B,YAAI,UAAU;AAAW;AACzB,gBAAQ,KAAK;MACf,CAAC;IACH;AAEQ,SAAA,kBAAkB,CAAC,gBAAuB;;AAChD,UAAI,gBAAgB;AAAK;AAEzB,OAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,eAAc;IAC/B;AAEQ,SAAA,uBAAuB,OAAO,6BAAoC;;AACxE,YAAM,UAAU,MAAM,KAAK,OAAO,QAAQ,wBAAwB;AAClE,OAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,eAAe,OAAO;IACvC;AAEQ,SAAA,wBAAwB,OAAO,KAAa,2BAAkC;;AACpF,YAAM,iBAAiB,MAAM,KAAK,OAAO,QAAQ,sBAAsB;AACvE,OAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,gBAAgB,KAAK,cAAc;IACpD;AAEQ,SAAA,8BAA8B,OAAO,mBAA0B;AACrE,WAAK,sBAAsB,sBAAsB,cAAc;IACjE;AAEQ,SAAA,0BAA0B,OAAO,eAAsB;AAC7D,WAAK,sBAAsB,iBAAiB,UAAU;IACxD;AAEQ,SAAA,qBAAqB,OAAO,kBAA0B,wBAA+B;;AAC3F,YAAM,UAAU,MAAM,KAAK,OAAO,QAAQ,gBAAgB;AAC1D,YAAM,aAAa,MAAM,KAAK,OAAO,QAAQ,mBAAmB;AAChE,OAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,aAAa,SAAS,UAAU;IACjD;AAjXE,SAAK,UAAU;AACf,SAAK,SAAS,IAAI,iBAAiB,QAAQ,MAAM;AACjD,SAAK,WAAW;AAEhB,UAAM,KAAK,IAAI,oBAAoB,GAAG,UAAU,QAAQ,SAAS;AACjE,OAAG,2BAA2B,OAAO,UAAS;AAE5C,UAAI,YAAY;AAChB,cAAQ,OAAO;QACb,KAAK,gBAAgB;AAEnB,cAAI,CAAC,KAAK,WAAW;AACnB,kBAAM,UAAU,YAAW;AAEzB,oBAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAI,CAAC;AAExD,kBAAI,CAAC,KAAK,WAAW;AAEnB,mBAAG,QAAO,EAAG,MAAM,MAAK;AACtB,0BAAO;gBACT,CAAC;cACH;YACF;AACA,oBAAO;UACT;AACA;QAEF,KAAK,gBAAgB;AAGnB,sBAAY,MAAM,KAAK,gBAAe;AAMtC,eAAK,oBAAmB;AACxB,sBAAY,MAAK;AACf,iBAAK,UAAS;UAChB,GAAG,kBAAkB;AAGrB,cAAI,KAAK,kCAAkC;AACzC,iBAAK,qBAAoB;UAC3B;AACA;QAEF,KAAK,gBAAgB;AACnB;MACJ;AAGA,UAAI,KAAK,cAAc,WAAW;AAChC,aAAK,YAAY;MACnB;IACF,CAAC;AACD,OAAG,wBAAwB,CAAC,MAAK;;AAC/B,cAAQ,EAAE,MAAM;QAEd,KAAK;AACH,eAAK,oBAAmB;AACxB;QAGF,KAAK;QACL,KAAK,UAAU;AACb,gBAAM,SAAS,EAAE,SAAS,eAAe,EAAE,SAAS;AACpD,eAAK,SAAS,UAAU,EAAE,eAAe;AACzC;QACF;QAGA,KAAK;QACL,KAAK,wBAAwB;AAC3B,eAAK,6BAA6B,EAAE,QAAQ;AAC5C;QACF;QAEA,KAAK,SAAS;AACZ,eAAK,oBAAoB,CAAC;AAC1B;QACF;MACF;AAGA,UAAI,EAAE,OAAO,QAAW;AACtB,SAAA,KAAA,KAAK,mBAAmB,IAAI,EAAE,EAAE,OAAC,QAAA,OAAA,SAAA,SAAA,GAAG,CAAC;MACvC;IACF,CAAC;AACD,SAAK,KAAK;AAEV,SAAK,OAAO,IAAI,eAAe,YAAY,QAAQ,IAAI,QAAQ,GAAG;EACpE;;;;EAKO,UAAO;AACZ,QAAI,KAAK,WAAW;AAClB,YAAM,IAAI,MAAM,uBAAuB;IACzC;AACA,SAAK,GAAG,QAAO;EACjB;;;;;EAMO,MAAM,UAAO;AAClB,QAAI,KAAK;AAAW;AAEpB,UAAM,KAAK,YACT;MACE,MAAM;MACN,IAAI,UAAU,KAAK,WAAW;MAC9B,WAAW,KAAK,QAAQ;MACxB,UAAU,EAAE,aAAa,IAAG;OAE9B,EAAE,SAAS,IAAI,CAAE;AAGnB,SAAK,YAAY;AACjB,SAAK,GAAG,WAAU;AAClB,SAAK,WAAW;EAClB;EAOA,IAAY,YAAS;AACnB,WAAO,KAAK;EACd;EACA,IAAY,UAAU,WAAkB;AACtC,SAAK,aAAa;EACpB;EAMA,IAAY,SAAM;AAChB,WAAO,KAAK;EACd;EACA,IAAY,OAAO,QAAe;;AAChC,SAAK,UAAU;AACf,QAAI;AAAQ,OAAA,KAAA,KAAK,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAA;AAC3B,KAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,cAAc,MAAM;EACrC;EAMQ,cAAiB,UAA0B;AACjD,WAAO,IAAI,QAAW,CAAC,YAAW;AAChC,UAAI,KAAK,QAAQ;AACf,iBAAQ,EAAG,KAAK,OAAO;MACzB,OAAO;AACL,aAAK,aAAa,MAAK;AACrB,mBAAQ,EAAG,KAAK,OAAO;AACvB,eAAK,aAAa;QACpB;MACF;IACF,CAAC;EACH;EAEQ,MAAM,oBAAoB,GAAgB;;AAChD,QAAI,EAAE,SAAS,WAAW,EAAE,UAAU,gBAAgB;AACpD;IACF;AAEA,UAAM,gBAAgB,MAAM,KAAK,OAAO,QAAQ,EAAE,IAAI;AACtD,UAAM,UAA+B,KAAK,MAAM,aAAa;AAE7D,QAAI,QAAQ,SAAS;AAAiB;AAEtC,UAAM,EAAE,IAAI,SAAQ,IAAK;AACzB,KAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAE,0BAA0B,IAAI,QAAQ;EACvD;EAIO,MAAM,oBAAiB;AAC5B,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,mCAAmC;AACxC;IACF;AAEA,UAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAG,CAAC;AACvD,QAAI;AACF,YAAM,KAAK,qBAAoB;IACjC,SAAS,GAAG;AACV,cAAQ,MAAM,qCAAqC,CAAC;IACtD;EACF;EAEQ,MAAM,uBAAoB;AAChC,SAAK,mCAAmC;AAExC,UAAM,iBAAiB,MAAM,KAAK,KAAK,kBAAiB;AACxD,mBAAe,QAAQ,CAAC,MAAM,KAAK,oBAAoB,CAAC,CAAC;EAC3D;;;;;;;;EASO,MAAM,aACX,OACA,iBACA,cAAc,OAAK;AAEnB,UAAM,OAAO,MAAM,KAAK,OAAO,QAC7B,KAAK,UAAS,OAAA,OAAA,OAAA,OAAA,CAAA,GACT,eAAe,GAAA,EAClB,QAAQ,SAAS,QACjB,UAAU,SAAS,MACnB,aACE,6BAA6B,UAAU,OAAO,0BAC1C,iBACA,MAAK,CAAA,CAAA,CACX;AAGJ,UAAM,UAAyB;MAC7B,MAAM;MACN,IAAI,UAAU,KAAK,WAAW;MAC9B,WAAW,KAAK,QAAQ;MACxB;MACA;MACA;;AAGF,WAAO,KAAK,cAAc,YAAW;AACnC,YAAM,MAAM,MAAM,KAAK,YAAuC,OAAO;AACrE,UAAI,IAAI,SAAS,QAAQ;AACvB,cAAM,IAAI,MAAM,IAAI,SAAS,yBAAyB;MACxD;AACA,aAAO,IAAI;IACb,CAAC;EACH;EAEQ,SAAS,SAAsB;AACrC,SAAK,GAAG,SAAS,KAAK,UAAU,OAAO,CAAC;EAC1C;EAEQ,sBAAmB;AACzB,SAAK,wBAAwB,KAAK,IAAG;EACvC;EAEQ,YAAS;AACf,QAAI,KAAK,IAAG,IAAK,KAAK,wBAAwB,qBAAqB,GAAG;AACpE,WAAK,GAAG,WAAU;AAClB;IACF;AACA,QAAI;AACF,WAAK,GAAG,SAAS,GAAG;IACtB,SAAE,IAAM;IAER;EACF;EAIQ,MAAM,YACZ,SACA,UAA+B,EAAE,SAAS,gBAAe,GAAE;AAE3D,UAAM,QAAQ,QAAQ;AACtB,SAAK,SAAS,OAAO;AAGrB,QAAI;AACJ,WAAO,QAAQ,KAAK;MAClB,IAAI,QAAW,CAACE,IAAG,WAAU;AAC3B,oBAAY,OAAO,WAAW,MAAK;AACjC,iBAAO,IAAI,MAAM,WAAW,KAAK,YAAY,CAAC;QAChD,GAAG,QAAQ,OAAO;MACpB,CAAC;MACD,IAAI,QAAW,CAAC,YAAW;AACzB,aAAK,mBAAmB,IAAI,OAAO,CAAC,MAAK;AACvC,uBAAa,SAAS;AACtB,kBAAQ,CAAM;AACd,eAAK,mBAAmB,OAAO,KAAK;QACtC,CAAC;MACH,CAAC;KACF;EACH;EAEQ,MAAM,kBAAe;AAC3B,UAAM,MAAM,MAAM,KAAK,YAA2B;MAChD,MAAM;MACN,IAAI,UAAU,KAAK,WAAW;MAC9B,WAAW,KAAK,QAAQ;MACxB,YAAY,KAAK,QAAQ;KAC1B;AACD,QAAI,IAAI,SAAS;AAAQ,aAAO;AAEhC,SAAK,SAAS;MACZ,MAAM;MACN,IAAI,UAAU,KAAK,WAAW;MAC9B,WAAW,KAAK,QAAQ;KACzB;AAED,SAAK,SAAS;MACZ,MAAM;MACN,IAAI,UAAU,KAAK,WAAW;MAC9B,WAAW,KAAK,QAAQ;KACzB;AAED,WAAO;EACT;;;;AC7WI,IAAO,oBAAP,MAAwB;EAA9B,cAAA;AACE,SAAA,iBAAiB;AACjB,SAAA,YAAY,oBAAI,IAAG;EAcrB;EAZS,gBAAa;AAElB,SAAK,kBAAkB,KAAK,iBAAiB,KAAK;AAClD,UAAM,KAAK,KAAK;AAChB,UAAM,QAAQ,UAAU,GAAG,SAAS,EAAE,CAAC;AAEvC,UAAM,WAAW,KAAK,UAAU,IAAI,KAAK;AACzC,QAAI,UAAU;AACZ,WAAK,UAAU,OAAO,KAAK;IAC7B;AACA,WAAO;EACT;;;;ACbK,IAAMC,UACX,OAAO,eAAe,YAAY,YAAY,aAAa,WAAW,SAAS;;;ACO3E,SAAU,QAAQ,GAAU;AAChC,SAAO,aAAa,cAAe,YAAY,OAAO,CAAC,KAAK,EAAE,YAAY,SAAS;AACrF;AAQM,SAAU,OAAO,MAA8B,SAAiB;AACpE,MAAI,CAAC,QAAQ,CAAC;AAAG,UAAM,IAAI,MAAM,qBAAqB;AACtD,MAAI,QAAQ,SAAS,KAAK,CAAC,QAAQ,SAAS,EAAE,MAAM;AAClD,UAAM,IAAI,MAAM,mCAAmC,UAAU,kBAAkB,EAAE,MAAM;AAC3F;AAWM,SAAU,QAAQ,UAAe,gBAAgB,MAAI;AACzD,MAAI,SAAS;AAAW,UAAM,IAAI,MAAM,kCAAkC;AAC1E,MAAI,iBAAiB,SAAS;AAAU,UAAM,IAAI,MAAM,uCAAuC;AACjG;AAGM,SAAU,QAAQ,KAAU,UAAa;AAC7C,SAAO,GAAG;AACV,QAAM,MAAM,SAAS;AACrB,MAAI,IAAI,SAAS,KAAK;AACpB,UAAM,IAAI,MAAM,2DAA2D,GAAG;EAChF;AACF;AAkBM,SAAU,SAAS,QAAoB;AAC3C,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAO,CAAC,EAAE,KAAK,CAAC;EAClB;AACF;AAGM,SAAU,WAAW,KAAe;AACxC,SAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,YAAY,IAAI,UAAU;AAChE;AAGM,SAAU,KAAK,MAAc,OAAa;AAC9C,SAAQ,QAAS,KAAK,QAAW,SAAS;AAC5C;AAQO,IAAM,QAAiC,MAC5C,IAAI,WAAW,IAAI,YAAY,CAAC,SAAU,CAAC,EAAE,MAAM,EAAE,CAAC,MAAM,IAAK;AA+BnE,IAAM,iBAA0C;;EAE9C,OAAO,WAAW,KAAK,CAAA,CAAE,EAAE,UAAU,cAAc,OAAO,WAAW,YAAY;GAAW;AAG9F,IAAM,QAAwB,MAAM,KAAK,EAAE,QAAQ,IAAG,GAAI,CAACC,IAAG,MAC5D,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC;AAO3B,SAAU,WAAW,OAAiB;AAC1C,SAAO,KAAK;AAEZ,MAAI;AAAe,WAAO,MAAM,MAAK;AAErC,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,WAAO,MAAM,MAAM,CAAC,CAAC;EACvB;AACA,SAAO;AACT;AAmEM,SAAU,YAAY,KAAW;AACrC,MAAI,OAAO,QAAQ;AAAU,UAAM,IAAI,MAAM,iBAAiB;AAC9D,SAAO,IAAI,WAAW,IAAI,YAAW,EAAG,OAAO,GAAG,CAAC;AACrD;AAiBM,SAAU,QAAQ,MAAW;AACjC,MAAI,OAAO,SAAS;AAAU,WAAO,YAAY,IAAI;AACrD,SAAO,IAAI;AACX,SAAO;AACT;AAmDM,IAAgB,OAAhB,MAAoB;;AA4CpB,SAAU,aACd,UAAuB;AAOvB,QAAM,QAAQ,CAAC,QAA2B,SAAQ,EAAG,OAAO,QAAQ,GAAG,CAAC,EAAE,OAAM;AAChF,QAAM,MAAM,SAAQ;AACpB,QAAM,YAAY,IAAI;AACtB,QAAM,WAAW,IAAI;AACrB,QAAM,SAAS,MAAM,SAAQ;AAC7B,SAAO;AACT;;;ACpVM,SAAU,aACd,MACA,YACA,OACAC,OAAa;AAEb,MAAI,OAAO,KAAK,iBAAiB;AAAY,WAAO,KAAK,aAAa,YAAY,OAAOA,KAAI;AAC7F,QAAMC,QAAO,OAAO,EAAE;AACtB,QAAM,WAAW,OAAO,UAAU;AAClC,QAAM,KAAK,OAAQ,SAASA,QAAQ,QAAQ;AAC5C,QAAM,KAAK,OAAO,QAAQ,QAAQ;AAClC,QAAM,IAAID,QAAO,IAAI;AACrB,QAAM,IAAIA,QAAO,IAAI;AACrB,OAAK,UAAU,aAAa,GAAG,IAAIA,KAAI;AACvC,OAAK,UAAU,aAAa,GAAG,IAAIA,KAAI;AACzC;AAGM,SAAU,IAAI,GAAW,GAAW,GAAS;AACjD,SAAQ,IAAI,IAAM,CAAC,IAAI;AACzB;AAGM,SAAU,IAAI,GAAW,GAAW,GAAS;AACjD,SAAQ,IAAI,IAAM,IAAI,IAAM,IAAI;AAClC;AAMM,IAAgB,SAAhB,cAAoD,KAAO;EAoB/D,YAAY,UAAkB,WAAmB,WAAmBA,OAAa;AAC/E,UAAK;AANG,SAAA,WAAW;AACX,SAAA,SAAS;AACT,SAAA,MAAM;AACN,SAAA,YAAY;AAIpB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,OAAOA;AACZ,SAAK,SAAS,IAAI,WAAW,QAAQ;AACrC,SAAK,OAAO,WAAW,KAAK,MAAM;EACpC;EACA,OAAO,MAAW;AAChB,YAAQ,IAAI;AACZ,WAAO,QAAQ,IAAI;AACnB,WAAO,IAAI;AACX,UAAM,EAAE,MAAM,QAAQ,SAAQ,IAAK;AACnC,UAAM,MAAM,KAAK;AACjB,aAAS,MAAM,GAAG,MAAM,OAAO;AAC7B,YAAM,OAAO,KAAK,IAAI,WAAW,KAAK,KAAK,MAAM,GAAG;AAEpD,UAAI,SAAS,UAAU;AACrB,cAAM,WAAW,WAAW,IAAI;AAChC,eAAO,YAAY,MAAM,KAAK,OAAO;AAAU,eAAK,QAAQ,UAAU,GAAG;AACzE;MACF;AACA,aAAO,IAAI,KAAK,SAAS,KAAK,MAAM,IAAI,GAAG,KAAK,GAAG;AACnD,WAAK,OAAO;AACZ,aAAO;AACP,UAAI,KAAK,QAAQ,UAAU;AACzB,aAAK,QAAQ,MAAM,CAAC;AACpB,aAAK,MAAM;MACb;IACF;AACA,SAAK,UAAU,KAAK;AACpB,SAAK,WAAU;AACf,WAAO;EACT;EACA,WAAW,KAAe;AACxB,YAAQ,IAAI;AACZ,YAAQ,KAAK,IAAI;AACjB,SAAK,WAAW;AAIhB,UAAM,EAAE,QAAQ,MAAM,UAAU,MAAAA,MAAI,IAAK;AACzC,QAAI,EAAE,IAAG,IAAK;AAEd,WAAO,KAAK,IAAI;AAChB,UAAM,KAAK,OAAO,SAAS,GAAG,CAAC;AAG/B,QAAI,KAAK,YAAY,WAAW,KAAK;AACnC,WAAK,QAAQ,MAAM,CAAC;AACpB,YAAM;IACR;AAEA,aAAS,IAAI,KAAK,IAAI,UAAU;AAAK,aAAO,CAAC,IAAI;AAIjD,iBAAa,MAAM,WAAW,GAAG,OAAO,KAAK,SAAS,CAAC,GAAGA,KAAI;AAC9D,SAAK,QAAQ,MAAM,CAAC;AACpB,UAAM,QAAQ,WAAW,GAAG;AAC5B,UAAM,MAAM,KAAK;AAEjB,QAAI,MAAM;AAAG,YAAM,IAAI,MAAM,6CAA6C;AAC1E,UAAM,SAAS,MAAM;AACrB,UAAM,QAAQ,KAAK,IAAG;AACtB,QAAI,SAAS,MAAM;AAAQ,YAAM,IAAI,MAAM,oCAAoC;AAC/E,aAAS,IAAI,GAAG,IAAI,QAAQ;AAAK,YAAM,UAAU,IAAI,GAAG,MAAM,CAAC,GAAGA,KAAI;EACxE;EACA,SAAM;AACJ,UAAM,EAAE,QAAQ,UAAS,IAAK;AAC9B,SAAK,WAAW,MAAM;AACtB,UAAM,MAAM,OAAO,MAAM,GAAG,SAAS;AACrC,SAAK,QAAO;AACZ,WAAO;EACT;EACA,WAAW,IAAM;AACf,WAAA,KAAO,IAAK,KAAK,YAAmB;AACpC,OAAG,IAAI,GAAG,KAAK,IAAG,CAAE;AACpB,UAAM,EAAE,UAAU,QAAQ,QAAQ,UAAU,WAAW,IAAG,IAAK;AAC/D,OAAG,YAAY;AACf,OAAG,WAAW;AACd,OAAG,SAAS;AACZ,OAAG,MAAM;AACT,QAAI,SAAS;AAAU,SAAG,OAAO,IAAI,MAAM;AAC3C,WAAO;EACT;EACA,QAAK;AACH,WAAO,KAAK,WAAU;EACxB;;AASK,IAAM,YAAyC,YAAY,KAAK;EACrE;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAGM,IAAM,YAAyC,YAAY,KAAK;EACrE;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAGM,IAAM,YAAyC,YAAY,KAAK;EACrE;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAGM,IAAM,YAAyC,YAAY,KAAK;EACrE;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;;;AC1KD,IAAM,aAA6B,OAAO,KAAK,KAAK,CAAC;AACrD,IAAM,OAAuB,OAAO,EAAE;AAEtC,SAAS,QACP,GACA,KAAK,OAAK;AAKV,MAAI;AAAI,WAAO,EAAE,GAAG,OAAO,IAAI,UAAU,GAAG,GAAG,OAAQ,KAAK,OAAQ,UAAU,EAAC;AAC/E,SAAO,EAAE,GAAG,OAAQ,KAAK,OAAQ,UAAU,IAAI,GAAG,GAAG,OAAO,IAAI,UAAU,IAAI,EAAC;AACjF;AAEA,SAAS,MAAM,KAAe,KAAK,OAAK;AACtC,QAAM,MAAM,IAAI;AAChB,MAAI,KAAK,IAAI,YAAY,GAAG;AAC5B,MAAI,KAAK,IAAI,YAAY,GAAG;AAC5B,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,UAAM,EAAE,GAAG,EAAC,IAAK,QAAQ,IAAI,CAAC,GAAG,EAAE;AACnC,KAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;EACxB;AACA,SAAO,CAAC,IAAI,EAAE;AAChB;AAIA,IAAM,QAAQ,CAAC,GAAW,IAAY,MAAsB,MAAM;AAClE,IAAM,QAAQ,CAAC,GAAW,GAAW,MAAuB,KAAM,KAAK,IAAO,MAAM;AAEpF,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,MAAM,IAAM,KAAM,KAAK;AACpF,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,KAAK,IAAO,MAAM;AAErF,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,KAAM,KAAK,IAAO,MAAO,IAAI;AAC1F,IAAM,SAAS,CAAC,GAAW,GAAW,MAAuB,MAAO,IAAI,KAAQ,KAAM,KAAK;AAa3F,SAAS,IACP,IACA,IACA,IACA,IAAU;AAKV,QAAM,KAAK,OAAO,MAAM,OAAO;AAC/B,SAAO,EAAE,GAAI,KAAK,MAAO,IAAI,KAAK,KAAM,KAAM,GAAG,GAAG,IAAI,EAAC;AAC3D;AAEA,IAAM,QAAQ,CAAC,IAAY,IAAY,QAAwB,OAAO,MAAM,OAAO,MAAM,OAAO;AAChG,IAAM,QAAQ,CAAC,KAAa,IAAY,IAAY,OACjD,KAAK,KAAK,MAAO,MAAM,KAAK,KAAM,KAAM;AAC3C,IAAM,QAAQ,CAAC,IAAY,IAAY,IAAY,QAChD,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO;AACjD,IAAM,QAAQ,CAAC,KAAa,IAAY,IAAY,IAAY,OAC7D,KAAK,KAAK,KAAK,MAAO,MAAM,KAAK,KAAM,KAAM;AAChD,IAAM,QAAQ,CAAC,IAAY,IAAY,IAAY,IAAY,QAC5D,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO,MAAM,OAAO;AAC9D,IAAM,QAAQ,CAAC,KAAa,IAAY,IAAY,IAAY,IAAY,OACzE,KAAK,KAAK,KAAK,KAAK,MAAO,MAAM,KAAK,KAAM,KAAM;;;AC3DrD,IAAM,WAA2B,YAAY,KAAK;EAChD;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAGD,IAAM,WAA2B,IAAI,YAAY,EAAE;AAC7C,IAAO,SAAP,cAAsB,OAAc;EAYxC,YAAY,YAAoB,IAAE;AAChC,UAAM,IAAI,WAAW,GAAG,KAAK;AAVrB,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;EAIrC;EACU,MAAG;AACX,UAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAAE,IAAG,GAAG,GAAG,EAAC,IAAK;AACnC,WAAO,CAAC,GAAG,GAAG,GAAG,GAAGA,IAAG,GAAG,GAAG,CAAC;EAChC;;EAEU,IACR,GAAW,GAAW,GAAW,GAAWA,IAAW,GAAW,GAAW,GAAS;AAEtF,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAIA,KAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;AACb,SAAK,IAAI,IAAI;EACf;EACU,QAAQ,MAAgB,QAAc;AAE9C,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,UAAU;AAAG,eAAS,CAAC,IAAI,KAAK,UAAU,QAAQ,KAAK;AACpF,aAAS,IAAI,IAAI,IAAI,IAAI,KAAK;AAC5B,YAAM,MAAM,SAAS,IAAI,EAAE;AAC3B,YAAM,KAAK,SAAS,IAAI,CAAC;AACzB,YAAM,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,IAAK,QAAQ;AACnD,YAAM,KAAK,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,IAAK,OAAO;AACjD,eAAS,CAAC,IAAK,KAAK,SAAS,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,EAAE,IAAK;IACjE;AAEA,QAAI,EAAE,GAAG,GAAG,GAAG,GAAG,GAAAA,IAAG,GAAG,GAAG,EAAC,IAAK;AACjC,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,YAAM,SAAS,KAAKA,IAAG,CAAC,IAAI,KAAKA,IAAG,EAAE,IAAI,KAAKA,IAAG,EAAE;AACpD,YAAM,KAAM,IAAI,SAAS,IAAIA,IAAG,GAAG,CAAC,IAAI,SAAS,CAAC,IAAI,SAAS,CAAC,IAAK;AACrE,YAAM,SAAS,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,EAAE;AACpD,YAAM,KAAM,SAAS,IAAI,GAAG,GAAG,CAAC,IAAK;AACrC,UAAI;AACJ,UAAI;AACJ,UAAIA;AACJ,MAAAA,KAAK,IAAI,KAAM;AACf,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAK,KAAK,KAAM;IAClB;AAEA,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,IAAAA,KAAKA,KAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,QAAK,IAAI,KAAK,IAAK;AACnB,SAAK,IAAI,GAAG,GAAG,GAAG,GAAGA,IAAG,GAAG,GAAG,CAAC;EACjC;EACU,aAAU;AAClB,UAAM,QAAQ;EAChB;EACA,UAAO;AACL,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC/B,UAAM,KAAK,MAAM;EACnB;;AAGI,IAAO,SAAP,cAAsB,OAAM;EAShC,cAAA;AACE,UAAM,EAAE;AATA,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;AAC3B,SAAA,IAAY,UAAU,CAAC,IAAI;EAGrC;;AAQF,IAAM,QAAwB,MAAU,MAAM;EAC5C;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE;EAAsB;EAAsB;EAAsB;EAClE,IAAI,OAAK,OAAO,CAAC,CAAC,CAAC,GAAE;AACvB,IAAM,aAA6B,MAAM,KAAK,CAAC,GAAE;AACjD,IAAM,aAA6B,MAAM,KAAK,CAAC,GAAE;AAGjD,IAAM,aAA6B,IAAI,YAAY,EAAE;AACrD,IAAM,aAA6B,IAAI,YAAY,EAAE;AAE/C,IAAO,SAAP,cAAsB,OAAc;EAqBxC,YAAY,YAAoB,IAAE;AAChC,UAAM,KAAK,WAAW,IAAI,KAAK;AAlBvB,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,EAAE,IAAI;AAC7B,SAAA,KAAa,UAAU,EAAE,IAAI;AAC7B,SAAA,KAAa,UAAU,EAAE,IAAI;AAC7B,SAAA,KAAa,UAAU,EAAE,IAAI;AAC7B,SAAA,KAAa,UAAU,EAAE,IAAI;AAC7B,SAAA,KAAa,UAAU,EAAE,IAAI;EAIvC;;EAEU,MAAG;AAIX,UAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AAC3E,WAAO,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACxE;;EAEU,IACR,IAAY,IAAY,IAAY,IAAY,IAAY,IAAY,IAAY,IACpF,IAAY,IAAY,IAAY,IAAY,IAAY,IAAY,IAAY,IAAU;AAE9F,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;EACjB;EACU,QAAQ,MAAgB,QAAc;AAE9C,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK,UAAU,GAAG;AACxC,iBAAW,CAAC,IAAI,KAAK,UAAU,MAAM;AACrC,iBAAW,CAAC,IAAI,KAAK,UAAW,UAAU,CAAE;IAC9C;AACA,aAAS,IAAI,IAAI,IAAI,IAAI,KAAK;AAE5B,YAAM,OAAO,WAAW,IAAI,EAAE,IAAI;AAClC,YAAM,OAAO,WAAW,IAAI,EAAE,IAAI;AAClC,YAAM,MAAU,OAAO,MAAM,MAAM,CAAC,IAAQ,OAAO,MAAM,MAAM,CAAC,IAAQ,MAAM,MAAM,MAAM,CAAC;AAC3F,YAAM,MAAU,OAAO,MAAM,MAAM,CAAC,IAAQ,OAAO,MAAM,MAAM,CAAC,IAAQ,MAAM,MAAM,MAAM,CAAC;AAE3F,YAAM,MAAM,WAAW,IAAI,CAAC,IAAI;AAChC,YAAM,MAAM,WAAW,IAAI,CAAC,IAAI;AAChC,YAAM,MAAU,OAAO,KAAK,KAAK,EAAE,IAAQ,OAAO,KAAK,KAAK,EAAE,IAAQ,MAAM,KAAK,KAAK,CAAC;AACvF,YAAM,MAAU,OAAO,KAAK,KAAK,EAAE,IAAQ,OAAO,KAAK,KAAK,EAAE,IAAQ,MAAM,KAAK,KAAK,CAAC;AAEvF,YAAM,OAAW,MAAM,KAAK,KAAK,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,EAAE,CAAC;AACtE,YAAM,OAAW,MAAM,MAAM,KAAK,KAAK,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,EAAE,CAAC;AAC5E,iBAAW,CAAC,IAAI,OAAO;AACvB,iBAAW,CAAC,IAAI,OAAO;IACzB;AACA,QAAI,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAE,IAAK;AAEzE,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAE3B,YAAM,UAAc,OAAO,IAAI,IAAI,EAAE,IAAQ,OAAO,IAAI,IAAI,EAAE,IAAQ,OAAO,IAAI,IAAI,EAAE;AACvF,YAAM,UAAc,OAAO,IAAI,IAAI,EAAE,IAAQ,OAAO,IAAI,IAAI,EAAE,IAAQ,OAAO,IAAI,IAAI,EAAE;AAEvF,YAAM,OAAQ,KAAK,KAAO,CAAC,KAAK;AAChC,YAAM,OAAQ,KAAK,KAAO,CAAC,KAAK;AAGhC,YAAM,OAAW,MAAM,IAAI,SAAS,MAAM,UAAU,CAAC,GAAG,WAAW,CAAC,CAAC;AACrE,YAAM,MAAU,MAAM,MAAM,IAAI,SAAS,MAAM,UAAU,CAAC,GAAG,WAAW,CAAC,CAAC;AAC1E,YAAM,MAAM,OAAO;AAEnB,YAAM,UAAc,OAAO,IAAI,IAAI,EAAE,IAAQ,OAAO,IAAI,IAAI,EAAE,IAAQ,OAAO,IAAI,IAAI,EAAE;AACvF,YAAM,UAAc,OAAO,IAAI,IAAI,EAAE,IAAQ,OAAO,IAAI,IAAI,EAAE,IAAQ,OAAO,IAAI,IAAI,EAAE;AACvF,YAAM,OAAQ,KAAK,KAAO,KAAK,KAAO,KAAK;AAC3C,YAAM,OAAQ,KAAK,KAAO,KAAK,KAAO,KAAK;AAC3C,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,OAAC,EAAE,GAAG,IAAI,GAAG,GAAE,IAAS,IAAI,KAAK,GAAG,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC;AAC5D,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,WAAK,KAAK;AACV,YAAM,MAAU,MAAM,KAAK,SAAS,IAAI;AACxC,WAAS,MAAM,KAAK,KAAK,SAAS,IAAI;AACtC,WAAK,MAAM;IACb;AAEA,KAAC,EAAE,GAAG,IAAI,GAAG,GAAE,IAAS,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpE,KAAC,EAAE,GAAG,IAAI,GAAG,GAAE,IAAS,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpE,KAAC,EAAE,GAAG,IAAI,GAAG,GAAE,IAAS,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpE,KAAC,EAAE,GAAG,IAAI,GAAG,GAAE,IAAS,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpE,KAAC,EAAE,GAAG,IAAI,GAAG,GAAE,IAAS,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpE,KAAC,EAAE,GAAG,IAAI,GAAG,GAAE,IAAS,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpE,KAAC,EAAE,GAAG,IAAI,GAAG,GAAE,IAAS,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpE,KAAC,EAAE,GAAG,IAAI,GAAG,GAAE,IAAS,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AACpE,SAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;EACzE;EACU,aAAU;AAClB,UAAM,YAAY,UAAU;EAC9B;EACA,UAAO;AACL,UAAM,KAAK,MAAM;AACjB,SAAK,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;EACzD;;AAGI,IAAO,SAAP,cAAsB,OAAM;EAkBhC,cAAA;AACE,UAAM,EAAE;AAlBA,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,CAAC,IAAI;AAC5B,SAAA,KAAa,UAAU,EAAE,IAAI;AAC7B,SAAA,KAAa,UAAU,EAAE,IAAI;AAC7B,SAAA,KAAa,UAAU,EAAE,IAAI;AAC7B,SAAA,KAAa,UAAU,EAAE,IAAI;AAC7B,SAAA,KAAa,UAAU,EAAE,IAAI;AAC7B,SAAA,KAAa,UAAU,EAAE,IAAI;EAIvC;;AAWF,IAAM,UAA0B,YAAY,KAAK;EAC/C;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAGD,IAAM,UAA0B,YAAY,KAAK;EAC/C;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EACpF;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;EAAY;CACrF;AAEK,IAAO,aAAP,cAA0B,OAAM;EAkBpC,cAAA;AACE,UAAM,EAAE;AAlBA,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,EAAE,IAAI;AAC3B,SAAA,KAAa,QAAQ,EAAE,IAAI;AAC3B,SAAA,KAAa,QAAQ,EAAE,IAAI;AAC3B,SAAA,KAAa,QAAQ,EAAE,IAAI;AAC3B,SAAA,KAAa,QAAQ,EAAE,IAAI;AAC3B,SAAA,KAAa,QAAQ,EAAE,IAAI;EAIrC;;AAGI,IAAO,aAAP,cAA0B,OAAM;EAkBpC,cAAA;AACE,UAAM,EAAE;AAlBA,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,CAAC,IAAI;AAC1B,SAAA,KAAa,QAAQ,EAAE,IAAI;AAC3B,SAAA,KAAa,QAAQ,EAAE,IAAI;AAC3B,SAAA,KAAa,QAAQ,EAAE,IAAI;AAC3B,SAAA,KAAa,QAAQ,EAAE,IAAI;AAC3B,SAAA,KAAa,QAAQ,EAAE,IAAI;AAC3B,SAAA,KAAa,QAAQ,EAAE,IAAI;EAIrC;;AAUK,IAAM,SAAgC,aAAa,MAAM,IAAI,OAAM,CAAE;AAErE,IAAM,SAAgC,aAAa,MAAM,IAAI,OAAM,CAAE;AAGrE,IAAM,SAAgC,aAAa,MAAM,IAAI,OAAM,CAAE;AAErE,IAAM,SAAgC,aAAa,MAAM,IAAI,OAAM,CAAE;AAMrE,IAAM,aAAoC,aAAa,MAAM,IAAI,WAAU,CAAE;AAK7E,IAAM,aAAoC,aAAa,MAAM,IAAI,WAAU,CAAE;;;AC9X7E,IAAMC,UAAyB;;;ACXtC,IAAM,yBAAyB;AAC/B,IAAM,6BAA6B;AACnC,IAAM,6BAA6B;AAE7B,IAAO,oBAAP,MAAO,mBAAiB;EAI5B,YACWC,UACA,IACA,QACT,SAAS,OAAK;AAHL,SAAA,UAAAA;AACA,SAAA,KAAA;AACA,SAAA,SAAA;AAGT,SAAK,MAAM,WAAWC,QAAO,GAAG,EAAE,KAAK,MAAM,aAAa,CAAC;AAC3D,SAAK,UAAU,CAAC,CAAC;EACnB;EAEO,OAAO,OAAOD,UAA2B;AAC9C,UAAM,KAAK,eAAe,EAAE;AAC5B,UAAM,SAAS,eAAe,EAAE;AAChC,WAAO,IAAI,mBAAkBA,UAAS,IAAI,MAAM,EAAE,KAAI;EACxD;EAEO,OAAO,KAAKA,UAA2B;AAC5C,UAAM,KAAKA,SAAQ,QAAQ,sBAAsB;AACjD,UAAM,SAASA,SAAQ,QAAQ,0BAA0B;AACzD,UAAM,SAASA,SAAQ,QAAQ,0BAA0B;AAEzD,QAAI,MAAM,QAAQ;AAChB,aAAO,IAAI,mBAAkBA,UAAS,IAAI,QAAQ,WAAW,GAAG;IAClE;AAEA,WAAO;EACT;EAEA,IAAW,SAAM;AACf,WAAO,KAAK;EACd;EAEA,IAAW,OAAO,KAAY;AAC5B,SAAK,UAAU;AACf,SAAK,cAAa;EACpB;EAEO,OAAI;AACT,SAAK,QAAQ,QAAQ,wBAAwB,KAAK,EAAE;AACpD,SAAK,QAAQ,QAAQ,4BAA4B,KAAK,MAAM;AAC5D,SAAK,cAAa;AAClB,WAAO;EACT;EAEQ,gBAAa;AACnB,SAAK,QAAQ,QAAQ,4BAA4B,KAAK,UAAU,MAAM,GAAG;EAC3E;;;;ACvCF,SAAS,aAAU;AACjB,MAAI;AACF,WAAO,OAAO,iBAAiB;EACjC,SAAS,GAAG;AACV,WAAO;EACT;AACF;AAEM,SAAU,cAAW;AACzB,MAAI;AACF,QAAI,WAAU,KAAM,OAAO,KAAK;AAC9B,aAAO,OAAO,IAAI;IACpB;AACA,WAAO,OAAO;EAChB,SAAS,GAAG;AACV,WAAO,OAAO;EAChB;AACF;AAEM,SAAU,cAAW;;AACzB,SAAO,iEAAiE,MACtE,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS;AAEhC;AAEM,SAAU,aAAU;;AACxB,UAAO,MAAA,KAAA,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,QAAG,8BAA8B,EAAE,aAAO,QAAA,OAAA,SAAA,KAAI;AACzE;;;AClDA,IAAA,uBAAgB,uBAAM,6nGAA4nG;;;ACI5oG,SAAU,iBAAc;AAC5B,QAAM,UAAU,SAAS,cAAc,OAAO;AAC9C,UAAQ,OAAO;AACf,UAAQ,YAAY,SAAS,eAAe,oBAAG,CAAC;AAChD,WAAS,gBAAgB,YAAY,OAAO;AAC9C;;;ACTA,SAAS,EAAE,GAAE;AAAC,MAAI,GAAE,GAAE,IAAE;AAAG,MAAG,YAAU,OAAO,KAAG,YAAU,OAAO,EAAE,MAAG;AAAA,WAAU,YAAU,OAAO,EAAE,KAAG,MAAM,QAAQ,CAAC,EAAE,MAAI,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,GAAE,CAAC,MAAI,IAAE,EAAE,EAAE,CAAC,CAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAA,MAAQ,MAAI,KAAK,EAAE,GAAE,CAAC,MAAI,MAAI,KAAG,MAAK,KAAG;AAAG,SAAO;AAAC;AAAQ,SAAS,OAAM;AAAC,WAAQ,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,SAAQ,EAAC,IAAE,UAAU,GAAG,OAAK,IAAE,EAAE,CAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAG,SAAO;AAAC;;;ACGjW;AACA;;;ACJA,IAAA,uBAAgB,uBAAM,goGAA+nG;;;ADSrpG,IAAM,SAAS;AACf,IAAM,WAAW;AAmBX,IAAO,WAAP,MAAe;EAOnB,cAAA;AALiB,SAAA,QAAQ,oBAAI,IAAG;AAExB,SAAA,cAAc;AACd,SAAA,OAAuB;AAG7B,SAAK,WAAW,WAAU;EAC5B;EAEO,OAAO,IAAW;AACvB,SAAK,OAAO,SAAS,cAAc,KAAK;AAExC,SAAK,KAAK,YAAY;AACtB,OAAG,YAAY,KAAK,IAAI;AAExB,SAAK,OAAM;EACb;EAEO,YAAY,WAAgC;AACjD,UAAM,MAAM,KAAK;AACjB,SAAK,MAAM,IAAI,KAAK,SAAS;AAC7B,SAAK,OAAM;AAEX,WAAO,MAAK;AACV,WAAK,MAAM,OAAO,GAAG;AACrB,WAAK,OAAM;IACb;EACF;EAEO,QAAK;AACV,SAAK,MAAM,MAAK;AAChB,SAAK,OAAM;EACb;EAEQ,SAAM;AACZ,QAAI,CAAC,KAAK,MAAM;AACd;IACF;AACA,MACE;MAAA;MAAA;MACE,EAAC,mBAAiB,EAAC,UAAU,KAAK,SAAQ,GACvC,MAAM,KAAK,KAAK,MAAM,QAAO,CAAE,EAAE,IAAI,CAAC,CAAC,KAAK,SAAS,MACpD,EAAC,kBAAgB,OAAA,OAAA,CAAA,GAAK,WAAS,EAAE,IAAQ,CAAA,CAAA,CAC1C,CAAC;IACgB,GAEtB,KAAK,IAAI;EAEb;;AAGK,IAAM,oBAER,CAAC,UACJ;EAAA;EAAA,EAAK,OAAO,KAAK,4BAA4B,EAAC;EAC5C,EAAA,SAAA,MAAQ,oBAAG;EACX,EAAA,OAAA,EAAK,OAAM,mBAAkB,GAAE,MAAM,QAAQ;AAAO;AAIjD,IAAM,mBAA6D,CAAC,EACzE,YACA,SACA,UAAS,MACN;AACH,QAAM,CAAC,QAAQ,SAAS,IAAI,EAAS,IAAI;AACzC,QAAM,CAAC,UAAU,WAAW,IAAI,EAAS,eAAU,QAAV,eAAU,SAAV,aAAc,KAAK;AAE5D,IAAU,MAAK;AACb,UAAM,SAAS;MACb,OAAO,WAAW,MAAK;AACrB,kBAAU,KAAK;MACjB,GAAG,CAAC;MACJ,OAAO,WAAW,MAAK;AACrB,oBAAY,IAAI;MAClB,GAAG,GAAK;;AAGV,WAAO,MAAK;AACV,aAAO,QAAQ,OAAO,YAAY;IACpC;EACF,CAAC;AAED,QAAM,iBAAiB,MAAK;AAC1B,gBAAY,CAAC,QAAQ;EACvB;AAEA,SACE;IAAA;IAAA,EACE,OAAO,KACL,6BACA,UAAU,oCACV,YAAY,oCAAoC,EACjD;IAED;MAAA;MAAA,EAAK,OAAM,oCAAmC,SAAS,eAAc;MACnE,EAAA,OAAA,EAAK,KAAK,QAAQ,OAAM,0CAAyC,CAAA;MAAI;MACrE,EAAA,OAAA,EAAK,OAAM,2CAA0C,GAAE,OAAO;MAC9D;QAAA;QAAA,EAAK,OAAM,kBAAiB;QACzB,CAAC,YACA;UAAA;UAAA,EACE,OAAM,MACN,QAAO,MACP,SAAQ,aACR,MAAK,QACL,OAAM,6BAA4B;UAElC,EAAA,UAAA,EAAQ,IAAG,MAAK,IAAG,MAAK,GAAE,MAAK,MAAK,UAAS,CAAA;QAAG;QAGpD,EAAA,OAAA,EAAK,KAAK,UAAU,OAAM,cAAa,OAAM,SAAQ,CAAA;MAAG;IACpD;IAEP,aAAa,UAAU,SAAS,KAC/B,EAAA,OAAA,EAAK,OAAM,iCAAgC,GACxC,UAAU,IAAI,CAAC,QAAQ,MACtB;MAAA;MAAA,EACE,OAAO,KACL,uCACA,OAAO,SAAS,4CAA4C,GAE9D,SAAS,OAAO,SAChB,KAAK,EAAC;MAEN;QAAA;QAAA,EACE,OAAO,OAAO,UACd,QAAQ,OAAO,WACf,SAAQ,aACR,MAAK,QACL,OAAM,6BAA4B;QAElC,EAAA,QAAA,EAAA,aACa,OAAO,iBAAe,aACtB,OAAO,iBAClB,GAAG,OAAO,MACV,MAAK,UAAS,CAAA;MACd;MAEJ,EAAA,QAAA,EACE,OAAO,KACL,4CACA,OAAO,SAAS,iDAAiD,EAClE,GAEA,OAAO,IAAI;IACP,CAEV,CAAC;EAEL;AAGP;;;AEnLM,IAAO,oBAAP,MAAwB;EAI5B,cAAA;AAFQ,SAAA,WAAW;AAGjB,SAAK,WAAW,IAAI,SAAQ;EAC9B;EAEA,SAAM;AACJ,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,MAAM,4CAA4C;IAC9D;AACA,UAAM,KAAK,SAAS;AACpB,UAAM,YAAY,SAAS,cAAc,KAAK;AAC9C,cAAU,YAAY;AACtB,OAAG,YAAY,SAAS;AAExB,SAAK,SAAS,OAAO,SAAS;AAC9B,SAAK,WAAW;AAEhB,mBAAc;EAChB;EAEA,eAAe,SAId;AACC,QAAI;AACJ,QAAI,QAAQ,sBAAsB;AAChC,sBAAgB;QACd,YAAY;QACZ,SAAS;QACT,WAAW;UACT;YACE,OAAO;YACP,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,iBAAiB;YACjB,iBAAiB;YACjB,SAAS,QAAQ;;;;IAIzB,OAAO;AACL,sBAAgB;QACd,SAAS;QACT,WAAW;UACT;YACE,OAAO;YACP,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,iBAAiB;YACjB,iBAAiB;YACjB,SAAS,QAAQ;;UAEnB;YACE,OAAO;YACP,MAAM;YACN,UAAU;YACV,WAAW;YACX,MAAM;YACN,iBAAiB;YACjB,iBAAiB;YACjB,SAAS,QAAQ;;;;IAIzB;AAEA,WAAO,KAAK,SAAS,YAAY,aAAa;EAChD;;;;AC9EF;;;ACDA,IAAA,6BAAgB,uBAAM,glCAA+kC;;;ADc/lC,IAAO,iBAAP,MAAqB;EAIzB,cAAA;AAFQ,SAAA,OAAuB;AAG7B,SAAK,WAAW,WAAU;EAC5B;EAEO,SAAM;AACX,UAAM,KAAK,SAAS;AACpB,SAAK,OAAO,SAAS,cAAc,KAAK;AACxC,SAAK,KAAK,YAAY;AACtB,OAAG,YAAY,KAAK,IAAI;AACxB,mBAAc;EAChB;EAEO,QAAQ,OAA0B;AACvC,SAAK,OAAO,KAAK;EACnB;EAEO,QAAK;AACV,SAAK,OAAO,IAAI;EAClB;EAEQ,OAAO,OAAiC;AAC9C,QAAI,CAAC,KAAK;AAAM;AAChB,MAAO,MAAM,KAAK,IAAI;AAEtB,QAAI,CAAC;AAAO;AACZ,MACE,EAAC,uBAAqB,OAAA,OAAA,CAAA,GAChB,OAAK,EACT,WAAW,MAAK;AACd,WAAK,MAAK;IACZ,GACA,UAAU,KAAK,SAAQ,CAAA,CAAA,GAEzB,KAAK,IAAI;EAEb;;AAGF,IAAM,wBAKF,CAAC,EAAE,OAAO,YAAY,UAAU,eAAe,UAAS,MAAM;AAChE,QAAM,QAAQ,WAAW,SAAS;AAElC,SACE;IAAC;IAAiB,EAAC,SAAkB;IACnC;MAAA;MAAA,EAAK,OAAM,0BAAyB;MAClC,EAAA,SAAA,MAAQ,0BAAG;MACX,EAAA,OAAA,EAAK,OAAM,oCAAmC,SAAS,UAAS,CAAA;MAChE;QAAA;QAAA,EAAK,OAAO,KAAK,+BAA+B,KAAK,EAAC;QACpD,EAAA,KAAA,MAAI,KAAK;QACT,EAAA,UAAA,EAAQ,SAAS,cAAa,GAAG,UAAU;MAAU;IACjD;EACF;AAGZ;;;AE5EO,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,iBAAiB;AACvB,IAAM,0BAA0B;;;ACEjC,IAAO,kBAAP,MAAsB;EAI1B,cAAA;AAFQ,SAAA,WAAW;AAGjB,SAAK,iBAAiB,IAAI,eAAc;EAC1C;EAEA,SAAM;AACJ,QAAI,KAAK,UAAU;AACjB,YAAM,IAAI,MAAM,4CAA4C;IAC9D;AACA,SAAK,eAAe,OAAM;AAC1B,SAAK,WAAW;EAClB;EAEQ,yBAAyB,eAAsB;AACrD,UAAM,MAAM,IAAI,IAAI,uBAAuB;AAE3C,QAAI,aAAa,OAAO,gBAAgB,YAAW,EAAG,IAAI;AAC1D,QAAI,eAAe;AACjB,UAAI,aAAa,OAAO,UAAU,aAAa;IACjD;AAEA,UAAM,YAAY,SAAS,cAAc,GAAG;AAC5C,cAAU,SAAS;AACnB,cAAU,OAAO,IAAI;AACrB,cAAU,MAAM;AAChB,cAAU,MAAK;EACjB;EAEA,2BAA2B,eAAsB;AAC/C,SAAK,eAAe,QAAQ;MAC1B,OAAO;MACP,YAAY;MACZ,eAAe,MAAK;AAClB,aAAK,yBAAyB,aAAa;MAC7C;KACD;AAED,eAAW,MAAK;AACd,WAAK,yBAAyB,aAAa;IAC7C,GAAG,EAAE;EACP;EAEA,eAAe,UAId;AAEC,WAAO,MAAK;AACV,WAAK,eAAe,MAAK;IAC3B;EACF;;;;AC5BI,IAAO,kBAAP,MAAO,iBAAe;EAmB1B,YAAY,SAAyC;AAV7C,SAAA,sBAAsB,EAAE,SAAS,IAAI,YAAY,GAAE;AAInD,SAAA,cAAc,YAAW;AA2CjC,SAAA,gBAAgB,CAAC,WAAmB;AAClC,WAAK,WAAW;AAChB,YAAM,kBAAkB,KAAK,QAAQ,QAAQ,2BAA2B;AAExE,UAAI,QAAQ;AAEV,aAAK,SAAS,SAAS;MACzB;AAEA,WAAK,uBAAuB;AAE5B,UAAI,iBAAiB;AACnB,cAAM,YAAY,gBAAgB,MAAM,GAAG;AAC3C,cAAM,4BAA4B,KAAK,QAAQ,QAAQ,qBAAqB,MAAM;AAClF,YAAI,UAAU,CAAC,MAAM,MAAM,CAAC,UAAU,KAAK,SAAS,UAAU,CAAC,2BAA2B;AACxF,eAAK,uBAAuB;QAC9B;MACF;IACF;AAEA,SAAA,kBAAkB,CAAC,KAAa,UAAiB;AAC/C,WAAK,QAAQ,QAAQ,KAAK,KAAK;IACjC;AAEA,SAAA,eAAe,CAAC,SAAiB,eAAsB;AACrD,UACE,KAAK,oBAAoB,YAAY,WACrC,KAAK,oBAAoB,eAAe,YACxC;AACA;MACF;AACA,WAAK,sBAAsB;QACzB;QACA;;AAGF,UAAI,KAAK,eAAe;AACtB,aAAK,cAAc,YAAY,OAAO,SAAS,SAAS,EAAE,CAAC;MAC7D;IACF;AAEA,SAAA,iBAAiB,CAAC,oBAA2B;AAC3C,UAAI,KAAK,kBAAkB;AACzB,aAAK,iBAAiB,CAAC,eAAe,CAAC;MACzC;AACA,UAAI,iBAAgB,0BAA0B,OAAO,GAAG;AAItD,cAAM,KAAK,iBAAgB,0BAA0B,OAAM,CAAE,EAAE,QAAQ,CAAC,OAAM;AAC5E,eAAK,eAAe,IAAI;YACtB,QAAQ;YACR,QAAQ,CAAC,eAAgC;WAC1C;QACH,CAAC;AACD,yBAAgB,0BAA0B,MAAK;MACjD;IACF;AA7FE,SAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AAEnD,SAAK,aAAa,QAAQ;AAC1B,SAAK,UAAU,QAAQ;AACvB,SAAK,WAAW,QAAQ;AACxB,SAAK,mBAAmB,QAAQ;AAChC,SAAK,gBAAgB,QAAQ;AAE7B,UAAM,EAAE,SAAS,IAAI,WAAU,IAAK,KAAK,UAAS;AAElD,SAAK,WAAW;AAChB,SAAK,aAAa;AAElB,SAAK,oBAAoB,IAAI,kBAAiB;AAE9C,SAAK,KAAK;AACV,SAAK,GAAG,OAAM;EAChB;EAEQ,YAAS;AACf,UAAM,UAAU,kBAAkB,KAAK,KAAK,OAAO,KAAK,kBAAkB,OAAO,KAAK,OAAO;AAE7F,UAAM,EAAE,WAAU,IAAK;AACvB,UAAM,aAAa,IAAI,qBAAqB;MAC1C;MACA;MACA,UAAU;KACX;AAED,UAAM,KAAK,KAAK,cAAc,IAAI,gBAAe,IAAK,IAAI,kBAAiB;AAE3E,eAAW,QAAO;AAElB,WAAO,EAAE,SAAS,IAAI,WAAU;EAClC;EA6DO,iBAAc;AACnB,SAAK,WACF,QAAO,EACP,KAAK,MAAK;AAST,YAAM,gBAAgB,kBAAkB,KAAK,KAAK,OAAO;AACzD,WAAI,kBAAa,QAAb,kBAAa,SAAA,SAAb,cAAe,QAAO,KAAK,SAAS,IAAI;AAC1C,2BAAmB,SAAQ;MAC7B;AAEA,eAAS,SAAS,OAAM;IAC1B,CAAC,EACA,MAAM,CAACE,OAAK;IAAE,CAAC;EACpB;EAEO,wBAAwB,QAAiC;AAC9D,WAAO,KAAK,YAAY;MACtB,QAAQ;MACR,QAAQ;QACN,aAAa,OAAO;QACpB,WAAW,OAAO;QAClB,UAAU,uBAAuB,OAAO,QAAQ;QAChD,MAAM,oBAAoB,OAAO,MAAM,IAAI;QAC3C,OAAO,OAAO;QACd,eAAe,OAAO,gBAAgB,uBAAuB,OAAO,aAAa,IAAI;QACrF,cAAc,OAAO,gBAAgB,uBAAuB,OAAO,aAAa,IAAI;QACpF,sBAAsB,OAAO,gBACzB,uBAAuB,OAAO,aAAa,IAC3C;QACJ,UAAU,OAAO,WAAW,uBAAuB,OAAO,QAAQ,IAAI;QACtE,SAAS,OAAO;QAChB,cAAc;;KAEjB;EACH;EAEO,iCAAiC,QAAiC;AACvE,WAAO,KAAK,YAAoE;MAC9E,QAAQ;MACR,QAAQ;QACN,aAAa,OAAO;QACpB,WAAW,OAAO;QAClB,UAAU,uBAAuB,OAAO,QAAQ;QAChD,MAAM,oBAAoB,OAAO,MAAM,IAAI;QAC3C,OAAO,OAAO;QACd,eAAe,OAAO,gBAAgB,uBAAuB,OAAO,aAAa,IAAI;QACrF,cAAc,OAAO,eAAe,uBAAuB,OAAO,YAAY,IAAI;QAClF,sBAAsB,OAAO,uBACzB,uBAAuB,OAAO,oBAAoB,IAClD;QACJ,UAAU,OAAO,WAAW,uBAAuB,OAAO,QAAQ,IAAI;QACtE,SAAS,OAAO;QAChB,cAAc;;KAEjB;EACH;EAEO,0BAA0B,mBAA2B,SAAe;AACzE,WAAO,KAAK,YAAY;MACtB,QAAQ;MACR,QAAQ;QACN,mBAAmB,oBAAoB,mBAAmB,IAAI;QAC9D;;KAEH;EACH;EAEO,uBAAoB;AACzB,WAAO,KAAK;EACd;EAEO,YAIL,SAAmC;AACnC,QAAI,mBAAwC;AAC5C,UAAM,KAAK,eAAe,CAAC;AAE3B,UAAM,SAAS,CAAC,UAAiB;AAC/B,WAAK,gCAAgC,EAAE;AACvC,WAAK,oBAAoB,IAAI,QAAQ,QAAQ,KAAK;AAClD,2BAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAgB;IAClB;AAEA,WAAO,IAAI,QAAkB,CAAC,SAAS,WAAU;AAC/C;AACE,2BAAmB,KAAK,GAAG,eAAe;UACxC,sBAAsB,KAAK;UAC3B,UAAU;UACV,mBAAmB,KAAK;;SACzB;MACH;AAEA,WAAK,kBAAkB,UAAU,IAAI,IAAI,CAAC,aAAY;AACpD,6BAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAgB;AAChB,YAAI,gBAAgB,QAAQ,GAAG;AAC7B,iBAAO,OAAO,IAAI,MAAM,SAAS,YAAY,CAAC;QAChD;AAEA,gBAAQ,QAAoB;MAC9B,CAAC;AAED,WAAK,wBAAwB,IAAI,OAAO;IAC1C,CAAC;EACH;EAEQ,wBAAwB,IAAY,SAAoB;AAC9D,UAAM,UAA+B,EAAE,MAAM,gBAAgB,IAAI,QAAO;AACxE,SAAK,aAAa,eAAe,SAAS,IAAI,EAC3C,KAAK,CAACA,OAAK;IAAE,CAAC,EACd,MAAM,CAAC,QAAO;AACb,WAAK,0BAA0B,QAAQ,IAAI;QACzC,QAAQ,QAAQ;QAChB,cAAc,IAAI;OACnB;IACH,CAAC;AAEH,QAAI,KAAK,aAAa;AACpB,WAAK,2BAA2B,QAAQ,MAAM;IAChD;EACF;;EAGQ,2BAA2B,QAAkB;AACnD,QAAI,EAAE,KAAK,cAAc;AAAkB;AAG3C,YAAQ,QAAQ;MACd,KAAK;MACL,KAAK;AACH;MACF;AACE,eAAO,iBACL,QACA,MAAK;AACH,iBAAO,iBACL,SACA,MAAK;AACH,iBAAK,WAAW,kBAAiB;UACnC,GACA,EAAE,MAAM,KAAI,CAAE;QAElB,GACA,EAAE,MAAM,KAAI,CAAE;AAEhB,aAAK,GAAG,2BAA0B;AAClC;IACJ;EACF;EAEQ,gCAAgC,IAAU;AAChD,UAAM,UAA+B;MACnC,MAAM;MACN;;AAEF,SAAK,aAAa,uBAAuB,SAAS,KAAK,EAAE,KAAI;EAC/D;EAEQ,aACN,OACA,SACA,aAAoB;AAEpB,WAAO,KAAK,WAAW,aAAa,OAAO,SAAS,WAAW;EACjE;EAEA,0BAA0B,IAAY,UAAsB;AAC1D,QAAI,SAAS,WAAW,2BAA2B;AACjD,uBAAgB,0BAA0B,QAAQ,CAACC,QAAO,KAAK,eAAeA,KAAI,QAAQ,CAAC;AAC3F,uBAAgB,0BAA0B,MAAK;AAC/C;IACF;AAEA,SAAK,eAAe,IAAI,QAAQ;EAClC;EAEQ,oBAAoB,IAAY,QAAoB,OAAa;;AACvE,UAAM,gBAAe,KAAA,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,aAAO,QAAA,OAAA,SAAA,KAAI;AACvC,SAAK,0BAA0B,IAAI;MACjC;MACA;KACD;EACH;EAEQ,eAAe,IAAY,UAAsB;AACvD,UAAM,WAAW,KAAK,kBAAkB,UAAU,IAAI,EAAE;AACxD,QAAI,UAAU;AACZ,eAAS,QAAQ;AACjB,WAAK,kBAAkB,UAAU,OAAO,EAAE;IAC5C;EACF;EAEO,0BAAuB;AAC5B,UAAM,EAAE,SAAS,WAAU,IAAK,KAAK;AACrC,UAAM,UAAuB;MAC3B,QAAQ;MACR,QAAQ;QACN;QACA;;;AAIJ,UAAM,mBAAwC;AAC9C,UAAM,KAAK,eAAe,CAAC;AAE3B,WAAO,IAAI,QAAiD,CAAC,SAAS,WAAU;AAC9E,WAAK,kBAAkB,UAAU,IAAI,IAAI,CAAC,aAAY;AAGpD,6BAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAgB;AAChB,YAAI,gBAAgB,QAAQ,GAAG;AAC7B,iBAAO,OAAO,IAAI,MAAM,SAAS,YAAY,CAAC;QAChD;AACA,gBAAQ,QAAmD;MAC7D,CAAC;AACD,uBAAgB,0BAA0B,IAAI,EAAE;AAChD,WAAK,wBAAwB,IAAI,OAAO;IAC1C,CAAC;EACH;EAEA,WACE,MACA,SACA,QACA,UACA,OACA,SAAgB;AAEhB,UAAM,UAAuB;MAC3B,QAAQ;MACR,QAAQ;QACN;QACA,SAAS;UACP;UACA;UACA;UACA;;QAEF;;;AAIJ,QAAI,mBAAwC;AAC5C,UAAM,KAAK,eAAe,CAAC;AAE3B,UAAM,SAAS,CAAC,UAAiB;AAC/B,WAAK,gCAAgC,EAAE;AACvC,WAAK,oBAAoB,IAAI,QAAQ,QAAQ,KAAK;AAClD,2BAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAgB;IAClB;AAEA;AACE,yBAAmB,KAAK,GAAG,eAAe;QACxC,sBAAsB,KAAK;QAC3B,UAAU;QACV,mBAAmB,KAAK;;OACzB;IACH;AAEA,WAAO,IAAI,QAAoC,CAAC,SAAS,WAAU;AACjE,WAAK,kBAAkB,UAAU,IAAI,IAAI,CAAC,aAAY;AACpD,6BAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAgB;AAEhB,YAAI,gBAAgB,QAAQ,GAAG;AAC7B,iBAAO,OAAO,IAAI,MAAM,SAAS,YAAY,CAAC;QAChD;AACA,gBAAQ,QAAsC;MAChD,CAAC;AAED,WAAK,wBAAwB,IAAI,OAAO;IAC1C,CAAC;EACH;EAEA,iBACE,SACA,SACA,UACA,mBACA,WACA,gBAIC;AAED,UAAM,UAAuB;MAC3B,QAAQ;MACR,QAAQ;QACN;QACA;QACA;QACA;QACA;QACA;;;AAIJ,QAAI,mBAAwC;AAC5C,UAAM,KAAK,eAAe,CAAC;AAE3B,UAAM,SAAS,CAAC,UAAiB;AAC/B,WAAK,gCAAgC,EAAE;AACvC,WAAK,oBAAoB,IAAI,QAAQ,QAAQ,KAAK;AAClD,2BAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAgB;IAClB;AAEA;AACE,yBAAmB,KAAK,GAAG,eAAe;QACxC,sBAAsB,KAAK;QAC3B,UAAU;QACV,mBAAmB,KAAK;;OACzB;IACH;AAEA,WAAO,IAAI,QAA0C,CAAC,SAAS,WAAU;AACvE,WAAK,kBAAkB,UAAU,IAAI,IAAI,CAAC,aAAY;AACpD,6BAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAgB;AAEhB,YAAI,gBAAgB,QAAQ,GAAG;AAC7B,iBAAO,OAAO,IAAI,MAAM,SAAS,YAAY,CAAC;QAChD;AACA,gBAAQ,QAA4C;MACtD,CAAC;AAED,WAAK,wBAAwB,IAAI,OAAO;IAC1C,CAAC;EACH;EAEA,oBACE,SACA,SAAgB;AAEhB,UAAM,UAAuB;MAC3B,QAAQ;MACR,QAAM,OAAA,OAAA,EACJ,QAAO,GACJ,EAAE,QAAO,CAAE;;AAIlB,QAAI,mBAAwC;AAC5C,UAAM,KAAK,eAAe,CAAC;AAE3B,UAAM,SAAS,CAAC,UAAiB;AAC/B,WAAK,gCAAgC,EAAE;AACvC,WAAK,oBAAoB,IAAI,QAAQ,QAAQ,KAAK;AAClD,2BAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAgB;IAClB;AAEA;AACE,yBAAmB,KAAK,GAAG,eAAe;QACxC,sBAAsB,KAAK;QAC3B,UAAU;QACV,mBAAmB,KAAK;;OACzB;IACH;AAEA,WAAO,IAAI,QAA6C,CAAC,SAAS,WAAU;AAC1E,WAAK,kBAAkB,UAAU,IAAI,IAAI,CAAC,aAAY;AACpD,6BAAgB,QAAhB,qBAAgB,SAAA,SAAhB,iBAAgB;AAChB,YAAI,gBAAgB,QAAQ,KAAK,SAAS,WAAW;AACnD,iBAAO,OACL,eAAe,SAAS,OAAO;YAC7B,MAAM,SAAS;YACf,SAAS;WACV,CAAC;QAEN,WAAW,gBAAgB,QAAQ,GAAG;AACpC,iBAAO,OAAO,IAAI,MAAM,SAAS,YAAY,CAAC;QAChD;AAEA,gBAAQ,QAA+C;MACzD,CAAC;AAED,WAAK,wBAAwB,IAAI,OAAO;IAC1C,CAAC;EACH;;AAlfe,gBAAA,4BAA4B,oBAAI,IAAG;;;A1BNpD,IAAM,uBAAuB;AAC7B,IAAM,uBAAuB;AAKvB,IAAO,mBAAP,MAAuB;EAO3B,YAAY,SAAoE;AALxE,SAAA,SAAiC;AAEjC,SAAA,aAA8B,CAAA;AAIpC,SAAK,WAAW,QAAQ;AACxB,SAAK,WAAW,IAAI,mBAAmB,cAAc,cAAc;AACnE,SAAK,WAAW,QAAQ,YAAY;AAEpC,UAAM,kBAAkB,KAAK,SAAS,QAAQ,2BAA2B;AACzE,QAAI,iBAAiB;AACnB,YAAM,YAAY,gBAAgB,MAAM,GAAG;AAC3C,UAAI,UAAU,CAAC,MAAM,IAAI;AACvB,aAAK,aAAa,UAAU,IAAI,CAAC,YAAY,oBAAoB,OAAO,CAAC;MAC3E;IACF;AAEA,SAAK,gBAAe;EACtB;EAEA,aAAU;AACR,UAAM,QAAQ,KAAK,gBAAe;AAClC,UAAM,EAAE,IAAI,OAAM,IAAK,MAAM,qBAAoB;AACjD,WAAO,EAAE,IAAI,OAAM;EACrB;EAEA,MAAM,YAAS;AACb,UAAM,KAAK,qBAAoB;EACjC;EAEA,IAAY,kBAAe;AACzB,WAAO,KAAK,WAAW,CAAC,KAAK;EAC/B;EAEA,IAAY,aAAU;;AACpB,YAAO,KAAA,KAAK,SAAS,QAAQ,oBAAoB,OAAC,QAAA,OAAA,SAAA,KAAI;EACxD;EAEA,IAAY,WAAW,OAAa;AAClC,SAAK,SAAS,QAAQ,sBAAsB,KAAK;EACnD;EAEQ,mBAAmB,YAAoB,SAAe;;AAC5D,SAAK,aAAa;AAGlB,UAAM,kBAAkB,KAAK,WAAU;AACvC,SAAK,SAAS,QAAQ,sBAAsB,QAAQ,SAAS,EAAE,CAAC;AAChE,UAAM,eAAe,gBAAgB,OAAO,MAAM;AAClD,QAAI,cAAc;AAChB,OAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,gBAAgB,oBAAoB,OAAO,CAAC;IAC9D;EACF;EAEQ,MAAM,WAAW,QAAoB;AAC3C,UAAM,UAAW,MAAM,QAAQ,MAAM,IAAI,OAAO,CAAC,IAAI;AASrD,QAAI,CAAC,QAAQ,MAAM;AACjB,YAAM,eAAe,IAAI,cAAc,kBAAkB;IAC3D;AAEA,SAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UAAS,SAAS;AAC7B,YAAM,eAAe,IAAI,cAAc,kBAAkB,QAAQ,IAAI,oBAAoB;IAC3F;AAEA,QAAI,EAAC,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,UAAS;AACrB,YAAM,eAAe,IAAI,cAAc,sBAAsB;IAC/D;AAEA,QAAI,EAAC,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,QAAQ,UAAS;AAC7B,YAAM,eAAe,IAAI,cAAc,qBAAqB;IAC9D;AAEA,UAAM,UAAU,KAAK,WAAU;AAC/B,UAAM,EAAE,SAAS,QAAQ,OAAO,SAAQ,IAAK,QAAQ;AAErD,UAAM,QAAQ,KAAK,gBAAe;AAClC,UAAM,SAAS,MAAM,MAAM,WACzB,QAAQ,MACR,SACA,QACA,UACA,OACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,SAAQ,CAAE;AAGrB,QAAI,gBAAgB,MAAM;AAAG,aAAO;AAEpC,WAAO,CAAC,CAAC,OAAO;EAClB;EAEQ,MAAM,iBAAiB,QAAoB;;AACjD,UAAM,UAAU,OAAO,CAAC;AAaxB,UAAI,KAAA,QAAQ,aAAO,QAAA,OAAA,SAAA,SAAA,GAAE,YAAW,GAAG;AACjC,YAAM,eAAe,IAAI,cAAc,kCAAkC;IAC3E;AAEA,QAAI,CAAC,QAAQ,aAAa,QAAQ,UAAU,KAAI,MAAO,IAAI;AACzD,YAAM,eAAe,IAAI,cAAc,+BAA+B;IACxE;AAEA,QAAI,CAAC,QAAQ,gBAAgB;AAC3B,YAAM,eAAe,IAAI,cAAc,oCAAoC;IAC7E;AAEA,UAAM,gBAAgB,OAAO,SAAS,QAAQ,SAAS,EAAE;AAEzD,QAAI,kBAAkB,KAAK,WAAU,GAAI;AACvC,aAAO;IACT;AAEA,UAAM,QAAQ,KAAK,gBAAe;AAElC,UAAM,EACJ,UAAU,CAAA,GACV,oBAAoB,CAAA,GACpB,WACA,WAAW,CAAA,GACX,eAAc,IACZ;AAEJ,UAAM,MAAM,MAAM,MAAM,iBACtB,cAAc,SAAQ,GACtB,SACA,UACA,mBACA,WACA,cAAc;AAGhB,QAAI,gBAAgB,GAAG;AAAG,aAAO;AAEjC,UAAI,KAAA,IAAI,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,gBAAe,MAAM;AACnC,WAAK,mBAAmB,QAAQ,CAAC,GAAG,aAAa;AACjD,aAAO;IACT;AACA,UAAM,eAAe,IAAI,SAAS,8BAA8B;EAClE;EAEQ,MAAM,oBAAoB,QAAoB;AACpD,UAAM,UAAU,OAAO,CAAC;AAGxB,UAAM,UAAU,OAAO,SAAS,QAAQ,SAAS,EAAE;AAEnD,UAAM,QAAQ,KAAK,gBAAe;AAClC,UAAM,MAAM,MAAM,MAAM,oBACtB,QAAQ,SAAS,EAAE,GACnB,KAAK,mBAAmB,MAAS;AAGnC,QAAI,gBAAgB,GAAG;AAAG,YAAM;AAEhC,UAAM,iBAAiB,IAAI;AAC3B,QAAI,eAAe,cAAc,eAAe,OAAO,SAAS,GAAG;AACjE,WAAK,mBAAmB,eAAe,QAAQ,OAAO;IACxD;AAEA,WAAO;EACT;EAEO,MAAM,UAAO;AAClB,SAAK,WAAW;AAChB,QAAI,KAAK,QAAQ;AACf,WAAK,OAAO,eAAc;IAC5B;AACA,SAAK,SAAS,MAAK;EACrB;EAEQ,cAAc,WAAqBC,IAAW;;AACpD,QAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,YAAM,IAAI,MAAM,2BAA2B;IAC7C;AAEA,UAAM,eAAe,UAAU,IAAI,CAAC,YAAY,oBAAoB,OAAO,CAAC;AAE5E,QAAI,KAAK,UAAU,YAAY,MAAM,KAAK,UAAU,KAAK,UAAU,GAAG;AACpE;IACF;AAEA,SAAK,aAAa;AAClB,KAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,mBAAmB,YAAY;AAC/C,SAAK,SAAS,QAAQ,6BAA6B,aAAa,KAAK,GAAG,CAAC;EAC3E;EAEA,MAAM,QAAQ,SAAyB;AACrC,UAAM,SAAU,QAAQ,UAA2B,CAAA;AAEnD,YAAQ,QAAQ,QAAQ;MACtB,KAAK;AACH,eAAO,CAAC,GAAG,KAAK,UAAU;MAC5B,KAAK;AACH,eAAO,KAAK,mBAAmB;MACjC,KAAK;AACH,eAAO,KAAK,WAAU,EAAG,SAAS,EAAE;MACtC,KAAK;AACH,eAAO,oBAAoB,KAAK,WAAU,CAAE;MAE9C,KAAK;AACH,eAAO,KAAK,qBAAoB;MAElC,KAAK;MACL,KAAK;AACH,eAAO,KAAK,UAAU,OAAO;MAE/B,KAAK;AACH,eAAO,KAAK,aAAa,OAAO;MAElC,KAAK;AACH,eAAO,KAAK,qBAAqB,MAAM;MAEzC,KAAK;AACH,eAAO,KAAK,wBAAwB,MAAM;MAE5C,KAAK;AACH,eAAO,KAAK,qBAAqB,MAAM;MAEzC,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;AACH,eAAO,KAAK,cAAc,OAAO;MAEnC,KAAK;AACH,eAAO,KAAK,iBAAiB,MAAM;MAErC,KAAK;AACH,eAAO,KAAK,oBAAoB,MAAM;MAExC,KAAK;AACH,eAAO,KAAK,WAAW,MAAM;MAE/B;AACE,YAAI,CAAC,KAAK;AAAY,gBAAM,eAAe,IAAI,SAAS,0BAA0B;AAClF,eAAO,gBAAgB,SAAS,KAAK,UAAU;IACnD;EACF;EAEQ,oBAAoB,eAAqB;AAC/C,UAAM,aAAa,oBAAoB,aAAa;AACpD,UAAM,qBAAqB,KAAK,WAAW,IAAI,CAAC,YAAY,oBAAoB,OAAO,CAAC;AACxF,QAAI,CAAC,mBAAmB,SAAS,UAAU,GAAG;AAC5C,YAAM,IAAI,MAAM,0BAA0B;IAC5C;EACF;EAEQ,0BAA0B,IAWjC;AACC,UAAM,cAAc,GAAG,OAAO,oBAAoB,GAAG,IAAI,IAAI,KAAK;AAClE,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,MAAM,iCAAiC;IACnD;AAEA,SAAK,oBAAoB,WAAW;AAEpC,UAAM,YAAY,GAAG,KAAK,oBAAoB,GAAG,EAAE,IAAI;AACvD,UAAM,WAAW,GAAG,SAAS,OAAO,aAAa,GAAG,KAAK,IAAI,OAAO,CAAC;AACrE,UAAM,OAAO,GAAG,OAAO,aAAa,GAAG,IAAI,IAAI,OAAO,MAAM,CAAC;AAC7D,UAAM,QAAQ,GAAG,SAAS,OAAO,gBAAgB,GAAG,KAAK,IAAI;AAC7D,UAAM,gBAAgB,GAAG,YAAY,OAAO,aAAa,GAAG,QAAQ,IAAI;AACxE,UAAM,eAAe,GAAG,gBAAgB,OAAO,aAAa,GAAG,YAAY,IAAI;AAC/E,UAAM,uBACJ,GAAG,wBAAwB,OAAO,aAAa,GAAG,oBAAoB,IAAI;AAC5E,UAAM,WAAW,GAAG,OAAO,OAAO,aAAa,GAAG,GAAG,IAAI;AACzD,UAAM,UAAU,GAAG,UAAU,gBAAgB,GAAG,OAAO,IAAI,KAAK,WAAU;AAE1E,WAAO;MACL;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;EAEJ;EAEQ,MAAM,UAAU,SAAyB;AAC/C,UAAM,EAAE,QAAQ,OAAM,IAAK;AAC3B,QAAI,CAAC,MAAM,QAAQ,MAAM;AAAG,YAAM,eAAe,IAAI,cAAa;AAElE,UAAM,QAAQ,KAAK,gBAAe;AAClC,UAAM,MAAM,MAAM,MAAM,YAAY;MAClC,QAAQ;MACR,QAAQ;QACN,SAAS,kBAAkB,OAAO,CAAC,CAAC;QACpC,WAAW,kBAAkB,OAAO,CAAC,CAAC;QACtC,WAAW,WAAW;;KAEzB;AACD,QAAI,gBAAgB,GAAG;AAAG,YAAM;AAChC,WAAO,IAAI;EACb;EAEQ,aAAU;;AAChB,WAAO,OAAO,UAAS,KAAA,KAAK,SAAS,QAAQ,oBAAoB,OAAC,QAAA,OAAA,SAAA,KAAI,KAAK,EAAE;EAC/E;EAEQ,MAAM,uBAAoB;;AAChC,QAAI,KAAK,WAAW,SAAS,GAAG;AAC9B,OAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,WAAW,EAAE,SAAS,oBAAoB,KAAK,WAAU,CAAE,EAAC,CAAE;AAC9E,aAAO,KAAK;IACd;AAEA,UAAM,QAAQ,KAAK,gBAAe;AAClC,UAAM,MAAM,MAAM,MAAM,wBAAuB;AAC/C,QAAI,gBAAgB,GAAG;AAAG,YAAM;AAEhC,QAAI,CAAC,IAAI,QAAQ;AACf,YAAM,IAAI,MAAM,4BAA4B;IAC9C;AAEA,SAAK,cAAc,IAAI,MAAM;AAC7B,KAAA,KAAA,KAAK,cAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,MAAG,WAAW,EAAE,SAAS,oBAAoB,KAAK,WAAU,CAAE,EAAC,CAAE;AAC9E,WAAO,KAAK;EACd;EAEQ,MAAM,aAAa,EAAE,OAAM,GAAoB;AACrD,QAAI,CAAC,MAAM,QAAQ,MAAM;AAAG,YAAM,eAAe,IAAI,cAAa;AAElE,UAAM,UAAU,OAAO,CAAC;AACxB,UAAM,UAAU,OAAO,CAAC;AACxB,SAAK,oBAAoB,OAAO;AAEhC,UAAM,QAAQ,KAAK,gBAAe;AAClC,UAAM,MAAM,MAAM,MAAM,YAAY;MAClC,QAAQ;MACR,QAAQ;QACN,SAAS,oBAAoB,OAAO;QACpC,SAAS,kBAAkB,OAAO;QAClC,WAAW;QACX,eAAe;;KAElB;AAED,QAAI,gBAAgB,GAAG;AAAG,YAAM;AAChC,WAAO,IAAI;EACb;EAEQ,MAAM,qBAAqB,QAAoB;AACrD,UAAM,KAAK,KAAK,0BAA0B,OAAO,CAAC,KAAK,CAAA,CAAE;AAEzD,UAAM,QAAQ,KAAK,gBAAe;AAClC,UAAM,MAAM,MAAM,MAAM,wBAAwB,EAAE;AAClD,QAAI,gBAAgB,GAAG;AAAG,YAAM;AAChC,WAAO,IAAI;EACb;EAEQ,MAAM,wBAAwB,QAAoB;AACxD,UAAM,oBAAoB,aAAa,OAAO,CAAC,CAAC;AAChD,UAAM,QAAQ,KAAK,gBAAe;AAClC,UAAM,MAAM,MAAM,MAAM,0BAA0B,mBAAmB,KAAK,WAAU,CAAE;AACtF,QAAI,gBAAgB,GAAG;AAAG,YAAM;AAChC,WAAO,IAAI;EACb;EAEQ,MAAM,qBAAqB,QAAoB;AACrD,UAAM,KAAK,KAAK,0BAA0B,OAAO,CAAC,KAAK,CAAA,CAAE;AAEzD,UAAM,QAAQ,KAAK,gBAAe;AAClC,UAAM,MAAM,MAAM,MAAM,iCAAiC,EAAE;AAC3D,QAAI,gBAAgB,GAAG;AAAG,YAAM;AAChC,WAAO,IAAI;EACb;EAEQ,MAAM,cAAc,SAAyB;AACnD,UAAM,EAAE,QAAQ,OAAM,IAAK;AAC3B,QAAI,CAAC,MAAM,QAAQ,MAAM;AAAG,YAAM,eAAe,IAAI,cAAa;AAElE,UAAM,SAAS,CAAC,UAAiB;AAC/B,YAAM,cAAc;QAClB,sBAAsB,uBAAAC,QAAO;QAC7B,sBAAsB,uBAAAA,QAAO;QAC7B,sBAAsB,uBAAAA,QAAO;QAC7B,mBAAmB,uBAAAA,QAAO;;AAE5B,aAAO,oBACL,YAAY,MAAkC,EAAE;QAC9C,MAAM,uBAAuB,KAAK;OACnC,GACD,IAAI;IAER;AAEA,UAAM,UAAU,OAAO,WAAW,yBAAyB,IAAI,CAAC;AAChE,UAAM,UAAU,OAAO,WAAW,yBAAyB,IAAI,CAAC;AAChE,SAAK,oBAAoB,OAAO;AAEhC,UAAM,QAAQ,KAAK,gBAAe;AAClC,UAAM,MAAM,MAAM,MAAM,YAAY;MAClC,QAAQ;MACR,QAAQ;QACN,SAAS,oBAAoB,OAAO;QACpC,SAAS,OAAO,OAAO;QACvB,eAAe,KAAK,UAAU,SAAS,MAAM,CAAC;QAC9C,WAAW;;KAEd;AAED,QAAI,gBAAgB,GAAG;AAAG,YAAM;AAChC,WAAO,IAAI;EACb;EAEQ,kBAAe;AACrB,QAAI,CAAC,KAAK,QAAQ;AAChB,WAAK,SAAS,IAAI,gBAAgB;QAChC,YAAY;QACZ,SAAS,KAAK;QACd,UAAU,KAAK;QACf,kBAAkB,KAAK,cAAc,KAAK,IAAI;QAC9C,eAAe,KAAK,mBAAmB,KAAK,IAAI;OACjD;IACH;AACA,WAAO,KAAK;EACd;;;;A2BndF,IAAM,kBAAkB;AACxB,IAAM,UAAU,IAAI,mBAAmB,UAAU,oBAAoB;AAE/D,SAAU,iBAAc;AAC5B,SAAO,QAAQ,QAAQ,eAAe;AACxC;AAEM,SAAU,gBAAgB,YAAsB;AACpD,UAAQ,QAAQ,iBAAiB,UAAU;AAC7C;AAEA,eAAsB,gBAAgB,QAMrC;AACC,QAAM,EAAE,cAAc,UAAU,kBAAkB,SAAQ,IAAK;AAC/D,oCAAkC,cAAc,UAAU,QAAQ,EAAE,MAAM,MAAK;EAAE,CAAC;AAElF,QAAM,UAA6C;IACjD,IAAI,OAAO,WAAU;IACrB,OAAO;IACP,MAAI,OAAA,OAAA,OAAA,OAAA,CAAA,GACC,OAAO,UAAU,GAAA,EACpB,iBAAgB,CAAA;;AAGpB,QAAM,EAAE,KAAI,IAAK,MAAM,aAAa,8BAA8B,OAAO;AACzE,SAAO;AACT;AAEM,SAAU,aAAa,QAK5B;AACC,QAAM,EAAE,YAAY,UAAU,cAAc,SAAQ,IAAK;AACzD,UAAQ,YAAY;IAClB,KAAK,OAAO;AACV,aAAO,IAAI,UAAU;QACnB;QACA;QACA;OACD;IACH;IACA,KAAK,cAAc;AACjB,aAAO,IAAI,iBAAiB;QAC1B;QACA;OACD;IACH;EACF;AACF;AAEA,eAAe,kCACb,cACA,UACA,UAA+B;AAE/B,QAAM,aAAa,UAAyB,CAAC,EAAE,MAAK,MAAO,UAAU,0BAA0B;AAI/F,QAAM,aAAa,IAAI,iBAAiB;IACtC;IACA;GACD;AAGD,eAAa,YAAY;IACvB,OAAO;IACP,MAAM,EAAE,SAAS,WAAW,WAAU,EAAE;GACxB;AAGlB,QAAM,WAAW,UAAS;AAG1B,eAAa,YAAY;IACvB,OAAO;IACP,MAAM,EAAE,WAAW,KAAI;GACP;AACpB;;;ACnGA,IAAM,qBAAqB;;;AAmB3B,IAAM,oBAAoB,MAAK;AAC7B,MAAI;AAEJ,SAAO;IACL,4BAA4B,MAAK;AAC/B,UAAI,4BAA4B,QAAW;AACzC,eAAO;MACT;AAEA,aAAO;IACT;IACA,8BAA8B,YAAW;AACvC,UAAI,OAAO,WAAW,aAAa;AAEjC,kCAA0B;AAC1B;MACF;AAEA,UAAI;AACF,cAAM,MAAM,GAAG,OAAO,SAAS,MAAM,GAAG,OAAO,SAAS,QAAQ;AAChE,cAAM,WAAW,MAAM,MAAM,KAAK;UAChC,QAAQ;SACT;AAED,YAAI,CAAC,SAAS,IAAI;AAChB,gBAAM,IAAI,MAAM,uBAAuB,SAAS,MAAM,EAAE;QAC1D;AAEA,cAAM,SAAS,SAAS,QAAQ,IAAI,4BAA4B;AAChE,kCAA0B,WAAM,QAAN,WAAM,SAAN,SAAU;AAEpC,YAAI,4BAA4B,eAAe;AAC7C,kBAAQ,MAAM,kBAAkB;QAClC;MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,8CAA+C,MAAgB,OAAO;AACpF,kCAA0B;MAC5B;IACF;;AAEJ;AAEO,IAAM,EAAE,8BAA8B,2BAA0B,IAAK,kBAAiB;;;ACzD7F,IAAM,cAAc;AACpB,IAAM,eAAe;AAIf,SAAU,UAAU,KAAQ;AAChC,QAAM,QAAQ,OAAO,aAAa,eAAe,IAAI,OAAO;AAC5D,QAAM,OAAO,OAAO,cAAc,gBAAgB,IAAI,OAAO;AAC7D,2BAAyB,GAAG;AAE5B,QAAM,UAAU,UAAU,OAAO,WAAU,CAAE;AAC7C,QAAM,QAAQ,OAAO,KACnB,KACA,SACA,SAAS,WAAW,YAAY,YAAY,UAAU,IAAI,SAAS,GAAG,EAAE;AAG1E,YAAK,QAAL,UAAK,SAAA,SAAL,MAAO,MAAK;AAEZ,MAAI,CAAC,OAAO;AACV,UAAM,eAAe,IAAI,SAAS,8BAA8B;EAClE;AAEA,SAAO;AACT;AAEM,SAAU,WAAW,OAAoB;AAC7C,MAAI,SAAS,CAAC,MAAM,QAAQ;AAC1B,UAAM,MAAK;EACb;AACF;AAEA,SAAS,yBAAyB,KAAQ;AACxC,QAAM,SAAS;IACb,SAAS;IACT,YAAY;IACZ,QAAQ,OAAO,SAAS;IACxB,MAAM,2BAA0B;;AAGlC,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACjD,QAAI,aAAa,OAAO,KAAK,MAAM,SAAQ,CAAE;EAC/C;AACF;;;ACxBM,IAAO,eAAP,MAAmB;EAOvB,YAAY,EAAE,MAAM,aAAa,UAAU,WAAU,GAAuB;AAHpE,SAAA,QAAuB;AACvB,SAAA,YAAY,oBAAI,IAAG;AAW3B,SAAA,cAAc,OAAO,YAAoB;AACvC,YAAM,QAAQ,MAAM,KAAK,mBAAkB;AAC3C,YAAM,YAAY,SAAS,KAAK,IAAI,MAAM;IAC5C;AAKA,SAAA,gCAAgC,OAC9B,YACc;AACd,YAAM,kBAAkB,KAAK,UAAa,CAAC,EAAE,UAAS,MAAO,cAAc,QAAQ,EAAE;AACrF,WAAK,YAAY,OAAO;AACxB,aAAO,MAAM;IACf;AAKA,SAAA,YAAY,OAA0B,cAAqD;AACzF,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,cAAM,WAAW,CAAC,UAA0B;AAC1C,cAAI,MAAM,WAAW,KAAK,IAAI;AAAQ;AAEtC,gBAAM,UAAU,MAAM;AACtB,cAAI,UAAU,OAAO,GAAG;AACtB,oBAAQ,OAAO;AACf,mBAAO,oBAAoB,WAAW,QAAQ;AAC9C,iBAAK,UAAU,OAAO,QAAQ;UAChC;QACF;AAEA,eAAO,iBAAiB,WAAW,QAAQ;AAC3C,aAAK,UAAU,IAAI,UAAU,EAAE,OAAM,CAAE;MACzC,CAAC;IACH;AAKQ,SAAA,aAAa,MAAK;AAExB,iBAAW,KAAK,KAAK;AACrB,WAAK,QAAQ;AAEb,WAAK,UAAU,QAAQ,CAAC,EAAE,OAAM,GAAI,aAAY;AAC9C,eAAO,eAAe,SAAS,oBAAoB,kBAAkB,CAAC;AACtE,eAAO,oBAAoB,WAAW,QAAQ;MAChD,CAAC;AACD,WAAK,UAAU,MAAK;IACtB;AAKA,SAAA,qBAAqB,YAA4B;AAC/C,UAAI,KAAK,SAAS,CAAC,KAAK,MAAM,QAAQ;AAEpC,aAAK,MAAM,MAAK;AAChB,eAAO,KAAK;MACd;AAEA,WAAK,QAAQ,UAAU,KAAK,GAAG;AAE/B,WAAK,UAAyB,CAAC,EAAE,MAAK,MAAO,UAAU,aAAa,EACjE,KAAK,KAAK,UAAU,EACpB,MAAM,MAAK;MAAE,CAAC;AAEjB,aAAO,KAAK,UAAyB,CAAC,EAAE,MAAK,MAAO,UAAU,aAAa,EACxE,KAAK,CAAC,YAAW;AAChB,aAAK,YAAY;UACf,WAAW,QAAQ;UACnB,MAAM;YACJ,SAAS;YACT,UAAU,KAAK;YACf,YAAY,KAAK;YACjB,UAAU,OAAO,SAAS,SAAQ;;SAErC;MACH,CAAC,EACA,KAAK,MAAK;AACT,YAAI,CAAC,KAAK;AAAO,gBAAM,eAAe,IAAI,SAAQ;AAClD,eAAO,KAAK;MACd,CAAC;IACL;AA5FE,SAAK,MAAM,IAAI,IAAI,GAAG;AACtB,SAAK,WAAW;AAChB,SAAK,aAAa;EACpB;;;;ACvBI,SAAU,eAAe,OAAc;AAC3C,QAAM,aAAa,UAAU,eAAe,KAAK,GAAG;IAClD,oBAAoB;GACrB;AAED,QAAM,SAAS,IAAI,IAAI,wDAAwD;AAC/E,SAAO,aAAa,IAAI,WAAW,OAAO;AAC1C,SAAO,aAAa,IAAI,QAAQ,WAAW,KAAK,SAAQ,CAAE;AAC1D,SAAO,aAAa,IAAI,WAAW,WAAW,OAAO;AAErD,SAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,UAAU,GAAA,EACb,QAAQ,OAAO,KAAI,CAAA;AAEvB;AAKA,SAAS,eAAe,OAAsC;;AAC5D,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;MACL,SAAS;MACT,MAAM,mBAAmB,IAAI;;EAEjC,WAAW,gBAAgB,KAAK,GAAG;AACjC,UAAM,UAAU,MAAM;AACtB,UAAM,QACJ,KAAA,MAAM,eAAS,QAAA,OAAA,SAAA,KACd,QAAQ,MAAM,oBAAoB,IAC/B,mBAAmB,SAAS,sBAC5B;AAEN,WAAA,OAAA,OAAA,OAAA,OAAA,CAAA,GACK,KAAK,GAAA;MACR;MACA;MACA,MAAM,EAAE,QAAQ,MAAM,OAAM;IAAE,CAAA;EAElC;AACA,SAAO;AACT;;;AC5BM,IAAO,uBAAP,cAAoC,aAAAC,QAAoC;;;;;;;;;;;;;;;ACJxE,IAAO,yBAAP,cAAsC,qBAAoB;EAO9D,YAAY,IAAkF;QAAlF,EAAE,SAAQ,IAAA,IAAE,KAAA,GAAA,YAAA,EAAc,QAAO,IAAA,IAAK,aAAU,OAAA,IAAxB,CAAA,SAAA,CAA0B;AAC5D,UAAK;AAHC,SAAA,SAAwB;AA4EvB,SAAA,mBAAmB;AAxE1B,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,eAAe,IAAI,aAAa;MACnC,KAAK;MACL;MACA;KACD;AAED,UAAM,aAAa,eAAc;AACjC,QAAI,YAAY;AACd,WAAK,SAAS,KAAK,WAAW,UAAU;IAC1C;EACF;EAEO,MAAM,QAAW,MAAsB;AAC5C,QAAI;AACF,sCAAgC,IAAI;AACpC,UAAI,CAAC,KAAK,QAAQ;AAChB,gBAAQ,KAAK,QAAQ;UACnB,KAAK,uBAAuB;AAC1B,kBAAM,aAAa,MAAM,KAAK,uBAAuB,IAAI;AACzD,kBAAM,SAAS,KAAK,WAAW,UAAU;AACzC,kBAAM,OAAO,UAAU,IAAI;AAC3B,iBAAK,SAAS;AACd,4BAAgB,UAAU;AAC1B;UACF;UACA,KAAK,oBAAoB;AACvB,kBAAM,kBAAkB,KAAK,WAAW,KAAK;AAC7C,kBAAM,gBAAgB,UAAU,EAAE,QAAQ,YAAW,CAAE;AACvD,kBAAM,SAAS,MAAM,gBAAgB,QAAQ,IAAI;AACjD,kBAAM,gBAAgB,QAAO;AAC7B,mBAAO;UACT;UACA,KAAK;AACH,mBAAO,gBAAgB,MAAM,iBAAiB;UAChD,KAAK;AACH,mBAAO;UACT,KAAK;AACH,mBAAO,oBAAoB,CAAC;UAC9B,SAAS;AACP,kBAAM,eAAe,SAAS,aAC5B,sDAAsD;UAE1D;QACF;MACF;AACA,aAAO,MAAM,KAAK,OAAO,QAAQ,IAAI;IACvC,SAAS,OAAO;AACd,YAAM,EAAE,KAAI,IAAK;AACjB,UAAI,SAAS,mBAAmB,SAAS;AAAc,aAAK,WAAU;AACtE,aAAO,QAAQ,OAAO,eAAe,KAAK,CAAC;IAC7C;EACF;;EAGO,MAAM,SAAM;AACjB,YAAQ,KACN,gGAAgG;AAElG,WAAO,MAAM,KAAK,QAAQ;MACxB,QAAQ;KACT;EACH;EAEA,MAAM,aAAU;;AACd,YAAM,KAAA,KAAK,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,QAAO;AAC1B,SAAK,SAAS;AACd,uBAAmB,SAAQ;AAC3B,SAAK,KAAK,cAAc,eAAe,SAAS,aAAa,8BAA8B,CAAC;EAC9F;EAIQ,uBAAuB,kBAAkC;AAC/D,WAAO,gBAAgB;MACrB,cAAc,KAAK;MACnB,YAAY,KAAK;MACjB,UAAU,KAAK;MACf;MACA,UAAU,KAAK,KAAK,KAAK,IAAI;KAC9B;EACH;EAEQ,WAAW,YAAsB;AACvC,WAAO,aAAa;MAClB;MACA,UAAU,KAAK;MACf,cAAc,KAAK;MACnB,UAAU,KAAK,KAAK,KAAK,IAAI;KAC9B;EACH;;;;AClHI,SAAU,oBAAoB,YAAuB;AACzD,MAAI,CAAC,YAAY;AACf;EACF;AAEA,MAAI,CAAC,CAAC,OAAO,mBAAmB,SAAS,EAAE,SAAS,WAAW,OAAO,GAAG;AACvE,UAAM,IAAI,MAAM,oBAAoB,WAAW,OAAO,EAAE;EAC1D;AAEA,MAAI,WAAW,aAAa;AAC1B,QACE,WAAW,YAAY,SAAS,UAChC,WAAW,YAAY,eAAe,QACtC;AACA,YAAM,IAAI,MAAM,gEAAgE;IAClF;EACF;AACF;;;ACFM,IAAO,oBAAP,MAAwB;EAG5B,YAAY,UAA4C;AACtD,SAAK,WAAW;MACd,SAAS,SAAS,WAAW;MAC7B,YAAY,SAAS,cAAc,WAAU;MAC7C,aAAa,SAAS,eAAe,CAAA;;AAEvC,SAAK,mBAAkB;AACvB,SAAK,6BAA4B;EACnC;EAEO,iBAAiB,aAAyB,EAAE,SAAS,MAAK,GAAE;;AACjE,wBAAoB,UAAU;AAC9B,UAAM,SAAS,EAAE,UAAU,KAAK,UAAU,WAAU;AACpD,YAAO,KAAA,4BAA4B,MAAM,OAAC,QAAA,OAAA,SAAA,KAAI,IAAI,uBAAuB,MAAM;EACjF;;;;;;;EAQO,sBAAsB,MAAgB,QAAQ,KAAG;AACtD,WAAO,WAAW,MAAM,KAAK;EAC/B;EAEQ,qBAAkB;AACxB,UAAM,iBAAiB,IAAI,mBAAmB,QAAQ;AACtD,mBAAe,QAAQ,WAAW,OAAO;EAC3C;;;;AC5CI,SAAU,6BAA6B,SAA8B;;AACzE,QAAM,SAA6B;IACjC,UAAU,QAAQ;IAClB,YAAY,QAAQ;;AAEtB,UAAO,KAAA,4BAA4B,MAAM,OAAC,QAAA,OAAA,SAAA,KAAI,IAAI,uBAAuB,MAAM;AACjF;;;ACCA,IAAM,qBAAiC;EACrC,SAAS;;AAQL,SAAU,wBAAwB,QAAsC;;AAC5E,QAAM,iBAAiB,IAAI,mBAAmB,QAAQ;AACtD,iBAAe,QAAQ,WAAW,OAAO;AAEzC,OAAK,6BAA4B;AAEjC,QAAM,UAA8B;IAClC,UAAU;MACR,SAAS,OAAO,WAAW;MAC3B,YAAY,OAAO,cAAc;MACjC,aAAa,OAAO,eAAe,CAAA;;IAErC,YAAY,OAAO,OAAO,qBAAoB,KAAA,OAAO,gBAAU,QAAA,OAAA,SAAA,KAAI,CAAA,CAAE;;AAMvE,sBAAoB,QAAQ,UAAU;AAEtC,MAAI,WAAqC;AAEzC,SAAO;IACL,aAAa,MAAK;AAChB,UAAI,CAAC,UAAU;AACb,mBAAW,6BAA6B,OAAO;MACjD;AACA,aAAO;IACT;;AAEJ;;;ACrDA,IAAA,eAAe;", "names": ["add", "fromBig", "split", "U32_MASK64", "_32n", "shrSH", "shrSL", "rotrSH", "rotrSL", "rotrBH", "rotrBL", "add3L", "add3H", "add4L", "add4H", "add5L", "add5H", "isBytes", "abytes", "aexists", "aoutput", "clean", "createView", "rotr", "bytesToHex", "utf8ToBytes", "toBytes", "createHasher", "hasHexBuiltin", "hexes", "_", "Hash", "y", "isHexString", "type", "value", "_", "window", "chain", "ConnectionState", "_a", "_", "_", "crypto", "_", "isLE", "_32n", "E", "sha256", "storage", "sha256", "_", "id", "_", "eip712", "EventEmitter"]}