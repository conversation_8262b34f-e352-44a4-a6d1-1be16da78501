{"version": 3, "sources": ["../../isows/utils.ts", "../../isows/native.ts"], "sourcesContent": ["export function getNativeWebSocket() {\n  if (typeof WebSocket !== \"undefined\") return WebSocket;\n  if (typeof global.WebSocket !== \"undefined\") return global.WebSocket;\n  if (typeof window.WebSocket !== \"undefined\") return window.WebSocket;\n  if (typeof self.WebSocket !== \"undefined\") return self.WebSocket;\n  throw new Error(\"`WebSocket` is not supported in this environment\");\n}\n", "import { getNativeWebSocket } from \"./utils.js\";\n\nexport const WebSocket = getNativeWebSocket();\n\ntype MessageEvent_ = MessageEvent;\nexport type { MessageEvent_ as MessageEvent };\n"], "mappings": ";;;;;;AAAM,SAAU,qBAAkB;AAChC,MAAI,OAAO,cAAc;AAAa,WAAO;AAC7C,MAAI,OAAO,OAAO,cAAc;AAAa,WAAO,OAAO;AAC3D,MAAI,OAAO,OAAO,cAAc;AAAa,WAAO,OAAO;AAC3D,MAAI,OAAO,KAAK,cAAc;AAAa,WAAO,KAAK;AACvD,QAAM,IAAI,MAAM,kDAAkD;AACpE;AANA;;;;;;ACAA;;mBAAAA;;AAAA,IAEaA;AAFb;;;AAEO,IAAMA,aAAY,mBAAkB;;;", "names": ["WebSocket"]}