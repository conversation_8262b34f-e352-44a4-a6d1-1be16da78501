import { HardHat, Twitter, Instagram } from 'lucide-react';

const MaintenancePage = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-purple-800 via-purple-900 to-indigo-900 text-indigo-50 p-6">
      <HardHat className="w-28 h-28 mb-8 text-indigo-300 animate-pulse" />
      <h1 className="text-5xl font-extrabold mb-6 text-center tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-indigo-300 to-purple-300">
        STRAPT is Leveling Up!
      </h1>
      <p className="text-xl text-indigo-200 mb-10 text-center max-w-lg leading-relaxed">
        We're busy in the workshop, crafting some awesome new features and supercharging STRAPT for an even better experience. We'll be back online soon!
      </p>
      <div className="flex space-x-6 mb-12">
        <a
          href="https://twitter.com/StrapT" // Replace with actual Twitter link
          target="_blank"
          rel="noopener noreferrer"
          className="text-indigo-300 hover:text-indigo-100 transition-colors duration-300"
          aria-label="Follow STRAPT on Twitter"
        >
          <Twitter className="w-10 h-10" />
        </a>
        <a
          href="https://instagram.com/StrapT" // Replace with actual Instagram link
          target="_blank"
          rel="noopener noreferrer"
          className="text-indigo-300 hover:text-indigo-100 transition-colors duration-300"
          aria-label="Follow STRAPT on Instagram"
        >
          <Instagram className="w-10 h-10" />
        </a>
      </div>
      <p className="text-sm text-indigo-300">
        &copy; {new Date().getFullYear()} STRAPT - Building the Future of Financial Assistant
      </p>
    </div>
  );
};

export default MaintenancePage;